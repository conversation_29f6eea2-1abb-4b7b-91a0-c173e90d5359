<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet23" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="petIdle" />
		<walk defId="petWalk" />
		<run defId="petRun" />
		<attack defId="petAttack"  />
		<hurt defId="petHurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="petDie">
			<attackSourceData entityId="" skillId="" />
		</die>
	   
		<sound>
			
		</sound>
       <skill id="Skill_Pet22Skill" 
		       className="YJFY.Skill.PetSkills.Skill_Pet22Skill" 
			   x="-1200" 
			   y="-1200" 
			   z="-1" 
			   xRange="2400" 
			   yRange="2400" 
			   zRange="1200"   
			   hurtDuration="2000">
			<shakeView swfPath="NewGameFolder/PetSource/Pet21All.swf" className="ShakeView" />
		</skill>



		<animationDefinitions>
			<animationDefinition id="petIdle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet23All.swf"
					showClass="PetStand" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petWalk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet23All.swf"
					showClass="PetWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="petSkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet23All.swf"
					showClass="PetSkill" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petSkillEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet23All.swf"
					showClass="petSkillEffect_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
		</shows>

	</animal>
</data>
