<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet12" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet12Idle" />
		<walk defId="pet12Walk" />
		<run defId="pet12Run" />
		<attack defId="pet12Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet12Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet12Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	   <skill id="Skill_Pet12Skill" className="YJFY.Skill.PetSkills.Skill_Pet12Skill" x="0"
			y="-150" z="-1" xRange="600" yRange="300" zRange="100" bodyDefId="pet12SkillBodyShow"
			 disappearBodyId="pet12SkillBodyDisAppear" releaseSkillFrameLabel="releaseSkill^stop^"
			 skillAttackReachFrameLabel="skillAttackReach" replaceFrameLabel="replace" skillEndFrameLabel="skillEnd^stop^">
			
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet12Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet12All.swf"
					showClass="PetStand12_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet12Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet12All.swf"
					showClass="PetWalk12_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet12SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet12All.swf"
					showClass="PetSkill12AttackAnimation_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet12SkillBodyDisAppear" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet12All.swf"
					showClass="PetSkill12LinkDisapppear" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!-- 不化装-->
		    
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet12Idle" eqClassName="Pet_Lu_1"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetStand12_1"
				x_offset="0" y_offset="0" />
			<show defId="pet12Walk" eqClassName="Pet_Lu_1"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetWalk12_1"
				x_offset="0" y_offset="0" />
           <show defId="pet12SkillBodyShow" eqClassName="Pet_Lu_1"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetSkill12AttackAnimation_1"
				x_offset="0" y_offset="0" />
			<show defId="pet12SkillBodyDisAppear" eqClassName="Pet_Lu_1"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetSkill12LinkDisapppear"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet12Idle" eqClassName="Pet_Lu_2"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetStand12_2"
				x_offset="0" y_offset="0" />
			<show defId="pet12Walk" eqClassName="Pet_Lu_2"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetWalk12_2"
				x_offset="0" y_offset="0" />
           <show defId="pet12SkillBodyShow" eqClassName="Pet_Lu_2"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetSkill12AttackAnimation_2"
				x_offset="0" y_offset="0" />
			<show defId="pet12SkillBodyDisAppear" eqClassName="Pet_Lu_2"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetSkill12LinkDisapppear"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet12Idle" eqClassName="Pet_Lu_3"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetStand12_3"
				x_offset="0" y_offset="0" />
			<show defId="pet12Walk" eqClassName="Pet_Lu_3"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetWalk12_3"
				x_offset="0" y_offset="0" />
           <show defId="pet12SkillBodyShow" eqClassName="Pet_Lu_3"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetSkill12AttackAnimation_3"
				x_offset="0" y_offset="0" />
			<show defId="pet12SkillBodyDisAppear" eqClassName="Pet_Lu_3"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetSkill12LinkDisapppear"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
