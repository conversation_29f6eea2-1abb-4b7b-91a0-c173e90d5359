<?xml version="1.0" encoding="utf-8" ?>
<data id="Level30"
	swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/level.swf"
	className="LevelMap30" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
		swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf"
		className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
	<Waves totalWaveNum="21">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="0" duration="25000" num="10"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill" xmlPath="boss"
				startTime="500" duration="1000" num="0" />

		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="15000" duration="25000" num="10"
				isFallDown="1" />

		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="30000" duration="20000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="30000" duration="20000" num="5"
				isFallDown="0" />

		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="45000" duration="30000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="45000" duration="30000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="5" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="60000" duration="10000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="60000" duration="10000" num="5"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="75000" duration="5000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="75000" duration="5000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="7" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="90000" duration="10000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="90000" duration="10000" num="5"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="8" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="105000" duration="5000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="105000" duration="5000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="9" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="120000" duration="10000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="120000" duration="10000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="10" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="135000" duration="10000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="135000" duration="10000" num="5"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="11" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="150000" duration="10000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="150000" duration="10000" num="5"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="12" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="165000" duration="10000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="165000" duration="10000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="13" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="180000" duration="10000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="180000" duration="10000" num="5"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="14" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="195000" duration="10000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="195000" duration="10000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="15" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="210000" duration="5000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="210000" duration="5000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="16" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="225000" duration="10000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="225000" duration="10000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="17" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="240000" duration="10000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="240000" duration="10000" num="5"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="18" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="255000" duration="5000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="255000" duration="5000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="19" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="270000" duration="10000" num="5"
				isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="270000" duration="10000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill" xmlPath="boss"
				startTime="270000" duration="1000" num="1" />
		</Wave>

		<Wave waveCount="20" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="285000" duration="10000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="285000" duration="10000" num="5"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="21" totalEnemyNum="10" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddDefence"
				xmlPath="xiaoBing" startTime="300000" duration="10000" num="10"
				isFallDown="1" />
		
		</Wave>
	</Waves>
	<EqDrop>
		<xiaoBing noDropProWeight="100">
			<!--proWeight 概率权重 -->
			
			
				<!-- 暴击2级 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_CriticalRate2_S"
				proWeight="1" />
			<!-- 闪避2级 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Dodge2_S"
				proWeight="1" />
			
			
			<!-- 圣者之石 -->
			<item dropClassName="UI.Equipments.StackEquipments.Material_Shengzhezhishi_S"
				proWeight="1" />
			<!-- 凤凰蛋 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_FengHuang_S"
				proWeight="1" />
			<!-- 蓝宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp3_S"
				proWeight="2" />
			<!-- 人品宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp3_S"
				proWeight="2" />
			<!-- 攻击宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack3_S"
				proWeight="2" />
			<!-- 生命宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp3_S"
				proWeight="2" />
			<!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence3_S"
				proWeight="2" />
			<!-- 开孔灵符 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S"
				proWeight="2" />
			<!-- 碎石锤 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S"
				proWeight="1" />

			<!-- 红药 -->
			<item dropClassName="Item_HpUp" proWeight="50" />
			<!-- 蓝药 -->
			<item dropClassName="Item_MpUp" proWeight="50" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_10000" proWeight="25" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_50000" proWeight="5" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_100000" proWeight="2" />
			<!-- 灵兽石 -->
			<item dropClassName="Item_StrengthenNum_10" proWeight="5" />

			<!-- 圣灵精华 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S"
				proWeight="5" />
			<!-- 一级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S"
				proWeight="2" />
			<!-- 二级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_TwoFire_S"
				proWeight="2" />
			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="1" />
			<!-- 幸运宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S"
				proWeight="5" />
			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="2" />

		</xiaoBing>
		<boss noDropProWeight="150">
			<!--proWeight 概率权重 -->
			<dropNumData>
				<smallDropNumData proWeight="10">
					<numData num="1" proWeight="5" />
					<numData num="2" proweight="5" />
				</smallDropNumData>
				<bigDropNumData proWeight="1">
					<numData num="4" proWeight="8" />
					<numData num="5" proWeight="2" />
				</bigDropNumData>
			</dropNumData>
			
			
			
			
				<!-- 魔龙果模具 -->
			<item dropClassName="UI.Equipments.StackEquipments.Scroll_Molongguo_S"
				proWeight="2" />
				<!-- 幽冥宝珠（青） -->
			<item dropClassName="UI.Equipments.StackEquipments.Material_YouMing1_S"
				proWeight="2" />
				<!-- 重明鸟 -->	
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_ChongMing_S"
				proWeight="1" />	
            <!-- 开运灵石 -->
			<item dropClassName="UI.Equipments.StackEquipments.Potion_Kaiyunlingshi_S"
				proWeight="4" />
			<!-- 天王碎片 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_HongSe_S"
				proWeight="80" />
			<!-- 人品石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp4_S"
				proWeight="100" />

			<!-- 人品宝石模具 -->
			<item dropClassName="UI.Equipments.Scroll_InsetGem39_S"
				proWeight="60" />
			<!-- 凤凰蛋 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_FengHuang_S"
				proWeight="10" />
			

			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="2" />

			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="4" />


		</boss>

	</EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>

	</sharedAnimationDefinitions>

	<xiaoBing>
		<!--敌人数据 -->
		<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
			<animationDefinition id="xiaoBingFallDownShow1"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</fallDownEffect>
		<addDefence value="20" max="4500" addSpeed="55"/>
		<enemyData>

			<!-- totalHp=血量 attack=攻击 expOfDieThisEnemy=经验 defence=防御 dogdeRate=闪避 
				criticalRate=暴击率 criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中 -->
			<data att="totalHp" value="2500000" />
			<data att="attack" value="9500" />
			<data att="expOfDieThisEnemy" value="60000" />
			<data att="defence" value="4000" />
			<data att="dogdeRate" value="0.08" />
			<data att="criticalRate" value="0.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.7" />
			<data att="hitRate" value="0.09" />
		</enemyData>
		<!--移动速度以秒为单位 -->
		<animal id="enemy30" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="60" bodyHeight="120" walkSpeed="30"
			runSpeed="60">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->






			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
				zRange="100" />

			<idle defId="walk_enemy30" />
			<walk defId="walk_enemy30" />
			<run defId="walk_enemy30" />
			<attack defId="attack_enemy30" />
			<hurt defId="hurt_enemy30" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_enemy30" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow1" />

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>

				<animationDefinition id="walk_enemy30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
						showClass="Walk_Monster_30" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="hurt_enemy30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
						showClass="BeAttack_Monster_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
						showClass="Attack_Monster_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
						showClass="Dead_Monster_30" x_offset="0" y_offset="0" />
				</animationDefinition>



				<animationDefinition id="change" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="enemyFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="xiaoBingFallDownShow1"
					rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="FallDownEffect" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
			<shows>

				<show defId="walk_enemy30" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
					showClass="Walk_Monster_InSuper_30" x_offset="0" y_offset="0" />
				<show defId="hurt_enemy30" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
					showClass="BeAttack_Monster_InSuper_30" x_offset="0" y_offset="0" />
				<show defId="attack_enemy30" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
					showClass="Attack_Monster_InSuper_30" x_offset="0" y_offset="0" />
				<show defId="die_enemy30" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Enemy.swf"
					showClass="Dead_Monster_InSuper_30" x_offset="0" y_offset="0" />
			</shows>


		</animal>
	</xiaoBing>


	<boss>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="8000000" />
			<data att="attack" value="12000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="5000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>6</skillCd>
		<animal id="boss30" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="30"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="90" yRange="100"
				zRange="100" />

			<idle defId="idle_boss30" />
			<walk defId="walk_boss30" />
			<run defId="run_boss30" />
			<attack defId="attack_boss30" />
			<hurt defId="hurt_boss30" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss30" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			
			<skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao"
				x="-30" y="-40" z="-1" xRange="900" yRange="400" zRange="150"
				bodyDefId="skillAnimation" hurtDuration="3500"
				bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^"
				effectAddtoTargetId="tunshi" everyEntityAddShowIsFrontOfBody="0">
				<shakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf" className="ShakeView" />
				<animationDefinition id="tunshi" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf" 
						showClass="tunshi" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Walk_Boss_30" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Walk_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss29" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_29" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="BeAttack_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Attack_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Dead_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Skill_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="tunshi" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="tunshi" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</boss>
</data>
