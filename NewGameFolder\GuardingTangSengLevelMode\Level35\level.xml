<?xml version="1.0" encoding="utf-8" ?>
<data id="Level35"
	swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/level.swf"
	className="LevelMap35" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
		swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf"
		className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
	<Waves totalWaveNum="21">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="2000" duration="5000" num="6" isFallDown="0" />
		</Wave>
		<Wave waveCount="2" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="15000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="30000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="45000" duration="50000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="5" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="60000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="6" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="75000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="7" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="90000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="8" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="105000" duration="5000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill"
				xmlPath="boss" startTime="105000" duration="1000" num="0" />
		</Wave>
		<Wave waveCount="9" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="120000" duration="5000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="10" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="135000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="11" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="150000" duration="10000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="12" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="165000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="13" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="180000" duration="10000" num="6"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="14" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="195000" duration="10000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="15" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="210000" duration="5000" num="7"
				isFallDown="0" />
			
		</Wave>
		<Wave waveCount="16" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="225000" duration="10000" num="10"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill"
				xmlPath="boss" startTime="225000" duration="1000" num="1" />
		</Wave>
		<Wave waveCount="17" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="240000" duration="10000" num="5"
				isFallDown="0" />
			
		</Wave>
		<Wave waveCount="18" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="255000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="19" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="270000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>

		<Wave waveCount="20" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="285000" duration="10000" num="8"
				isFallDown="0" />
		
		</Wave>
		<Wave waveCount="21" totalEnemyNum="1" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing"
				xmlPath="xiaoBing" startTime="300000" duration="10000" num="10"
				isFallDown="0" />


		</Wave>
	</Waves>
	<EqDrop>
		<xiaoBing noDropProWeight="500">
			<!--proWeight 概率权重 -->

		
			<!-- 烛龙蛋 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_Zhulong_S"
				proWeight="1" />
			<!-- 黄金摘除 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZiPrecious_S"
				proWeight="1" />
			
			<!-- 魔龙-->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_Molong_S"
				proWeight="1" />
			<!-- 神域冥火 -->	
			<item dropClassName="UI.Equipments.StackEquipments.Material_Shenyuminghuo_S"
				proWeight="7" />
			<!-- 蓝宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp4_S"
				proWeight="12" />
			<!-- 人品宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp4_S"
				proWeight="10" />
			<!-- 攻击宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack4_S"
				proWeight="3" />
			<!-- 生命宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp3_S"
				proWeight="11" />
			
			<!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence3_S"
				proWeight="12" />
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence4_S"
				proWeight="2" />
			<!-- 开孔灵符 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S"
				proWeight="12" /> 
			<!-- 碎石锤 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S"
				proWeight="6" />

			<!-- 红药 -->
			<item dropClassName="Item_HpUp" proWeight="100" />
			<!-- 蓝药 -->
			<item dropClassName="Item_MpUp" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_10000" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_50000" proWeight="50" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_100000" proWeight="5" />
			<!-- 灵兽石 -->
			<item dropClassName="Item_StrengthenNum_10" proWeight="5" />

			<!-- 圣灵精华 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S"
				proWeight="15" />
			<!-- 一级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S"
				proWeight="13" />
			<!-- 二级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_TwoFire_S"
				proWeight="8" />
			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="8" />
			<!-- 幸运宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S"
				proWeight="18" />
			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="6" />


		</xiaoBing>
		<boss noDropProWeight="100">
			<!--proWeight 概率权重 -->
			<dropNumData>
				<smallDropNumData proWeight="10">
					<numData num="1" proWeight="5" />
					<numData num="2" proweight="5" />
				</smallDropNumData>
				<bigDropNumData proWeight="1">
					<numData num="4" proWeight="8" />
					<numData num="5" proWeight="2" />
				</bigDropNumData>
			</dropNumData>


			
				<!-- 神兽羽-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenShouYu_S"
				proWeight="2" />
			
		<!-- 魔龙-->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_Molong_S"
				proWeight="2" />
			<!-- 哪吒模具 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Scroll_Nezha3_S"
				proWeight="1" />
            <!-- 圣域冥火 -->
			<item dropClassName="UI.Equipments.StackEquipments.Material_Shenyuminghuo_S"
				proWeight="120" />
			<!-- 多闻碎片 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Duowen3_S"
				proWeight="1" />
			<!-- 攻击石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack5_S"
				proWeight="60" />
	      <!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence5_S"
				proWeight="50" />

			<!-- 凤凰蛋 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_FengHuang_S"
				proWeight="10" />
            	<!--生命宝石 -->
				<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp5_S"
				proWeight="40" />	

			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="20" />

			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="40" />


		</boss>

	</EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>

	</sharedAnimationDefinitions>

	<xiaoBing>
		<!--敌人数据 -->
		<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
			<animationDefinition id="xiaoBingFallDownShow1"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</fallDownEffect>
		<bombInfo speed="-30" time="5" normalDead="true" />
		<enemyData>

			<!-- totalHp=血量 attack=攻击 expOfDieThisEnemy=经验 defence=防御 dogdeRate=闪避 
				criticalRate=暴击率 criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中 -->
			<data att="totalHp" value="3800000" />
			<data att="attack" value="20000" />
			<data att="expOfDieThisEnemy" value="60000" />
			<data att="defence" value="10000" />
			<data att="dogdeRate" value="0.08" />
			<data att="criticalRate" value="1.5" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.2" />
			<data att="hitRate" value="0.09" />
		</enemyData>
		<!--移动速度以秒为单位 -->
		<animal id="enemy31" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="60" bodyHeight="110" walkSpeed="30"
			runSpeed="0">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->

		<isFly>true</isFly><!-- 是否是天上飞的怪物 -->
         <notBePushed>true</notBePushed><!--不能被推 -->



			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="-90" y="-30" z="-1" xRange="180" yRange="60"
				zRange="100" />

			<idle defId="walk_enemy35" />
			<walk defId="walk_enemy35" />
			<run defId="walk_enemy35" />
			<attack defId="attack_enemy35" />
			<hurt defId="hurt_enemy35" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_enemy35" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow1" />

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>

				<animationDefinition id="walk_enemy35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Enemy.swf"
						showClass="Walk_Monster_35" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="hurt_enemy35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Enemy.swf"
						showClass="BeAttack_Monster_35" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Enemy.swf"
						showClass="Attack_Monster_35" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Enemy.swf"
						showClass="Dead_Monster_35" x_offset="0" y_offset="0" />
				</animationDefinition>


				<animationDefinition id="enemyFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="xiaoBingFallDownShow1"
					rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="FallDownEffect" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>


		</animal>
	</xiaoBing>


	<boss>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="5600000" />
			<data att="attack" value="25000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="18000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>10</skillCd>
		
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		
		<animal id="boss35" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="12"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->

			<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->
			
			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="-20" y="-50" z="-1" xRange="90" yRange="100"
				zRange="100" />

			<idle defId="idle_boss35" />
			<walk defId="walk_boss35" />
			<run defId="run_boss35" />
			<attack defId="attack_boss35" />
			<hurt defId="hurt_boss35" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss35" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

		<skill id="Skill_BossBall" className="YJFY.BossMode.Boss1.Skill_BossThrowWeapon"
				bodyDefId="skill3Animation" dropShowDefId="daZhaoDropShow" bombShowDefId="daZhaoBombShow">
				<dropAttackRange x="-100" y="-100" z="-1" xRange="200"
					yRange="200" zRange="100" />
				<animationDefinition id="daZhaoDropShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
						showClass="ItemSkill2Ball" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="daZhaoBombShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
					showClass="ItemSkill2Bomb" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
		
		
		<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
						showClass="Walk_Boss_35" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
						showClass="Walk_Boss_35" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss29" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_29" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
						showClass="BeAttack_Boss_35" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
						showClass="Attack_Boss_35" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss35" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
						showClass="Dead_Boss_35" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skill3Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
						showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
					showClass="ItemSkill2Ball" x_offset="0" y_offset="0" />
			</animationDefinition>
				<animationDefinition id="daZhaoBombShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level35/Boss.swf"
					showClass="ItemSkill2Bomb" x_offset="0" y_offset="0" />
			</animationDefinition>
				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</boss>
</data>
