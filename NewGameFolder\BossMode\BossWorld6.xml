<?xml version="1.0" encoding="utf-8" ?>
<data id="boss6" swfPath="NewGameFolder/BossMode/Scene1.swf"
	className="BossModeMap1" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound5.swf" className="SoundCC" />
    <startShow swfPath="NewGameFolder/BossMode/Boss6.swf" className="CBossStoryShow" />
	<bossHead swfPath="NewGameFolder/BossMode/Boss6.swf" className="CBossHead" />
	<lottery>
		<!-- 只能12个-->
	    <!-- 无敌药水 -->
		<item id="11000000" num="2" proWeight="4" />
		<!-- 开孔灵符 -->
        <item id="10500032" num="2" proWeight="10" />
        <!-- 仓库钥匙 -->
        <item id="11100003" num="2" proWeight="10" />
        <!-- 升级宝石 -->
        <item id="10500008" num="4" proWeight="15" />
        <!-- 升级宝石 -->
        <item id="10500008" num="5" proWeight="10" />
        <!--  幸运宝石 -->
        <item id="10500000" num="1" proWeight="30" />
        <!--  幸运宝石 -->
        <item id="10500000" num="2" proWeight="10" />
        <!--  幸运宝石 -->
        <item id="10500000" num="3" proWeight="1" />
        <!--  幸运宝石 -->
        <item id="10500000" num="4" proWeight="1" />
         <!-- 极品宠物训练卡 -->
        <item id="11000021" num="1" proWeight="3" />
        <!--  金袋 -->
        <item id="11100000" num="1" proWeight="3" /> 
        <!--  大圣翎 -->
        <item id="10500023" num="1" proWeight="1" />
	</lottery>
	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
   <boss type="YJFY.BossMode.Boss1.Boss1">
	<!--敌人数据 -->
   <bossData hpSegment="10000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_BossTeleport" hurtMulti="0" costMp="10" cdTime="5000" priorityForRun="0" priorityForRunInHurt="1"  
	isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="Skill_BossSkill1" hurtMulti="3" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
	isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="BossDaZhao" hurtMulti="3" costMp="30" cdTime="10000" priorityForRun="2" priorityForRunInHurt="0"  
	isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO"/> <!--cdTime 毫秒-->
	</bossData>
	<enemyData>
		<data att="totalHp" value="249999" />
		<data att="attack" value="1100" />
		<data att="expOfDieThisEnemy" value="600000" />
		<data att="defence" value="150" />
		<data att="dogdeRate" value="0.03" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.45" />
		<data att="hitRate" value="0.09" />
		
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="10" />
	</enemyData>
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="200" walkSpeed="60"
		runSpeed="160">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="-150" y="-100" z="-1" xRange="300" yRange="200" zRange="200" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
			
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.BossMode.Boss1.Skill_BossSkill1" x="0"
			y="-100" z="-1" xRange="470" yRange="200" zRange="100" bodyDefId="skill1Animation" skillAttackReachFrameLabel="skillReach"
			skillEndFrameLabel="skillEnd^stop^"  >
			
		</skill>
		
		<skill id="BossDaZhao" className="YJFY.BossMode.Boss1.BossDaZhao1"  bodyDefId="daZhaoBodyShow" dropShowDefId="daZhaoDropShow">
			<dropAttackRange x="-200" y="-50" z="-1" xRange="200" yRange="100" zRange="100" />
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/BossMode/Boss6.swf" 
				showClass="CBossRun" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="CBossBeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="2"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="CBossDead" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="DisappearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="AppearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss6.swf"
					showClass="CBossChallengeBossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss>
</data>
