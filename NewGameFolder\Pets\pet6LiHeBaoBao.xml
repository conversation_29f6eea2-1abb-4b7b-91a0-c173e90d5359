<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet6" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet6Idle" />
		<walk defId="pet6Walk" />
		<run defId="pet6Run" />
		<attack defId="pet6Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet6Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet6Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	    <skill id="Skill_Pet6Skill" className="YJFY.Skill.PetSkills.Skill_Pet6Skill" x="-480" hurtDuration="5000"
			y="-280" z="-1" xRange="960" yRange="560" zRange="100" bodyDefId="pet6SkillBodyShow" bodyAttackReachFrameLabel="down" 
			bodySkillEndFrameLabel="end^stop^"  everyEntityAddShowDefId="pet6SkillEnemyBeAttack" 
			everyEntityAddShowIsFrontOfBody="1" >
			<animationDefinition id="pet6SkillEnemyBeAttack" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet6All.swf"
					showClass="PetSkill6Effect_BeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet6Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet6All.swf"
					showClass="PetStand6_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet6Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet6All.swf"
					showClass="PetWalk6_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet6SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet6All.swf"
					showClass="PetSkill6Attack_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet6SkillFrontShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet6All.swf"
					showClass="PetSkill6Effect_Word_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			<animationDefinition id="pet6SkillEnemyBeAttack" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet6All.swf"
					showClass="PetSkill6Effect_BeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet6Idle" eqClassName="Pet_LiHe_1"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetStand6_1"
				x_offset="0" y_offset="0" />
			<show defId="pet6Walk" eqClassName="Pet_LiHe_1"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetWalk6_1"
				x_offset="0" y_offset="0" />
           <show defId="pet6SkillBodyShow" eqClassName="Pet_LiHe_1"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetSkill6Attack_1"
				x_offset="0" y_offset="0" />
			<show defId="pet6SkillFrontShow" eqClassName="Pet_LiHe_1"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetSkill6Effect_Word_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet6Idle" eqClassName="Pet_LiHe_2"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetStand6_2"
				x_offset="0" y_offset="0" />
			<show defId="pet6Walk" eqClassName="Pet_LiHe_2"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetWalk6_2"
				x_offset="0" y_offset="0" />
           <show defId="pet6SkillBodyShow" eqClassName="Pet_LiHe_2"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetSkill6Attack_2"
				x_offset="0" y_offset="0" />
			<show defId="pet6SkillFrontShow" eqClassName="Pet_LiHe_2"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetSkill6Effect_Word_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet6Idle" eqClassName="Pet_LiHe_3"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetStand6_3"
				x_offset="0" y_offset="0" />
			<show defId="pet6Walk" eqClassName="Pet_LiHe_3"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetWalk6_3"
				x_offset="0" y_offset="0" />
           <show defId="pet6SkillBodyShow" eqClassName="Pet_LiHe_3"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetSkill6Attack_3"
				x_offset="0" y_offset="0" />
			<show defId="pet6SkillFrontShow" eqClassName="Pet_LiHe_3"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetSkill6Effect_Word_3"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
