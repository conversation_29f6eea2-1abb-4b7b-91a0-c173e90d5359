<?xml version="1.0" encoding="utf-8" ?>
<data>
	<skillLinkData>
	</skillLinkData>
	<hurtAnimation2 defId="hurt2_tortoise" playFrameLabel="1" recoverFrameLabel="recover^stop^" />
	<autoAttackAI className="YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetAI1" />
	<petAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="500" />
		<data att="unableAttackMaxInterval" value="500" />
	</petAttackData>
   <!--移动速度以秒为单位 -->
	<animal id="tortoise" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="2000" bodyWidth="60" bodyHeight="80" walkSpeed="60"
		runSpeed="120">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-50" z="-1" xRange="400" yRange="100"
			zRange="100" />

		<idle defId="idle_tortoise" />
		<walk defId="walk_tortoise" />
		<run defId="run_tortoise" />
		<attack defId="attack_tortoise" />
		<attackEffect defId="enemyAttackEffect_tortoise" />
		<hurt defId="hurt_tortoise" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_tortoise" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow_tortoise" />
		<skill id="SkillResurgence" className="YJFY.LevelMode2.Levels1.Skill_AllResurgence"  bodyDefId="skillShow_resurgence"
			x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="300">
		</skill>
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="idle_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="IdleOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="WalkOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
            <animationDefinition id="run_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="RunOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="Hurt1OfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="Hurt2OfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="AttackOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="DieOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillShow_resurgence" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="skill_resurgence" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="enemyFootShadow_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="10"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf"
					showClass="ShadowOfAutomaticPet1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="enemyAttackEffect_tortoise" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf"
					showClass="AttackEffectOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>

        <shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="enemyFootShadow_tortoise" showSeriesId="d"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet1"
				x_offset="0" y_offset="0" />
			<show defId="enemyFootShadow_tortoise" showSeriesId="c"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet2"
				x_offset="0" y_offset="0" />
			<show defId="enemyFootShadow_tortoise" showSeriesId="b"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet3"
				x_offset="0" y_offset="0" />
			<show defId="enemyFootShadow_tortoise" showSeriesId="a"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet4"
				x_offset="0" y_offset="0" />
			<show defId="enemyFootShadow_tortoise" showSeriesId="s"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet5"
				x_offset="0" y_offset="0" />
				
		</shows>

	</animal>
</data>