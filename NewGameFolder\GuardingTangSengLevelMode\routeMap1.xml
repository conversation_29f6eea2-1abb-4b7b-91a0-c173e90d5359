<?xml version="1.0" encoding="utf-8" ?>
<data routeMapId="routeMap1">
    <level btnName="levelMaptBtn_1" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level1/level.xml" />
    <level btnName="levelMaptBtn_2" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level2/level.xml" />
    <level btnName="levelMaptBtn_3" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level3/level.xml" />
    <level btnName="levelMaptBtn_4" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level4/level.xml" />
    <level btnName="levelMaptBtn_5" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level5/level.xml" />
    <level btnName="levelMaptBtn_6" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level6/level.xml" />
    <level btnName="levelMaptBtn_7" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level7/level.xml" />
    <level btnName="levelMaptBtn_8" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level8/level.xml" />
    <level btnName="levelMaptBtn_9" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level9/level.xml" />
    <level btnName="levelMaptBtn_10" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level10/level.xml" />
    <level btnName="levelMaptBtn_11" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level11/level.xml" />
    <level btnName="levelMaptBtn_12" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level12/level.xml" />
    <level btnName="levelMaptBtn_13" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level13/level.xml" />
    <level btnName="levelMaptBtn_14" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level14/level.xml" />
    <level btnName="levelMaptBtn_15" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level15/level.xml" />
    <level btnName="levelMaptBtn_16" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level16/level.xml" />
    <level btnName="levelMaptBtn_intersection" toAnotherRouteMap="1"  routeMapXMLPath="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" 
	       routeMapShowSwfPath="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" routeMapShowClassName="RouteMap3" />
    <level btnName="levelMaptBtn_9999" toAnotherRouteMap="1"  routeMapXMLPath="NewGameFolder/GuardingTangSengLevelMode/endlessMap.xml" 
	       routeMapShowSwfPath="NewGameFolder/GuardingTangSengLevelMode/endlessMap.swf" routeMapShowClassName="endlessMap" />
</data>