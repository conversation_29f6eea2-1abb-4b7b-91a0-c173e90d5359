<?xml version="1.0" encoding="utf-8" ?>
<data>
	<skillLinkData>
		<data part1SkillId="bossSkill1" part2SkillId="jiaoRenSkill1" />
	</skillLinkData>
	<hurtAnimation2 defId="hurt2_jiaoRen" playFrameLabel="1" recoverFrameLabel="recover^stop^" />
	<autoAttackAI className="YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetAI1" />
	<petAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="500" />
		<data att="unableAttackMaxInterval" value="1500" />
	</petAttackData>
    <animal id="jiaoRen" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="150" walkSpeed="200"
		runSpeed="300">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-100" z="-1" xRange="200" yRange="200" zRange="100" />

		<idle defId="idle_jiaoRen" />
		<walk defId="walk_jiaoRen" />
		<run defId="run_jiaoRen" />
		<attack defId="attack_jiaoRen" />
		<hurt defId="hurt1_jiaoRen" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_jiaoRen" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1_jiaoRen" />
		
       
	    
		<skill id="bossSkill1" className="YJFY.LevelMode2.Levels1.Skill_JiaoRenSkill" x="-100" hurtDuration="2000"
			y="-150" z="-1" xRange="500" yRange="300" zRange="200" bodyDefId="skill1Show_jiaoRen" skillAttackReachFrameLabel="skillReach"
			skillEndFrameLabel="skillEnd^stop^">
			<moveData swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf" className="MoveDataOfSkill" />
		</skill>
		<skill id="SkillResurgence" className="YJFY.LevelMode2.Levels1.Skill_AllResurgence"  bodyDefId="skillShow_resurgence"
			x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="300">
		</skill>
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_jiaoRen" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf"
					showClass="IdleOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_jiaoRen" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf"
					showClass="IdleOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_jiaoRen" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf" 
				showClass="RunOfJiaoRen" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt1_jiaoRen" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf"
					showClass="Hurt1OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_jiaoRen" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf"
					showClass="Hurt2OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_jiaoRen" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf"
					showClass="AttackOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_jiaoRen" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf"
					showClass="DieOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="skillShow_resurgence" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf"
					showClass="skill_resurgence" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill1Show_jiaoRen" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf"
					showClass="SkillShowOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<animationDefinition id="bossFootShadow1_jiaoRen" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf"
					showClass="ShadowOfAutomaticPet1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>
        
		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="bossFootShadow1_jiaoRen" showSeriesId="d"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet1"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1_jiaoRen" showSeriesId="c"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet2"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1_jiaoRen" showSeriesId="b"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet3"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1_jiaoRen" showSeriesId="a"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet4"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1_jiaoRen" showSeriesId="s"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet5"
				x_offset="0" y_offset="0" />
				
		</shows>


	</animal>
</data>