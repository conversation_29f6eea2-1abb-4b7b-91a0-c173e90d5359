<?xml version="1.0" encoding="utf-8" ?>
<data id="Level20" swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/level.swf"
	className="LevelMap4" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound5.swf" className="SoundCC" />
	<!--totalWaveNum 用于显示波次总数 -->
		<Waves totalWaveNum="20">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="0" duration="5000" num="10" isFallDown="0" />
		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="15000" duration="5000" num="10" isFallDown="0" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="700" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="30000" duration="5000" num="10" isFallDown="0" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="45000" duration="5000" num="10" isFallDown="0" />
		</Wave>				
		<Wave waveCount="5" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="60000" duration="5000" num="10" isFallDown="0" />
		</Wave>		
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="75000" duration="5000" num="10" isFallDown="0" />
		</Wave>		
		<Wave waveCount="7" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="90000" duration="5000" num="15" isFallDown="0" />
		</Wave>		
		<Wave waveCount="8" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="105000" duration="5000" num="15" isFallDown="0" />
		</Wave>		
		<Wave waveCount="9" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="120000" duration="5000" num="15" isFallDown="0" />
		</Wave>	
		<Wave waveCount="10" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="135000" duration="5000" num="15" isFallDown="0" />
		</Wave>	
		<Wave waveCount="11" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="150000" duration="10000" num="15" isFallDown="1" />
		</Wave>		
		<Wave waveCount="12" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="165000" duration="5000" num="15" isFallDown="0" />
		</Wave>	
		<Wave waveCount="13" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="180000" duration="10000" num="20" isFallDown="0" />
		</Wave>														
		<Wave waveCount="14" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="195000" duration="10000" num="20" isFallDown="1" />
		</Wave>														
		<Wave waveCount="15" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="210000" duration="10000" num="20" isFallDown="0" />
		</Wave>														
		<Wave waveCount="16" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="225000" duration="10000" num="20" isFallDown="0" />
		</Wave>														
		<Wave waveCount="17" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="240000" duration="10000" num="15" isFallDown="1" />
		</Wave>														
		<Wave waveCount="18" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="255000" duration="5000" num="20" isFallDown="0" />
		</Wave>	
		<Wave waveCount="19" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="270000" duration="10000" num="15" isFallDown="1" />
		</Wave>			
																		
		<Wave waveCount="20" totalEnemyNum="10" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="300000" duration="10000" num="20" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss" startTime="305000" duration="1000" num="1"  />
		</Wave>
	</Waves>
      <EqDrop>
	   <xiaoBing noDropProWeight="500">
		   <!--proWeight 概率权重-->
		   
		  <!-- 无敌药水-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="2" />		 
		  <!-- 幸运蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_CaiDan_S" proWeight="3" />		 
		  <!-- 麋鹿蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Lu_S" proWeight="1" />		 	
   	       
		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp1_S" proWeight="2" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp1_S" proWeight="1" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack1_S" proWeight="1" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp1_S" proWeight="1" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence1_S" proWeight="2" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="1" />   
		   
		  <!-- 地狱灵芝 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LingZhi_S" proWeight="2" />		 
		  <!-- 瑶池圣莲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Flower_S" proWeight="2" />		 
   	      <!-- 龙胆果 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_GuoZi_S" proWeight="1" />

  	      <!--  红药 -->   
   	      <item dropClassName="Item_HpUp" proWeight="50" />
   	      <!--  金币 -->  
   	      <item dropClassName="Item_MoneyUp" proWeight="50" />
   	      <!--  蓝药 -->  
   	      <item dropClassName="Item_MpUp" proWeight="50" />    	      	        


   	      
 

     

   	      <!-- 羽毛 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_SuperLu_1_S" proWeight="2" />
   	      <!-- 圣灵精华 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S" proWeight="1" />
   	      <!-- 一级火 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S" proWeight="1" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="15" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="5" />
   	      <!-- 高级升级宝石模具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="3" />
   	          
	  </xiaoBing>
	  
	  
	   <boss noDropProWeight="20">
		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="5" />
				   <numData num="2" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="4" proWeight="8" />
				   <numData num="5" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>		  

   	      
		  <!-- 无敌药水-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="2" />		 
		  <!-- 幸运蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_CaiDan_S" proWeight="2" />		 
		  <!-- 麋鹿蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Lu_S" proWeight="1" />		 	
   	       
		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp1_S" proWeight="2" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp1_S" proWeight="1" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack1_S" proWeight="1" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp1_S" proWeight="1" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence1_S" proWeight="2" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="1" /> 
   	      <!-- 碎石锤 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S" proWeight="1" /> 
   	      
   	        
   	      <!-- 高级防御书 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.PetSkillBook_6" proWeight="1" />   
		   

   	      	         	      
<!--   	       红药   
   	      <item dropClassName="Item_HpUp" proWeight="100" />
   	       金币  
   	      <item dropClassName="Item_MoneyUp" proWeight="300" />
   	       蓝药  
   	      <item dropClassName="Item_MpUp" proWeight="100" /> 
  -->

     

   	      <!-- 黑羽毛 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_SuperLu_1_S" proWeight="2" />
   	      <!-- 圣灵精华 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S" proWeight="2" />
   	      <!-- 一级火 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S" proWeight="2" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="2" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="1" />
   	      <!-- 高级升级宝石模具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="1" />
	   </boss>
	   
   </EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   <xiaoBing>
	<!--敌人数据 -->
	<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
		<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
	</fallDownEffect>
	<enemyData>
	
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
		<data att="totalHp" value="50000" />
		<data att="attack" value="3000" />
		<data att="expOfDieThisEnemy" value="8000" />
		<data att="defence" value="120" />
		<data att="dogdeRate" value="0.1" />
		<data att="criticalRate" value="0.2" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.6" />
		<data att="hitRate" value="0.1" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy20" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="80" bodyHeight="120" walkSpeed="22"
		runSpeed="100">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="40" yRange="60"
			zRange="100" />

		<idle defId="walk_enemy20" />
		<walk defId="walk_enemy20" />
		<run defId="walk_enemy20" />
		<attack defId="attack_enemy20" />
		<hurt defId="hurt_enemy20" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy20" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow1" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="walk_enemy20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Enemy.swf"
					showClass="Walk_Monster_4" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="hurt_enemy20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Enemy.swf"
					showClass="BeAttack_Monster_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Enemy.swf"
					showClass="Attack_Monster_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Enemy.swf"
					showClass="Dead_Monster_4" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
		</animationDefinitions>



	</animal>
</xiaoBing>
   <boss>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="250000" />
		<data att="attack" value="4000" />
		<data att="expOfDieThisEnemy" value="500000" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.03" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.15" />
	</enemyData>
	<animal id="boss20" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="idle_boss20" />
		<walk defId="walk_boss20" />
		<run defId="run_boss20" />
		<attack defId="attack_boss20" />
		<hurt defId="hurt_boss20" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss20" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Boss.swf"
					showClass="Walk_Boss_4" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Boss.swf"
					showClass="Walk_Boss_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss20" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_4" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Boss.swf"
					showClass="BeAttack_Boss_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Boss.swf"
					showClass="Attack_Boss_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss20" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level20/Boss.swf"
					showClass="Dead_Boss_4" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</boss>
</data>
