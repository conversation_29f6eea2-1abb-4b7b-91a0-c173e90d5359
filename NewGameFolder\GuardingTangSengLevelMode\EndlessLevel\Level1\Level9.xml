<?xml version="1.0" encoding="utf-8" ?>
<data id="level1" swfPath="NewGameFolder/GuardingTangSengLevelMode/EndlessMode/Level1.swf"
	className="LevelMap" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound7.swf" className="SoundQ" />	
	<startShow swfPath="NewGameFolder/LevelMode2/Level1/StartShow1.swf" className="StartShow" />
	<Waves totalWaveNum="5">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="20" x="450" y="200" xRange="450" yRange="200" cameraX="0">
			<Enemy enemyClass="YJFY.EndlessMode.EndlessXiaoBing" xmlPath="xiaoBing" startTime="0" duration="2500" num="5"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="450" y="200" xRange="450" yRange="200" cameraX="1">
			<Enemy enemyClass="YJFY.EndlessMode.EndlessXiaoBing" xmlPath="xiaoBing" startTime="4000" duration="2500" num="5"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="450" y="200" xRange="450" yRange="200" cameraX="2">
			<Enemy enemyClass="YJFY.EndlessMode.EndlessXiaoBing" xmlPath="xiaoBing" startTime="0" duration="2500" num="10"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="450" y="200" xRange="450" yRange="200" cameraX="3">
			<Enemy enemyClass="YJFY.EndlessMode.EndlessXiaoBing" xmlPath="xiaoBing" startTime="4000" duration="2500" num="10"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="450" y="200" xRange="450" yRange="200" cameraX="3">
			<Enemy enemyClass="YJFY.EndlessMode.EndlessBoss" xmlPath="boss" startTime="2000" duration="2500" num="1"  />
		</Wave>
	</Waves>
    <!--抽奖配置-->
	<lottery>
		<!-- 只能12个-->
		<!-- 升级宝石-->
		<item id="10500064" num="1" proWeight="3" />
		<!-- 升级宝石 -->
        <item id="10500008" num="4" proWeight="20" />
        <!-- 升级宝石-->
        <item id="10500008" num="5" proWeight="20" />
        <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="20" />
        <!-- 幸运宝石 -->
        <item id="10500000" num="2" proWeight="15" />
        <!-- 幸运宝石-->
        <item id="10500000" num="3" proWeight="10" />
        <!--  幸运宝石 -->
        <item id="10500000" num="4" proWeight="5" />
        <!-- 龙蛋 -->
        <item id="10800000" num="1" proWeight="10" />
        <!-- 剑蛋 -->
        <item id="10800007" num="1" proWeight="10" />
        <!-- 超进化仙果 -->
        <item id="10500030" num="1" proWeight="5" />
        <!-- 神秘圣诞宠物蛋-->
        <item id="10800008" num="1" proWeight="3" />
        <!--   大圣翎 -->
        <item id="10500023" num="1" proWeight="3" />
	</lottery>
   <EqDrop>
	   <xiaoBing noDropProWeight="500">
		   <!--proWeight 概率权重-->
   	          
	  </xiaoBing>
	  
	  
	   <boss noDropProWeight="50">
		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="5" />
				   <numData num="2" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="4" proWeight="8" />
				   <numData num="5" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>		  
	   </boss>
	   
   </EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   <xiaoBing>
	<!--敌人数据 -->
	<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
			<animationDefinition id="xiaoBingFallDownShow1"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
	</fallDownEffect>
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="5000" />
		<data att="unableAttackMaxInterval" value="10000" />
	</enemyAttackData>
	<enemyData>
	
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
		<data att="totalHp" value="15000" />
		<data att="attack" value="600" /> 
		<data att="expOfDieThisEnemy" value="500" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.01" />
		<data att="criticalRate" value="0.03" /> 
		<data att="criticalMuti" value="2.5" />
		<data att="deCriticalRate" value="0.02" />
		<data att="hitRate" value="0.01" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy27" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="60" bodyHeight="90" walkSpeed="40"
			runSpeed="60">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				
				/> -->
					<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->
			






			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-30" z="-1" xRange="120" yRange="60"
				zRange="100" />


			<idle defId="walk_enemy27" />
			<walk defId="walk_enemy27" />
			<run defId="walk_enemy27" />
			<attack defId="attack_enemy27" />
			<hurt defId="hurt_enemy27" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_enemy27" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow1" />

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>

				<animationDefinition id="walk_enemy27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Enemy.swf"
						showClass="Walk_Monster_27" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="hurt_enemy27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Enemy.swf"
						showClass="BeAttack_Monster_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Enemy.swf"
						showClass="Attack_Monster_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Enemy.swf"
						showClass="Dead_Monster_27" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="AddHp" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Enemy.swf"
						showClass="AddHp" x_offset="0" y_offset="0" />
				</animationDefinition>



				<animationDefinition id="enemyFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="xiaoBingFallDownShow1"
					rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="FallDownEffect" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>



		</animal>
</xiaoBing>

<boss >
	<!--敌人数据 -->
	<hpShowData frameLabel="jiaoRen" />
   <bossData hpSegment="1000"> <!--用于血条显示，一条的容量-->
	</bossData>
	<invincibleSkill skillId="skill_BossInvincibleSkill" cdTime="15000"/> <!--无敌防护盾-->
	<hurtAnimation2 defId="hurt2_boss" playFrameLabel="1" recoverFrameLabel="recover^stop^"/>
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="3000" />
		<data att="unableAttackMaxInterval" value="6000" />
	</enemyAttackData>
	<enemyData>
		<data att="totalHp" value="45000" />
		<data att="attack" value="800" />  
		<data att="expOfDieThisEnemy" value="1000" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.01" />
		<data att="criticalRate" value="0.04" /> 
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.04" />
		<data att="hitRate" value="0.01" />
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="2" />
	</enemyData>
	<animal id="boss27" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="30"
			runSpeed="70">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->
					<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			      <notBePushed>true</notBePushed><!--不能被推 -->
			


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-30" z="-1" xRange="120" yRange="60"
				zRange="100" />

			<idle defId="idle_boss27" />
			<walk defId="walk_boss27" />
			<run defId="run_boss27" />
			<attack defId="attack_boss27" />
			<hurt defId="hurt_boss27" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss27" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />

			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Walk_Boss_27" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Walk_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="run_boss27" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf" 
					showClass="Walk_Boss_27" x_offset="0" y_offset="0" /> </animationDefinition>
				<animationDefinition id="hurt_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="BeAttack_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Attack_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Dead_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>


				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="AddHp" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="AddHp" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>



		</animal>
</boss>
</data>
