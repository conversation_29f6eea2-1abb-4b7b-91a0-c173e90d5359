<?xml version="1.0" encoding="utf-8" ?>
<data id="Level17" swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/level.swf"
	className="LevelMap1" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound1.swf" className="SoundSC" />
	<!--totalWaveNum 用于显示波次总数 -->
		<Waves totalWaveNum="10">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="0" duration="5000" num="7" isFallDown="0" />
		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="15000" duration="5000" num="7" isFallDown="0" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="30000" duration="10000" num="7" isFallDown="1" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="45000" duration="5000" num="7" isFallDown="0" />
		</Wave>				
		<Wave waveCount="5" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="60000" duration="10000" num="7" isFallDown="0" />
		</Wave>		
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="75000" duration="5000" num="7" isFallDown="0" />
		</Wave>		
		<Wave waveCount="7" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="90000" duration="10000" num="7" isFallDown="0" />
		</Wave>		
		<Wave waveCount="8" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="105000" duration="10000" num="7" isFallDown="1" />
		</Wave>		
		<Wave waveCount="9" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="120000" duration="5000" num="7" isFallDown="0" />
		</Wave>	

					
		<Wave waveCount="10" totalEnemyNum="10" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="135000" duration="10000" num="7" isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss2" startTime="135000" duration="1000" num="1"  />
		</Wave>
	</Waves>
      <EqDrop>
			   <boss noDropProWeight="200">
	   		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="6" />
				   <numData num="2" proweight="4" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="3" proWeight="8" />
				   <numData num="4" proWeight="1" />
			   </bigDropNumData>
		   </dropNumData>		  
	   
	   
		   <!--proWeight 概率权重-->
 		  <!-- 无敌药水 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="5" />
  	      <!-- 龙蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Tyrannosaurs_S" proWeight="5" />
   	      <!-- 雷蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Jian_S" proWeight="5" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="20" />
   	      <!-- 高级升级宝石磨具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="3" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="3" /> 
 		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp2_S" proWeight="2" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp2_S" proWeight="2" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack2_S" proWeight="2" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp2_S" proWeight="2" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence2_S" proWeight="2" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="5" />   
   	      <!-- 碎石锤 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S" proWeight="5" />  	      
     	     <!-- 朱雀 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ZhuQueStone_S" proWeight="2" />   
   	      <!-- 玄武 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_XuanWuStone_S" proWeight="2" />      
     	     <!-- 青龙 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_QingLongStone_S" proWeight="2" />   
   	      <!-- 白虎 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_BaiHuStone_S" proWeight="2" />      
		  <!-- 灵狐 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LingHuShi_S" proWeight="5" />    	      
   	         	              	      
           <!--  红药   -->
   	      <item dropClassName="Item_HpUp" proWeight="50" />
           <!--  蓝药  -->
   	      <item dropClassName="Item_MpUp" proWeight="50" /> 
           <!--    金币  -->
   	      <item dropClassName="Item_MoneyUp_10000" proWeight="10"  /> 
           <!--    金币  -->
   	      <item dropClassName="Item_MoneyUp_50000" proWeight="3"  /> 
              <!--   灵兽石-->
   	      <item dropClassName="Item_StrengthenNum_10" proWeight="5"  /> 
             <!--  召唤卷轴 -->
   	      <item dropClassName="Item_ZhaoHuan_1" proWeight="3"  /> 
      
	   </boss>
	   
   </EqDrop>
	   
 



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   <xiaoBing>
	<!--敌人数据 -->
	<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
		<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
	</fallDownEffect>
	<enemyData>
	
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
		<data att="totalHp" value="25000" />
		<data att="attack" value="1000" />
		<data att="expOfDieThisEnemy" value="1400" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.05" />
		<data att="criticalRate" value="0.2" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.09" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy17" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80" walkSpeed="20"
		runSpeed="100">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="40" yRange="60"
			zRange="100" />

		<idle defId="walk_enemy17" />
		<walk defId="walk_enemy17" />
		<run defId="walk_enemy17" />
		<attack defId="attack_enemy17" />
		<hurt defId="hurt_enemy17" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy17" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow1" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="walk_enemy17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Enemy.swf"
					showClass="Walk_Monster_1" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="hurt_enemy17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Enemy.swf"
					showClass="BeAttack_Monster_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Enemy.swf"
					showClass="Attack_Monster_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Enemy.swf"
					showClass="Dead_Monster_1" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
		</animationDefinitions>



	</animal>
</xiaoBing>
   <boss1>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="350000" />
		<data att="attack" value="3600" />
		<data att="expOfDieThisEnemy" value="50000" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.03" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.15" />
	</enemyData>
	<animal id="boss17" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="40" yRange="60"
			zRange="100" />

		<idle defId="idle_boss17" />
		<walk defId="walk_boss17" />
		<run defId="run_boss17" />
		<attack defId="attack_boss17" />
		<hurt defId="hurt_boss17" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss17" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="Walk_Boss_1" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="Walk_Boss_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss17" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_16" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="BeAttack_Boss_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="Attack_Boss_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="Dead_Boss_1" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</boss1>


   <boss2>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="1750000" />
		<data att="attack" value="3600" />
		<data att="expOfDieThisEnemy" value="500000" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.03" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.15" />
	</enemyData>
	<animal id="boss17" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="idle_boss17" />
		<walk defId="walk_boss17" />
		<run defId="run_boss17" />
		<attack defId="attack_boss17" />
		<hurt defId="hurt_boss17" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss17" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="Walk_Boss_1" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT rM="0.5" gM="1" bM="0" rO="0" gO="0" bO="0" aO="0" />
				</show>		
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="Walk_Boss_1" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT rM="0.5" gM="1" bM="0" rO="0" gO="0" bO="0" aO="0" />
				</show>		
			</animationDefinition>
			<!-- <animationDefinition id="run_boss17" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_16" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="BeAttack_Boss_1" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT rM="0.5" gM="1" bM="0" rO="0" gO="0" bO="0" aO="0" />
				</show>		
			</animationDefinition>
			<animationDefinition id="attack_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="Attack_Boss_1" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT rM="0.5" gM="1" bM="0" rO="0" gO="0" bO="0" aO="0" />
				</show>		
			</animationDefinition>
			<animationDefinition id="die_boss17" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level17/Boss.swf"
					showClass="Dead_Boss_1" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT rM="0.5" gM="1" bM="0" rO="0" gO="0" bO="0" aO="0" />
				</show>		
			</animationDefinition>


			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</boss2>
</data>
