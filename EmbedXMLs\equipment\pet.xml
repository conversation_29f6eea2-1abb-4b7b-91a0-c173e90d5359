<?xml version="1.0" encoding="utf-8" ?>
<data>
<!-- 宠物装备 level为宠物进化阶段 maxLevel为宠物最大进化阶段 petLevel为宠物等级 activeSkill 宠物的主动技能id 
		upgradeLevelNum 宠物的进化等级 （如18 宠物19级进化 ，宠物就没19级了 -->
	<!-- data :宠物各个等级的数据 experienceVolume： 该等级宠物的升级所需经验量 essentialVolume：该等级宠物的精气量
	 promoteXMLId:宠物幻化引用skill2的幻化配置    initPassiveSkillNum：初始技能数量     awakeSkill：觉醒技能-->

	<item id="10400100" className="Pet_Tyrannosaurs_1" name="肚皮龙" petSeries="tyrannosaurs" 
		level="1" maxLevel="3" description="卖萌的小龙总是鼓着肚皮，因此得来肚皮龙这个名字。" price="10000"
		owner="" equipmentType="pet" activeSkill="10103100" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
	<item id="10400200" className="Pet_Tyrannosaurs_2" name="暴龙" petSeries="tyrannosaurs"
		level="2" maxLevel="3" description="开始成长的肚皮龙带上了盔甲，它不在鼓肚皮卖萌，而是开始迎接各种各样的挑战。"
		price="50000" owner="" equipmentType="pet" activeSkill="10103200"
		upgradeLevelNum="20" isAbleSell="0" messageBoxColor="0xa31ec4"
		isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
	<item id="10400300" className="Pet_Tyrannosaurs_3" name="暴走龙魂" petSeries="tyrannosaurs"
		level="3" maxLevel="3" description="很少龙族能进化到这一形态，它们全身遍布着机甲，为战斗而活着。"
		price="100000" owner="" equipmentType="pet" activeSkill="10103300"
		upgradeLevelNum="20" isAbleSell="0" messageBoxColor="0xfd8602"
		isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	<item id="10400101" className="Pet_XiaoPiKaQiu_1" name="雷精" level="1" petSeries="xiaoPiKaQiu"
		maxLevel="3" description="可爱的小雷精。" price="10000" owner="" equipmentType="pet"
		activeSkill="10103101" upgradeLevelNum="20" isAbleSell="0"
		messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
	<item id="10400201" className="Pet_XiaoPiKaQiu_2" name="怒雷" level="2" petSeries="xiaoPiKaQiu"
		maxLevel="3" description="成长后的雷精，已经拥有令僵尸苦恼的法力了。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103201" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
	<item id="10400301" className="Pet_XiaoPiKaQiu_3" name="闪电幽魂" petSeries="xiaoPiKaQiu"
		level="3" maxLevel="3" description="闪电幽魂是闪电的化身。它可以疯狂地向自己周围的物体释放着无穷的电能。"
		price="100000" owner="" equipmentType="pet" activeSkill="10103301"
		upgradeLevelNum="20" isAbleSell="0" messageBoxColor="0xfd8602"
		isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	<item id="10400102" className="Pet_ShiTouRen_1" name="小石怪" level="1" petSeries="shiTouRen"
		maxLevel="3" description="小石头修炼百年得道，幻化成可爱的小石怪。" price="10000" owner=""
		equipmentType="pet" activeSkill="10103102" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
	<item id="10400202" className="Pet_ShiTouRen_2" name="岩石兽" level="2" petSeries="shiTouRen"
		maxLevel="3" description="小石怪经过磨练后富有战斗力。" price="50000" owner="" equipmentType="pet"
		activeSkill="10103202" upgradeLevelNum="20" isAbleSell="0"
		messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
	<item id="10400302" className="Pet_ShiTouRen_3" name="熔岩巨兽" level="3" petSeries="shiTouRen"
		maxLevel="3" description="石怪的最终形态，他的拳头可以击碎任何僵尸！" price="100000" owner="" equipmentType="pet"
		activeSkill="10103302" upgradeLevelNum="20" isAbleSell="0"
		messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	<item id="10400103" className="Pet_HuoRen_1" name="小萌火" level="1" petSeries="huoRen"
		maxLevel="3" description="一团萌火，可爱的扭动着它的烈焰。" price="10000" owner=""
		equipmentType="pet" activeSkill="10103103" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
	<item id="10400203" className="Pet_HuoRen_2" name="焰魂" level="2" petSeries="huoRen"
		maxLevel="3" description="烈焰的颜色成长了许多，也更具破坏性了。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103203" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400303" className="Pet_HuoRen_3" name="复仇烈焰" level="3" petSeries="huoRen"
		maxLevel="3" description="空荡的眼睛里埋藏着火焰般不停息的仇恨。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103303" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	
		<item id="10400104" className="Pet_JiQiRen_1" name="机械宝宝" level="1" petSeries="jiQiRen"
		maxLevel="3" description="负责打扫卫生的机械宝宝，没有什么武器装备。" price="10000" owner=""
		equipmentType="pet" activeSkill="10103104" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
	<item id="10400204" className="Pet_JiQiRen_2" name="机械先锋" level="2" petSeries="jiQiRen"
		maxLevel="3" description="装备各种武器炮弹的机械先锋令敌人畏惧。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103204" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400304" className="Pet_JiQiRen_3" name="机械战将" level="3" petSeries="jiQiRen"
		maxLevel="3" description="机械战将的弹幕可以制造全屏范围内密不透风的飞弹攻击。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103304" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
		<item id="10400105" className="Pet_BingLong_1" name="蛋壳龙" level="1" petSeries="bingLong" promoteXMLId="1" initPassiveSkillNum="5" 
		maxLevel="3" description="龙蛋经过千年的时间，孵化成天赋异禀的蛋壳龙。" price="10000" owner=""
		equipmentType="pet" activeSkill="10103105" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
		<item id="10400205" className="Pet_BingLong_2" name="寒冰龙" level="2" petSeries="bingLong" promoteXMLId="1"
		maxLevel="3" description="褪去蛋壳的束缚，巨大的翅膀和龙角具有恐怖的破坏力。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103205" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400305" className="Pet_BingLong_3" name="机甲冰龙" level="3" petSeries="bingLong" promoteXMLId="1"
		maxLevel="3" description="龙族帝王，王者的象征，寒冰下的霸气让人望而止步。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103305" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>

    <item id="10400106" className="Pet_LiHe_1" name="礼盒宝宝" level="1" petSeries="liHe" promoteXMLId="2" initPassiveSkillNum="3" 
		maxLevel="3" description="装满六一儿童节礼物的盒子。" price="10000" owner=""
		equipmentType="pet" activeSkill="10103106" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
		<item id="10400206" className="Pet_LiHe_2" name="礼盒精灵" level="2" petSeries="liHe" promoteXMLId="2" initPassiveSkillNum="3" 
		maxLevel="3" description="六一儿童节礼盒精灵，头顶的礼盒里有神秘的礼物。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103206" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400306" className="Pet_LiHe_3" name="礼盒使者" level="3" petSeries="liHe" promoteXMLId="2" initPassiveSkillNum="3" 
		maxLevel="3" description="在六一儿童节送给孩子们气球与欢乐的礼盒使者。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103306" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	
	 <item id="10400107" className="Pet_Jian_1" name="地炎宝剑" level="1" petSeries="jian" promoteXMLId="3" initPassiveSkillMode="randomFixedFive" 
		maxLevel="3" description="上古神铁经过地心之火熔炼而成的宝剑。" price="10000" owner=""
		equipmentType="pet" activeSkill="10103107" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
		<item id="10400207" className="Pet_Jian_2" name="天炎妖剑" level="2" petSeries="jian" promoteXMLId="3"
		maxLevel="3" description="地炎宝剑经过大罗天火的进一步淬炼而成的妖剑。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103207" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400307" className="Pet_Jian_3" name="黑炎魔剑" level="3" petSeries="jian" promoteXMLId="3"
		maxLevel="3" description="天照黑炎附魔而成的魔剑，焚天灭地的象征。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103307" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	
	<item id="10400408" className="Pet_RenXingBingLong_4" name="神龙剑士" level="4" petSeries="renXingBingLong" promoteXMLId="4" initPassiveSkillMode="randomFixedFive" 
		maxLevel="4" description="神龙觉醒，天下无敌。" price="10000000" owner=""  petType="advancePet"
		equipmentType="pet" activeSkill="10103108" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1500" />
		<awakeSkill id="petAwakePassive_GaoJingLiLiang_1"  />
	</item>
	
		<item id="10400409" className="Pet_JuYanZhanJiang_4" name="巨岩战将" level="4" petSeries="junYanZhanJiang" promoteXMLId="2" initPassiveSkillNum="3" 
		maxLevel="4" description="熔岩觉醒，坚不可摧。" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103109" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1500" />
		
		
		<awakeSkill id="petAwakePassive_NaiLi_1" />
	</item>
	
		<item id="10400410" className="Pet_HanDiJuLong_4" name="撼地巨龙" level="4" petSeries="hanDiJuLong" promoteXMLId="5" initPassiveSkillNum="3"   
		maxLevel="4" description="远古撼地巨龙，一锤定音！" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103110" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1500" />
		
		
		<awakeSkill id="petAwakePassive_FuZhu_1" />
	</item>
	
	<item id="10400411" className="Pet_Jian_4" name="降魔剑圣" level="4" petSeries="xiangMoJianSheng" promoteXMLId="6" initPassiveSkillNum="3" 
		maxLevel="4" description="降魔剑圣，传说中得到降魔之剑的人将拥有强大无比的力量！" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103111" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1500" />
		
		
		<awakeSkill id="petAwakePassive_GaoJingLiLiang_1" />
	</item>
	
	
	 <item id="10400112" className="Pet_Lu_1" name="圣诞小鹿" level="1" petSeries="lu" promoteXMLId="7" initPassiveSkillNum="4" 
		maxLevel="3" description="圣诞节的小礼物，小鹿的鹿角可是很厉害的！" price="10000" owner=""
		equipmentType="pet" activeSkill="10103112" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
		<item id="10400212" className="Pet_Lu_2" name="圣诞麋鹿" level="2" petSeries="lu" promoteXMLId="7"  initPassiveSkillNum="4" 
		maxLevel="3" description="圣诞麋鹿，强有力的鹿角将给敌人致命一击。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103212" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400312" className="Pet_Lu_3" name="圣诞神鹿" level="3" petSeries="lu" promoteXMLId="7"  initPassiveSkillNum="4"  
		maxLevel="3" description="圣诞神鹿，皇冠鹿角能击破任何防御。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103312" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	<item id="10400413" className="Pet_TianShiLu_4" name="冰雪女神" level="4" petSeries="lu" promoteXMLId="8" initPassiveSkillNum="5"  
		maxLevel="4" description="冰雪世界中带给人温暖的女神，能召唤神圣的冰雪之力保护伙伴。" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103113" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1500" />
		
		
		<awakeSkill id="petAwakePassive_GaoJiNaiLi_1" />
	</item>
	<item id="10400414" className="Pet_EMoLu_4" name="痛苦女王" level="4" petSeries="lu" promoteXMLId="9" initPassiveSkillNum="5" 
		maxLevel="4" description="冰雪世界中带给人痛苦的女王，能召唤邪恶的冰雪之力发动攻击。" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103114" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1500" />
		
		
		<awakeSkill id="petAwakePassive_GaoJiTiZzhi_1" />
	</item>
	<item id="10400415" className="Pet_LiHe_4" name="魔幻小丑" level="4" petSeries="liHe" promoteXMLId="10" initPassiveSkillNum="5" 
		maxLevel="3" description="礼盒使者超进化形态，充满魔幻之力的小丑，他拥有改变世界的魔力。" price="100000" owner="" petType="advancePet" 
		equipmentType="pet" activeSkill="10103115" upgradeLevelNum="50"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1520" />
		<data petLevel="21" experienceVolume="2000000" essentialVolume="1540" />
		<data petLevel="22" experienceVolume="2200000" essentialVolume="1560" />
		<data petLevel="23" experienceVolume="2400000" essentialVolume="1580" />
		<data petLevel="24" experienceVolume="2600000" essentialVolume="1600" />
		<data petLevel="25" experienceVolume="2800000" essentialVolume="1620" />
		<data petLevel="26" experienceVolume="3000000" essentialVolume="1640" />
		<data petLevel="27" experienceVolume="3200000" essentialVolume="1660" />
		<data petLevel="28" experienceVolume="3400000" essentialVolume="1680" />
		<data petLevel="29" experienceVolume="3600000" essentialVolume="1700" />
		<data petLevel="30" experienceVolume="3800000" essentialVolume="1720" />
		<data petLevel="31" experienceVolume="4000000" essentialVolume="1740" />
		<data petLevel="32" experienceVolume="4200000" essentialVolume="1760" />
		<data petLevel="33" experienceVolume="4400000" essentialVolume="1780" />
		<data petLevel="34" experienceVolume="4600000" essentialVolume="1800" />
		<data petLevel="35" experienceVolume="4800000" essentialVolume="1820" />
		<data petLevel="36" experienceVolume="5000000" essentialVolume="1840" />
		<data petLevel="37" experienceVolume="5200000" essentialVolume="1860" />
		<data petLevel="38" experienceVolume="5400000" essentialVolume="1880" />
		<data petLevel="39" experienceVolume="5600000" essentialVolume="1900" />
		<data petLevel="40" experienceVolume="5800000" essentialVolume="1920" />
		<data petLevel="41" experienceVolume="6000000" essentialVolume="1940" />
		<data petLevel="42" experienceVolume="6200000" essentialVolume="1960" />
		<data petLevel="43" experienceVolume="6400000" essentialVolume="1980" />
		<data petLevel="44" experienceVolume="6600000" essentialVolume="2000" />
		<data petLevel="45" experienceVolume="6800000" essentialVolume="2000" />
		<data petLevel="46" experienceVolume="7000000" essentialVolume="2000" />
		<data petLevel="47" experienceVolume="7200000" essentialVolume="2000" />
		<data petLevel="48" experienceVolume="7400000" essentialVolume="2000" />
		<data petLevel="49" experienceVolume="7600000" essentialVolume="2000" />
		<data petLevel="50" experienceVolume="7800000" essentialVolume="2000" />
	
		
		
	    <awakeSkill id="petAwakePassive_GaoJiTiZzhi_1" />
	</item>	

<!-- id=宠物ID（根据蛋孵化后填写，不能重复）  className=fla文件引用ID（去EquipmentUI2.fla中新增）  petSeries=宠物类型  promoteXMLId=宠物被动幻化引用的ID（在skill2中）
initPassiveSkillNum=宠物初始技能数量  activeSkill=宠物主动技能ID（在skill中）   upgradeLevelNum宠物最高等级
experienceVolume=宠物升级所需经验  essentialVolume宠物精气 -->


	 <item id="10400116" className="Pet_FengHuang_1" name="冰灵凤雏" level="1" petSeries="FengHuang" promoteXMLId="12" initPassiveSkillNum="4" 
		maxLevel="3" description="冰晶凤凰幼崽，偶尔会使用出厉害的冰雪魔法！" price="10000" owner=""
		equipmentType="pet" activeSkill="10103116" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
		<item id="10400216" className="Pet_FengHuang_2" name="寒晶幼凤" level="2" petSeries="FengHuang" promoteXMLId="12"  initPassiveSkillNum="4" 
		maxLevel="3" description="幼年冰晶凤凰，开始领悟强有力的冰雪魔法。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103216" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400316" className="Pet_FengHuang_3" name="冰晶凤凰" level="3" petSeries="FengHuang" promoteXMLId="12"  initPassiveSkillNum="4"  
		maxLevel="3" description="冰晶凤凰，拥有强大的冰雪魔法。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103316" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	<item id="10400417" className="Pet_FengHuang_4" name="冰凤仙子" level="4" petSeries="FengHuang" promoteXMLId="11" initPassiveSkillNum="5"  
		maxLevel="4" description="冰晶凤凰涅槃而生，无限魔力足以冰封世界。" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103117" upgradeLevelNum="50"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		
		
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1520" />
		<data petLevel="21" experienceVolume="2000000" essentialVolume="1540" />
		<data petLevel="22" experienceVolume="2200000" essentialVolume="1560" />
		<data petLevel="23" experienceVolume="2400000" essentialVolume="1580" />
		<data petLevel="24" experienceVolume="2600000" essentialVolume="1600" />
		<data petLevel="25" experienceVolume="2800000" essentialVolume="1620" />
		<data petLevel="26" experienceVolume="3000000" essentialVolume="1640" />
		<data petLevel="27" experienceVolume="3200000" essentialVolume="1660" />
		<data petLevel="28" experienceVolume="3400000" essentialVolume="1680" />
		<data petLevel="29" experienceVolume="3600000" essentialVolume="1700" />
		<data petLevel="30" experienceVolume="3800000" essentialVolume="1720" />
		<data petLevel="31" experienceVolume="4000000" essentialVolume="1740" />
		<data petLevel="32" experienceVolume="4200000" essentialVolume="1760" />
		<data petLevel="33" experienceVolume="4400000" essentialVolume="1780" />
		<data petLevel="34" experienceVolume="4600000" essentialVolume="1800" />
		<data petLevel="35" experienceVolume="4800000" essentialVolume="1820" />
		<data petLevel="36" experienceVolume="5000000" essentialVolume="1840" />
		<data petLevel="37" experienceVolume="5200000" essentialVolume="1860" />
		<data petLevel="38" experienceVolume="5400000" essentialVolume="1880" />
		<data petLevel="39" experienceVolume="5600000" essentialVolume="1900" />
		<data petLevel="40" experienceVolume="5800000" essentialVolume="1920" />
		<data petLevel="41" experienceVolume="6000000" essentialVolume="1940" />
		<data petLevel="42" experienceVolume="6200000" essentialVolume="1960" />
		<data petLevel="43" experienceVolume="6400000" essentialVolume="1980" />
		<data petLevel="44" experienceVolume="6600000" essentialVolume="2000" />
		<data petLevel="45" experienceVolume="6800000" essentialVolume="2000" />
		<data petLevel="46" experienceVolume="7000000" essentialVolume="2000" />
		<data petLevel="47" experienceVolume="7200000" essentialVolume="2000" />
		<data petLevel="48" experienceVolume="7400000" essentialVolume="2000" />
		<data petLevel="49" experienceVolume="7600000" essentialVolume="2000" />
		<data petLevel="50" experienceVolume="7800000" essentialVolume="2000" />
		
<!--awakeSkill 觉醒技能   -->		

		<awakeSkill id="petAwakePassive_GaoJingLiLiang_1" />
	</item>
	
	<!-- id=宠物ID（根据蛋孵化后填写，不能重复）  className=fla文件引用ID（去EquipmentUI2.fla中新增）  petSeries=宠物类型  promoteXMLId=宠物被动幻化引用的ID（在skill2中）
initPassiveSkillNum=宠物初始技能数量  activeSkill=宠物主动技能ID（在skill中）   upgradeLevelNum宠物最高等级
experienceVolume=宠物升级所需经验  essentialVolume宠物精气 -->


	 <item id="10400118" className="Pet_ChongMing_1" name="重明雏鸟" level="1" petSeries="ChongMing" promoteXMLId="13" initPassiveSkillNum="4" 
		maxLevel="3" description="重明鸟雏鸟，已经具有了超凡的资质！" price="10000" owner=""
		equipmentType="pet" activeSkill="10103118" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
		<item id="10400218" className="Pet_ChongMing_2" name="重明鸟" level="2" petSeries="ChongMing" promoteXMLId="13"  initPassiveSkillNum="4" 
		maxLevel="3" description="重明鸟，已经拥有了摧毁怪物的能力。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103218" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400318" className="Pet_ChongMing_3" name="炽焰重明鸟" level="3" petSeries="ChongMing" promoteXMLId="13"  initPassiveSkillNum="4"  
		maxLevel="3" description="炽焰重明鸟，拥有极强的魔法，攻守兼具。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103318" upgradeLevelNum="20"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	<item id="10400419" className="Pet_ChongMing_4" name="炙翼皓目童子" level="4" petSeries="ChongMing" promoteXMLId="14" initPassiveSkillNum="5"  
		maxLevel="4" description="仙资重明鸟飞升而成，拥有减速、破甲的特殊本领。" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103119" upgradeLevelNum="50"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		
		
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1520" />
		<data petLevel="21" experienceVolume="2000000" essentialVolume="1540" />
		<data petLevel="22" experienceVolume="2200000" essentialVolume="1560" />
		<data petLevel="23" experienceVolume="2400000" essentialVolume="1580" />
		<data petLevel="24" experienceVolume="2600000" essentialVolume="1600" />
		<data petLevel="25" experienceVolume="2800000" essentialVolume="1620" />
		<data petLevel="26" experienceVolume="3000000" essentialVolume="1640" />
		<data petLevel="27" experienceVolume="3200000" essentialVolume="1660" />
		<data petLevel="28" experienceVolume="3400000" essentialVolume="1680" />
		<data petLevel="29" experienceVolume="3600000" essentialVolume="1700" />
		<data petLevel="30" experienceVolume="3800000" essentialVolume="1720" />
		<data petLevel="31" experienceVolume="4000000" essentialVolume="1740" />
		<data petLevel="32" experienceVolume="4200000" essentialVolume="1760" />
		<data petLevel="33" experienceVolume="4400000" essentialVolume="1780" />
		<data petLevel="34" experienceVolume="4600000" essentialVolume="1800" />
		<data petLevel="35" experienceVolume="4800000" essentialVolume="1820" />
		<data petLevel="36" experienceVolume="5000000" essentialVolume="1840" />
		<data petLevel="37" experienceVolume="5200000" essentialVolume="1860" />
		<data petLevel="38" experienceVolume="5400000" essentialVolume="1880" />
		<data petLevel="39" experienceVolume="5600000" essentialVolume="1900" />
		<data petLevel="40" experienceVolume="5800000" essentialVolume="1920" />
		<data petLevel="41" experienceVolume="6000000" essentialVolume="1940" />
		<data petLevel="42" experienceVolume="6200000" essentialVolume="1960" />
		<data petLevel="43" experienceVolume="6400000" essentialVolume="1980" />
		<data petLevel="44" experienceVolume="6600000" essentialVolume="2000" />
		<data petLevel="45" experienceVolume="6800000" essentialVolume="2050" />
		<data petLevel="46" experienceVolume="7000000" essentialVolume="2100" />
		<data petLevel="47" experienceVolume="7200000" essentialVolume="2200" />
		<data petLevel="48" experienceVolume="7400000" essentialVolume="2300" />
		<data petLevel="49" experienceVolume="7600000" essentialVolume="2400" />
		<data petLevel="50" experienceVolume="7800000" essentialVolume="2500" />
		
<!--awakeSkill 觉醒技能   -->		

		<awakeSkill id="petAwakePassive_GaoJiNaiLi_1" />
	</item>
	
	
	

	 <item id="10400120" className="Pet_MoLong_1" name="紫焱幼龙" level="1" petSeries="MoLong" promoteXMLId="16" initPassiveSkillNum="4" 
		maxLevel="3" description="紫焱魔龙幼年形态，已初现魔性！" price="10000" owner=""
		equipmentType="pet" activeSkill="10103120" upgradeLevelNum="20" addHitPet="4"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
	</item>
		<item id="10400220" className="Pet_MoLong_2" name="紫焱龙" level="2" petSeries="MoLong" promoteXMLId="16"  initPassiveSkillNum="4" 
		maxLevel="3" description="紫焱龙，已拥有令人惧怕的魔力。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103220" upgradeLevelNum="20" addHitPet="5"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="43000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="75000" essentialVolume="1000" />
	</item>
		<item id="10400320" className="Pet_MoLong_3" name="紫焱魔龙" level="3" petSeries="MoLong" promoteXMLId="16"  initPassiveSkillNum="4"  
		maxLevel="3" description="紫焱魔龙，魔族血统激发，傲视万物。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103320" upgradeLevelNum="20" addHitPet="6"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="500000" essentialVolume="1000" />
	</item>
	<item id="10400421" className="Pet_MoLong_4" name="魔龙圣君" level="4" petSeries="MoLong" promoteXMLId="15" initPassiveSkillNum="5"  
		maxLevel="4" description="紫焱魔龙圣君，掌控魔龙一族力量，拥有傲视仙凡两界的超然实力。" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103121" upgradeLevelNum="50" addHitPet="10"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		
		
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1520" />
		<data petLevel="21" experienceVolume="2000000" essentialVolume="1540" />
		<data petLevel="22" experienceVolume="2200000" essentialVolume="1560" />
		<data petLevel="23" experienceVolume="2400000" essentialVolume="1580" />
		<data petLevel="24" experienceVolume="2600000" essentialVolume="1600" />
		<data petLevel="25" experienceVolume="2800000" essentialVolume="1620" />
		<data petLevel="26" experienceVolume="3000000" essentialVolume="1640" />
		<data petLevel="27" experienceVolume="3200000" essentialVolume="1660" />
		<data petLevel="28" experienceVolume="3400000" essentialVolume="1680" />
		<data petLevel="29" experienceVolume="3600000" essentialVolume="1700" />
		<data petLevel="30" experienceVolume="3800000" essentialVolume="1720" />
		<data petLevel="31" experienceVolume="4000000" essentialVolume="1740" />
		<data petLevel="32" experienceVolume="4200000" essentialVolume="1760" />
		<data petLevel="33" experienceVolume="4400000" essentialVolume="1780" />
		<data petLevel="34" experienceVolume="4600000" essentialVolume="1800" />
		<data petLevel="35" experienceVolume="4800000" essentialVolume="1820" />
		<data petLevel="36" experienceVolume="5000000" essentialVolume="1840" />
		<data petLevel="37" experienceVolume="5200000" essentialVolume="1860" />
		<data petLevel="38" experienceVolume="5400000" essentialVolume="1880" />
		<data petLevel="39" experienceVolume="5600000" essentialVolume="1900" />
		<data petLevel="40" experienceVolume="5800000" essentialVolume="1920" />
		<data petLevel="41" experienceVolume="6000000" essentialVolume="1940" />
		<data petLevel="42" experienceVolume="6200000" essentialVolume="1960" />
		<data petLevel="43" experienceVolume="6400000" essentialVolume="1980" />
		<data petLevel="44" experienceVolume="6600000" essentialVolume="2000" />
		<data petLevel="45" experienceVolume="6800000" essentialVolume="2050" />
		<data petLevel="46" experienceVolume="7000000" essentialVolume="2100" />
		<data petLevel="47" experienceVolume="7200000" essentialVolume="2200" />
		<data petLevel="48" experienceVolume="7400000" essentialVolume="2300" />
		<data petLevel="49" experienceVolume="7600000" essentialVolume="2400" />
		<data petLevel="50" experienceVolume="7800000" essentialVolume="2500" />
		
<!--awakeSkill 觉醒技能   -->		

		<awakeSkill id="petAwakePassive_GaoJiTiZzhi_1" />
	</item>
	
	<item id="10400122" className="Pet_ZhuLong_1" name="烛龙幼崽" level="1" petSeries="ZhuLong" promoteXMLId="17" initPassiveSkillNum="5" 
		maxLevel="3" description="烛龙幼年形态，已初现王者之风！" price="10000" owner=""
		equipmentType="pet" activeSkill="10103122" upgradeLevelNum="30" addHitPet="4"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="21" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="22" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="23" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="24" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="25" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="26" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="27" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="28" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="29" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="30" experienceVolume="43000" essentialVolume="1000" />
		
	
	</item>
		<item id="10400222" className="Pet_ZhuLong_2" name="烛龙" level="2" petSeries="ZhuLong" promoteXMLId="17"  initPassiveSkillNum="5" 
		maxLevel="3" description="烛龙，已拥有令人惧怕的掌控力。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103222" upgradeLevelNum="30" addHitPet="5"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">

		<data petLevel="1" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="75000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="21" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="22" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="23" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="24" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="25" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="26" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="27" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="28" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="29" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="30" experienceVolume="500000" essentialVolume="1000" />
	
		
		
		
		
		
		
	</item>
		<item id="10400322" className="Pet_ZhuLong_3" name="烛龙王" level="3" petSeries="ZhuLong" promoteXMLId="17"  initPassiveSkillNum="5"  
		maxLevel="3" description="烛龙王，上古神兽，神仙魔三界唯命是从。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103322" upgradeLevelNum="30" addHitPet="6"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">

		
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1520" />
		<data petLevel="21" experienceVolume="2000000" essentialVolume="1540" />
		<data petLevel="22" experienceVolume="2200000" essentialVolume="1560" />
		<data petLevel="23" experienceVolume="2400000" essentialVolume="1580" />
		<data petLevel="24" experienceVolume="2600000" essentialVolume="1600" />
		<data petLevel="25" experienceVolume="2800000" essentialVolume="1620" />
		<data petLevel="26" experienceVolume="3000000" essentialVolume="1640" />
		<data petLevel="27" experienceVolume="3200000" essentialVolume="1660" />
		<data petLevel="28" experienceVolume="3400000" essentialVolume="1680" />
		<data petLevel="29" experienceVolume="3600000" essentialVolume="1700" />
		<data petLevel="30" experienceVolume="3800000" essentialVolume="1720" />
		
	
		
	</item>
	<item id="10400423" className="Pet_ZhuLong_4" name="烛龙尊者" level="4" petSeries="ZhuLong" promoteXMLId="18" initPassiveSkillNum="1"  
		maxLevel="4" description="烛龙尊者，上古神兽之力掌控天下，天地之间唯我独尊。" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103123" upgradeLevelNum="70" addHitPet="10"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		
		

		<data petLevel="1" experienceVolume="4000000" essentialVolume="1740" />
		<data petLevel="2" experienceVolume="4200000" essentialVolume="1760" />
		<data petLevel="3" experienceVolume="4400000" essentialVolume="1780" />
		<data petLevel="4" experienceVolume="4600000" essentialVolume="1800" />
		<data petLevel="5" experienceVolume="4800000" essentialVolume="1820" />
		<data petLevel="6" experienceVolume="5000000" essentialVolume="1840" />
		<data petLevel="7" experienceVolume="5200000" essentialVolume="1860" />
		<data petLevel="8" experienceVolume="5400000" essentialVolume="1880" />
		<data petLevel="9" experienceVolume="5600000" essentialVolume="1900" />
		<data petLevel="10" experienceVolume="5800000" essentialVolume="1920" />
		<data petLevel="11" experienceVolume="6000000" essentialVolume="1940" />
		<data petLevel="12" experienceVolume="6200000" essentialVolume="1960" />
		<data petLevel="13" experienceVolume="6400000" essentialVolume="1980" />
		<data petLevel="14" experienceVolume="6600000" essentialVolume="2000" />
		<data petLevel="15" experienceVolume="6800000" essentialVolume="2050" />
		<data petLevel="16" experienceVolume="7000000" essentialVolume="2100" />
		<data petLevel="17" experienceVolume="7200000" essentialVolume="2200" />
		<data petLevel="18" experienceVolume="7400000" essentialVolume="2300" />
		<data petLevel="19" experienceVolume="7600000" essentialVolume="2400" />
		<data petLevel="20" experienceVolume="7800000" essentialVolume="2500" />
		<data petLevel="21" experienceVolume="8000000" essentialVolume="2550" />
		<data petLevel="22" experienceVolume="8200000" essentialVolume="2600" />
		<data petLevel="23" experienceVolume="8400000" essentialVolume="2650" />
		<data petLevel="24" experienceVolume="8600000" essentialVolume="2700" />
		<data petLevel="25" experienceVolume="8800000" essentialVolume="2750" />
		<data petLevel="26" experienceVolume="9000000" essentialVolume="2800" />
		<data petLevel="27" experienceVolume="9200000" essentialVolume="2850" />
		<data petLevel="28" experienceVolume="9400000" essentialVolume="2900" />
		<data petLevel="29" experienceVolume="9600000" essentialVolume="2950" />
		<data petLevel="30" experienceVolume="9800000" essentialVolume="3000" />
		<data petLevel="31" experienceVolume="10000000" essentialVolume="3050" />
		<data petLevel="32" experienceVolume="10200000" essentialVolume="3100" />
		<data petLevel="33" experienceVolume="10400000" essentialVolume="3150" />
		<data petLevel="34" experienceVolume="10600000" essentialVolume="3200" />
		<data petLevel="35" experienceVolume="10800000" essentialVolume="3250" />
		<data petLevel="36" experienceVolume="11000000" essentialVolume="3300" />
		<data petLevel="37" experienceVolume="11200000" essentialVolume="3350" />
		<data petLevel="38" experienceVolume="11400000" essentialVolume="3400" />
		<data petLevel="39" experienceVolume="11600000" essentialVolume="3450" />
		<data petLevel="40" experienceVolume="11800000" essentialVolume="3500" />
		<data petLevel="41" experienceVolume="12000000" essentialVolume="3500" />
		<data petLevel="42" experienceVolume="12200000" essentialVolume="3500" />
		<data petLevel="43" experienceVolume="12400000" essentialVolume="3500" />
		<data petLevel="44" experienceVolume="12600000" essentialVolume="3500" />
		<data petLevel="45" experienceVolume="12800000" essentialVolume="3500" />
		<data petLevel="46" experienceVolume="13000000" essentialVolume="3500" />
		<data petLevel="47" experienceVolume="13200000" essentialVolume="3500" />
		<data petLevel="48" experienceVolume="13400000" essentialVolume="3500" />
		<data petLevel="49" experienceVolume="13600000" essentialVolume="3500" />
		<data petLevel="50" experienceVolume="13800000" essentialVolume="3500" />
		<data petLevel="51" experienceVolume="14000000" essentialVolume="4000" />
		<data petLevel="52" experienceVolume="14200000" essentialVolume="4000" />
		<data petLevel="53" experienceVolume="14400000" essentialVolume="4000" />
		<data petLevel="54" experienceVolume="14600000" essentialVolume="4000" />
		<data petLevel="55" experienceVolume="14800000" essentialVolume="4000" />
		<data petLevel="56" experienceVolume="15000000" essentialVolume="4000" />
		<data petLevel="57" experienceVolume="15200000" essentialVolume="4000" />
		<data petLevel="58" experienceVolume="15400000" essentialVolume="4000" />
		<data petLevel="59" experienceVolume="15600000" essentialVolume="4000" />
		<data petLevel="60" experienceVolume="15800000" essentialVolume="4500" />
		<data petLevel="61" experienceVolume="16000000" essentialVolume="4500" />
		<data petLevel="62" experienceVolume="16200000" essentialVolume="4500" />
		<data petLevel="63" experienceVolume="16400000" essentialVolume="4500" />
		<data petLevel="64" experienceVolume="16600000" essentialVolume="4500" />
		<data petLevel="65" experienceVolume="16800000" essentialVolume="4500" />
		<data petLevel="66" experienceVolume="17000000" essentialVolume="4500" />
		<data petLevel="67" experienceVolume="17200000" essentialVolume="4500" />
		<data petLevel="68" experienceVolume="17400000" essentialVolume="4500" />
		<data petLevel="69" experienceVolume="17600000" essentialVolume="4500" />
		<data petLevel="70" experienceVolume="17800000" essentialVolume="4500" />
		
		
		
<!--awakeSkill 觉醒技能   -->		

		<awakeSkill id="petAwakePassive_GaoJiTiZzhi_1" />
	</item>

	<item id="10400124" className="Pet_QiongQi_1" name="穷奇幼崽" level="1" petSeries="QiongQi" promoteXMLId="19" initPassiveSkillNum="5" 
		maxLevel="3" description="穷奇幼年形态，已初现王者之风！" price="10000" owner=""
		equipmentType="pet" activeSkill="10103124" upgradeLevelNum="30" addHitPet="4"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		<data petLevel="1" experienceVolume="50" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="100" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="200" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="300" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="500" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="800" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="1000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="1500" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="2000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="3000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="4000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="5000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="8000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="10000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="13000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="15000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="18000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="22000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="21" experienceVolume="20000" essentialVolume="1000" />
		<data petLevel="22" experienceVolume="23000" essentialVolume="1000" />
		<data petLevel="23" experienceVolume="25000" essentialVolume="1000" />
		<data petLevel="24" experienceVolume="28000" essentialVolume="1000" />
		<data petLevel="25" experienceVolume="30000" essentialVolume="1000" />
		<data petLevel="26" experienceVolume="33000" essentialVolume="1000" />
		<data petLevel="27" experienceVolume="35000" essentialVolume="1000" />
		<data petLevel="28" experienceVolume="38000" essentialVolume="1000" />
		<data petLevel="29" experienceVolume="40000" essentialVolume="1000" />
		<data petLevel="30" experienceVolume="43000" essentialVolume="1000" />
		
	
	</item>
		<item id="10400224" className="Pet_QiongQi_2" name="穷奇" level="2" petSeries="QiongQi" promoteXMLId="19"  initPassiveSkillNum="5" 
		maxLevel="3" description="穷奇，已拥有令人惧怕的掌控力。" price="50000" owner=""
		equipmentType="pet" activeSkill="10103224" upgradeLevelNum="30" addHitPet="5"
		isAbleSell="0" messageBoxColor="0xa31ec4" isAbleAKeySell="0">

		<data petLevel="1" experienceVolume="45000" essentialVolume="1000" />
		<data petLevel="2" experienceVolume="48000" essentialVolume="1000" />
		<data petLevel="3" experienceVolume="50000" essentialVolume="1000" />
		<data petLevel="4" experienceVolume="53000" essentialVolume="1000" />
		<data petLevel="5" experienceVolume="55000" essentialVolume="1000" />
		<data petLevel="6" experienceVolume="58000" essentialVolume="1000" />
		<data petLevel="7" experienceVolume="60000" essentialVolume="1000" />
		<data petLevel="8" experienceVolume="65000" essentialVolume="1000" />
		<data petLevel="9" experienceVolume="70000" essentialVolume="1000" />
		<data petLevel="10" experienceVolume="75000" essentialVolume="1000" />
		<data petLevel="11" experienceVolume="80000" essentialVolume="1000" />
		<data petLevel="12" experienceVolume="85000" essentialVolume="1000" />
		<data petLevel="13" experienceVolume="90000" essentialVolume="1000" />
		<data petLevel="14" experienceVolume="95000" essentialVolume="1000" />
		<data petLevel="15" experienceVolume="100000" essentialVolume="1000" />
		<data petLevel="16" experienceVolume="105000" essentialVolume="1000" />
		<data petLevel="17" experienceVolume="110000" essentialVolume="1000" />
		<data petLevel="18" experienceVolume="115000" essentialVolume="1000" />
		<data petLevel="19" experienceVolume="120000" essentialVolume="1000" />
		<data petLevel="20" experienceVolume="125000" essentialVolume="1000" />
		<data petLevel="21" experienceVolume="130000" essentialVolume="1000" />
		<data petLevel="22" experienceVolume="135000" essentialVolume="1000" />
		<data petLevel="23" experienceVolume="140000" essentialVolume="1000" />
		<data petLevel="24" experienceVolume="145000" essentialVolume="1000" />
		<data petLevel="25" experienceVolume="150000" essentialVolume="1000" />
		<data petLevel="26" experienceVolume="160000" essentialVolume="1000" />
		<data petLevel="27" experienceVolume="170000" essentialVolume="1000" />
		<data petLevel="28" experienceVolume="200000" essentialVolume="1000" />
		<data petLevel="29" experienceVolume="300000" essentialVolume="1000" />
		<data petLevel="30" experienceVolume="500000" essentialVolume="1000" />
	
		
		
		
		
		
		
	</item>
		<item id="10400324" className="Pet_QiongQi_3" name="穷奇王" level="3" petSeries="QiongQi" promoteXMLId="19"  initPassiveSkillNum="5"  
		maxLevel="3" description="穷奇王，上古神兽，神仙魔三界唯命是从。" price="100000" owner=""
		equipmentType="pet" activeSkill="10103324" upgradeLevelNum="30" addHitPet="6"
		isAbleSell="0" messageBoxColor="0xfd8602" isAbleAKeySell="0">

		
		<data petLevel="1" experienceVolume="550000" essentialVolume="1020" />
		<data petLevel="2" experienceVolume="560000" essentialVolume="1040" />
		<data petLevel="3" experienceVolume="570000" essentialVolume="1060" />
		<data petLevel="4" experienceVolume="580000" essentialVolume="1080" />
		<data petLevel="5" experienceVolume="590000" essentialVolume="1120" />
		<data petLevel="6" experienceVolume="600000" essentialVolume="1140" />
		<data petLevel="7" experienceVolume="610000" essentialVolume="1160" />
		<data petLevel="8" experienceVolume="620000" essentialVolume="1180" />
		<data petLevel="9" experienceVolume="630000" essentialVolume="1200" />
		<data petLevel="10" experienceVolume="640000" essentialVolume="1220" />
		<data petLevel="11" experienceVolume="750000" essentialVolume="1240" />
		<data petLevel="12" experienceVolume="850000" essentialVolume="1260" />
		<data petLevel="13" experienceVolume="950000" essentialVolume="1280" />
		<data petLevel="14" experienceVolume="1050000" essentialVolume="1300" />
		<data petLevel="15" experienceVolume="1150000" essentialVolume="1330" />
		<data petLevel="16" experienceVolume="1250000" essentialVolume="1360" />
		<data petLevel="17" experienceVolume="1350000" essentialVolume="1390" />
		<data petLevel="18" experienceVolume="1450000" essentialVolume="1420" />
		<data petLevel="19" experienceVolume="1550000" essentialVolume="1450" />
		<data petLevel="20" experienceVolume="1650000" essentialVolume="1520" />
		<data petLevel="21" experienceVolume="2000000" essentialVolume="1540" />
		<data petLevel="22" experienceVolume="2200000" essentialVolume="1560" />
		<data petLevel="23" experienceVolume="2400000" essentialVolume="1580" />
		<data petLevel="24" experienceVolume="2600000" essentialVolume="1600" />
		<data petLevel="25" experienceVolume="2800000" essentialVolume="1620" />
		<data petLevel="26" experienceVolume="3000000" essentialVolume="1640" />
		<data petLevel="27" experienceVolume="3200000" essentialVolume="1660" />
		<data petLevel="28" experienceVolume="3400000" essentialVolume="1680" />
		<data petLevel="29" experienceVolume="3600000" essentialVolume="1700" />
		<data petLevel="30" experienceVolume="3800000" essentialVolume="1720" />
		
	
		
	</item>
	<item id="10400425" className="Pet_QiongQi_4" name="穷奇尊者" level="4" petSeries="QiongQi" promoteXMLId="20" initPassiveSkillNum="1"  
		maxLevel="4" description="穷奇尊者，上古神兽之力掌控天下，天地之间唯我独尊。" price="10000000" owner=""  petType="advancePet" 
		equipmentType="pet" activeSkill="10103125" upgradeLevelNum="70" addHitPet="10"
		isAbleSell="0" messageBoxColor="0x0389c2" isAbleAKeySell="0">
		
		

		<data petLevel="1" experienceVolume="4000000" essentialVolume="1740" />
		<data petLevel="2" experienceVolume="4200000" essentialVolume="1760" />
		<data petLevel="3" experienceVolume="4400000" essentialVolume="1780" />
		<data petLevel="4" experienceVolume="4600000" essentialVolume="1800" />
		<data petLevel="5" experienceVolume="4800000" essentialVolume="1820" />
		<data petLevel="6" experienceVolume="5000000" essentialVolume="1840" />
		<data petLevel="7" experienceVolume="5200000" essentialVolume="1860" />
		<data petLevel="8" experienceVolume="5400000" essentialVolume="1880" />
		<data petLevel="9" experienceVolume="5600000" essentialVolume="1900" />
		<data petLevel="10" experienceVolume="5800000" essentialVolume="1920" />
		<data petLevel="11" experienceVolume="6000000" essentialVolume="1940" />
		<data petLevel="12" experienceVolume="6200000" essentialVolume="1960" />
		<data petLevel="13" experienceVolume="6400000" essentialVolume="1980" />
		<data petLevel="14" experienceVolume="6600000" essentialVolume="2000" />
		<data petLevel="15" experienceVolume="6800000" essentialVolume="2050" />
		<data petLevel="16" experienceVolume="7000000" essentialVolume="2100" />
		<data petLevel="17" experienceVolume="7200000" essentialVolume="2200" />
		<data petLevel="18" experienceVolume="7400000" essentialVolume="2300" />
		<data petLevel="19" experienceVolume="7600000" essentialVolume="2400" />
		<data petLevel="20" experienceVolume="7800000" essentialVolume="2500" />
		<data petLevel="21" experienceVolume="8000000" essentialVolume="2550" />
		<data petLevel="22" experienceVolume="8200000" essentialVolume="2600" />
		<data petLevel="23" experienceVolume="8400000" essentialVolume="2650" />
		<data petLevel="24" experienceVolume="8600000" essentialVolume="2700" />
		<data petLevel="25" experienceVolume="8800000" essentialVolume="2750" />
		<data petLevel="26" experienceVolume="9000000" essentialVolume="2800" />
		<data petLevel="27" experienceVolume="9200000" essentialVolume="2850" />
		<data petLevel="28" experienceVolume="9400000" essentialVolume="2900" />
		<data petLevel="29" experienceVolume="9600000" essentialVolume="2950" />
		<data petLevel="30" experienceVolume="9800000" essentialVolume="3000" />
		<data petLevel="31" experienceVolume="10000000" essentialVolume="3050" />
		<data petLevel="32" experienceVolume="10200000" essentialVolume="3100" />
		<data petLevel="33" experienceVolume="10400000" essentialVolume="3150" />
		<data petLevel="34" experienceVolume="10600000" essentialVolume="3200" />
		<data petLevel="35" experienceVolume="10800000" essentialVolume="3250" />
		<data petLevel="36" experienceVolume="11000000" essentialVolume="3300" />
		<data petLevel="37" experienceVolume="11200000" essentialVolume="3350" />
		<data petLevel="38" experienceVolume="11400000" essentialVolume="3400" />
		<data petLevel="39" experienceVolume="11600000" essentialVolume="3450" />
		<data petLevel="40" experienceVolume="11800000" essentialVolume="3500" />
		<data petLevel="41" experienceVolume="12000000" essentialVolume="3500" />
		<data petLevel="42" experienceVolume="12200000" essentialVolume="3500" />
		<data petLevel="43" experienceVolume="12400000" essentialVolume="3500" />
		<data petLevel="44" experienceVolume="12600000" essentialVolume="3500" />
		<data petLevel="45" experienceVolume="12800000" essentialVolume="3500" />
		<data petLevel="46" experienceVolume="13000000" essentialVolume="3500" />
		<data petLevel="47" experienceVolume="13200000" essentialVolume="3500" />
		<data petLevel="48" experienceVolume="13400000" essentialVolume="3500" />
		<data petLevel="49" experienceVolume="13600000" essentialVolume="3500" />
		<data petLevel="50" experienceVolume="13800000" essentialVolume="3500" />
		<data petLevel="51" experienceVolume="14000000" essentialVolume="4000" />
		<data petLevel="52" experienceVolume="14200000" essentialVolume="4000" />
		<data petLevel="53" experienceVolume="14400000" essentialVolume="4000" />
		<data petLevel="54" experienceVolume="14600000" essentialVolume="4000" />
		<data petLevel="55" experienceVolume="14800000" essentialVolume="4000" />
		<data petLevel="56" experienceVolume="15000000" essentialVolume="4000" />
		<data petLevel="57" experienceVolume="15200000" essentialVolume="4000" />
		<data petLevel="58" experienceVolume="15400000" essentialVolume="4000" />
		<data petLevel="59" experienceVolume="15600000" essentialVolume="4000" />
		<data petLevel="60" experienceVolume="15800000" essentialVolume="4500" />
		<data petLevel="61" experienceVolume="16000000" essentialVolume="4500" />
		<data petLevel="62" experienceVolume="16200000" essentialVolume="4500" />
		<data petLevel="63" experienceVolume="16400000" essentialVolume="4500" />
		<data petLevel="64" experienceVolume="16600000" essentialVolume="4500" />
		<data petLevel="65" experienceVolume="16800000" essentialVolume="4500" />
		<data petLevel="66" experienceVolume="17000000" essentialVolume="4500" />
		<data petLevel="67" experienceVolume="17200000" essentialVolume="4500" />
		<data petLevel="68" experienceVolume="17400000" essentialVolume="4500" />
		<data petLevel="69" experienceVolume="17600000" essentialVolume="4500" />
		<data petLevel="70" experienceVolume="17800000" essentialVolume="4500" />
		
		
		
<!--awakeSkill 觉醒技能   -->		

		<awakeSkill id="petAwakePassive_GaoJiTiZzhi_1" />
	</item>
</data>