<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet13" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet13Idle" />
		<walk defId="pet13Walk" />
		<run defId="pet13Run" />
		<attack defId="pet13Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet13Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet13Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	   <skill id="Skill_pet13Skill" className="YJFY.Skill.PetSkills.Skill_Pet13Skill" x="-480"
			y="-280" z="-1" xRange="960" yRange="560" zRange="100" bodyDefId="pet13SkillBodyShow" bodyAttackReachFrameLabel="skillAttackReach" 
			bodySkillEndFrameLabel="skillEnd^stop^"  everyEntityAddShowDefId=""  everyEntityAddShowIsFrontOfBody="0"  hurtDuration="3000"
			 createRandomShowInterval="200" randomEntityShowId="pet13SkillYu" addEffectOnRandomEntitysId="pet13SkillYuDaoDaEffect" 
			addGroundEffectOneRandomEntityId="pet13SkillGroundEffect" effectAddtoTargetId="" randomEntityReachGroundFrameLabel="shanDianreachGround">
			<animationDefinition id="pet13SkillGroundEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetSkill13Effect_FlashGroundEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet13SkillYuDaoDaEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetSkill13Effect_FlashBeAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet13SkillYu" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetSkill13Effect_FlashAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<shakeView swfPath="NewGameFolder/PetSource/Pet13All.swf" className="PetSkill13ShakeView" />
			<flashView swfPath="NewGameFolder/PetSource/Pet13All.swf" className="PetSkill13FlashView" />
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet13Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetStand13_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet13Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetWalk13_4" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet13SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetSkill13AttackAnimation" x_offset="0" y_offset="0" />
			</animationDefinition>
		
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			
			<animationDefinition id="pet13SkillGroundEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetSkill13Effect_FlashGroundEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet13SkillYuDaoDaEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetSkill13Effect_FlashBeAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet13SkillYu" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet13All.swf"
					showClass="PetSkill13Effect_FlashAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			
		</shows>

	</animal>
</data>
