<?xml version="1.0" encoding="utf-8" ?>
<data rePowerPointTicket="10" rePowerPointTicketId="983" proAttackValue="0.1" proAttackPointTickets="3_6_9_12_15_18_21_24_27_30" proAttackPointTicketIds="973_974_975_976_977_978_979_980_981_982">
    <playerShow>
		<player playerType="SunWuKong" swf="SunWuKongCardShow.swf"  className="SunWuKongCardShow" />
		<player playerType="BaiLongMa" swf="BaiLongMaCardShow.swf"  className="BaiLongMaCarShow" />
		<player playerType="ErLangShen" swf="ErLangShenCardShow.swf"  className="ErLangShenCardShow" />
		<player playerType="ChangE" swf="ChangECardShow.swf"  className="ChangECardShow" />
		<player playerType="Fox" swf="FoxCardShow.swf"  className="FoxCardShow" />
		<player playerType="TieShan" swf="TieShanCardShow.swf"  className="TieShanCardShow" />
		<player playerType="Houyi" swf="HouyiCardShow.swf"  className="HouyiCardShow" />
		<player playerType="ZiXia" swf="ZiXiaCardShow.swf"  className="ZiXiaCardShow" />
	</playerShow>
	<playerViewData>
		<shakeView className="UI.WorldBoss.ShakeView.ShakeView1" >
		  <att attName="setTimes" attValue="2" />
		  <att attName="setOffset" attValue="1" />
		  <att attName="setSpeed" attValue="32" />
		</shakeView>
		<flashView className="UI.WorldBoss.FlashView.FlashView1" >
		  <att attName="setColor" attValue="0x999999" />
		  <att attName="setTintAmount" attValue="0.1" />
		  <att attName="setAppearDuration" attValue="0.1" />
		  <att attName="setDisappearDuration" attValue="0.5" />
		</flashView>
	</playerViewData>
	<petViewData>
		<shakeView className="UI.WorldBoss.ShakeView.ShakeView1" >
		  <att attName="setTimes" attValue="2" />
		  <att attName="setOffset" attValue="1" />
		  <att attName="setSpeed" attValue="32" />
		</shakeView>
		<flashView className="UI.WorldBoss.FlashView.FlashView1" >
		  <att attName="setColor" attValue="0xff33cc" />
		  <att attName="setTintAmount" attValue="0.1" />
		  <att attName="setAppearDuration" attValue="0.1" />
		  <att attName="setDisappearDuration" attValue="0.5" />
		</flashView>
	</petViewData>
	<petShow>
		<!--肚皮龙-->
		<pet petClassName="Pet_Tyrannosaurs_1" swf="PetCardShow.swf"  className="Pet_Tyrannosaurs_1" />
		<pet petClassName="Pet_Tyrannosaurs_2" swf="PetCardShow.swf"  className="Pet_Tyrannosaurs_2" />
		<pet petClassName="Pet_Tyrannosaurs_3" swf="PetCardShow.swf"  className="Pet_Tyrannosaurs_3" />
		<!--雷精-->
		<pet petClassName="Pet_XiaoPiKaQiu_1" swf="PetCardShow.swf"  className="Pet_XiaoPiKaQiu_1" />
		<pet petClassName="Pet_XiaoPiKaQiu_2" swf="PetCardShow.swf"  className="Pet_XiaoPiKaQiu_2" />
		<pet petClassName="Pet_XiaoPiKaQiu_3" swf="PetCardShow.swf"  className="Pet_XiaoPiKaQiu_3" />
		<!--小石怪-->
		<pet petClassName="Pet_ShiTouRen_1" swf="PetCardShow.swf"  className="Pet_ShiTouRen_1" />
		<pet petClassName="Pet_ShiTouRen_2" swf="PetCardShow.swf"  className="Pet_ShiTouRen_2" />
		<pet petClassName="Pet_ShiTouRen_3" swf="PetCardShow.swf"  className="Pet_ShiTouRen_3" />
		<!--小萌火-->
		<pet petClassName="Pet_HuoRen_1" swf="PetCardShow.swf"  className="Pet_HuoRen_1" />
		<pet petClassName="Pet_HuoRen_2" swf="PetCardShow.swf"  className="Pet_HuoRen_2" />
		<pet petClassName="Pet_HuoRen_3" swf="PetCardShow.swf"  className="Pet_HuoRen_3" />
		<!--机械宝宝-->
		<pet petClassName="Pet_JiQiRen_1" swf="PetCardShow.swf"  className="Pet_JiQiRen_1" />
		<pet petClassName="Pet_JiQiRen_2" swf="PetCardShow.swf"  className="Pet_JiQiRen_2" />
		<pet petClassName="Pet_JiQiRen_3" swf="PetCardShow.swf"  className="Pet_JiQiRen_3" />
		<!--蛋壳龙-->
		<pet petClassName="Pet_BingLong_1" swf="PetCardShow.swf"  className="Pet_BingLong_1" />
		<pet petClassName="Pet_BingLong_2" swf="PetCardShow.swf"  className="Pet_BingLong_2" />
		<pet petClassName="Pet_BingLong_3" swf="PetCardShow.swf"  className="Pet_BingLong_3" />
		<!--礼盒宝宝-->
		<pet petClassName="Pet_LiHe_1" swf="PetCardShow.swf"  className="Pet_LiHe_1" />
		<pet petClassName="Pet_LiHe_2" swf="PetCardShow.swf"  className="Pet_LiHe_2" />
		<pet petClassName="Pet_LiHe_3" swf="PetCardShow.swf"  className="Pet_LiHe_3" />
		<pet petClassName="Pet_LiHe_4" swf="PetCardShow.swf"  className="Pet_LiHe_4" />
		<!--剑--> 
		<pet petClassName="Pet_Jian_1" swf="PetCardShow.swf"  className="Pet_Jian_1" />
		<pet petClassName="Pet_Jian_2" swf="PetCardShow.swf"  className="Pet_Jian_2" />
		<pet petClassName="Pet_Jian_3" swf="PetCardShow.swf"  className="Pet_Jian_3" />
		<pet petClassName="Pet_Jian_4" swf="PetCardShow.swf"  className="Pet_Jian_4" />

		
		<pet petClassName="Pet_Lu_1" swf="PetCardShow.swf"  className="Pet_Lu_1" />
		<pet petClassName="Pet_Lu_2" swf="PetCardShow.swf"  className="Pet_Lu_2" />
		<pet petClassName="Pet_Lu_3" swf="PetCardShow.swf"  className="Pet_Lu_3" />
		<pet petClassName="Pet_TianShiLu_4" swf="PetCardShow.swf"  className="Pet_TianShiLu_4" />
		<pet petClassName="Pet_EMoLu_4" swf="PetCardShow.swf"  className="Pet_EMoLu_4" />
		
		
		<!--神龙剑士-->
		<pet petClassName="Pet_RenXingBingLong_4" swf="PetCardShow.swf"  className="Pet_RenXingBingLong_4" />
		<!--巨岩战将-->
		<pet petClassName="Pet_JuYanZhanJiang_4" swf="PetCardShow.swf"  className="Pet_JuYanZhanJiang_4" />
		<!--撼地巨龙-->
		<pet petClassName="Pet_HanDiJuLong_4" swf="PetCardShow.swf"  className="Pet_HanDiJuLong_4" />
		
		<pet petClassName="Pet_FengHuang_1" swf="PetCardShow.swf"  className="Pet_BingJing_1" />
		<pet petClassName="Pet_FengHuang_2" swf="PetCardShow.swf"  className="Pet_BingJing_2" />
		<pet petClassName="Pet_FengHuang_3" swf="PetCardShow.swf"  className="Pet_BingJing_3" />
		<pet petClassName="Pet_FengHuang_4" swf="PetCardShow.swf"  className="Pet_BingJing_4" />
		
		<!--重睛鸟-->
		
		
		<pet petClassName="Pet_ChongMing_1" swf="PetCardShow.swf"  className="Pet_ChongMing_1" />
		<pet petClassName="Pet_ChongMing_2" swf="PetCardShow.swf"  className="Pet_ChongMing_2" />
		<pet petClassName="Pet_ChongMing_3" swf="PetCardShow.swf"  className="Pet_ChongMing_3" />
		<pet petClassName="Pet_ChongMing_4" swf="PetCardShow.swf"  className="Pet_ChongMing_4" />
		
			<!--魔龙-->
		
		
		<pet petClassName="Pet_MoLong_1" swf="PetCardShow.swf"  className="Pet_MoLong_1" />
		<pet petClassName="Pet_MoLong_2" swf="PetCardShow.swf"  className="Pet_MoLong_2" />
		<pet petClassName="Pet_MoLong_3" swf="PetCardShow.swf"  className="Pet_MoLong_3" />
		<pet petClassName="Pet_MoLong_4" swf="PetCardShow.swf"  className="Pet_MoLong_4" />
		<!--烛龙-->
		<pet petClassName="Pet_ZhuLong_1" swf="PetCardShow.swf"  className="Pet_ZhuLong_1" />
		<pet petClassName="Pet_ZhuLong_2" swf="PetCardShow.swf"  className="Pet_ZhuLong_2" />
		<pet petClassName="Pet_ZhuLong_3" swf="PetCardShow.swf"  className="Pet_ZhuLong_3" />
		<pet petClassName="Pet_ZhuLong_4" swf="PetCardShow.swf"  className="Pet_ZhuLong_4" />
		<!--穷奇-->
		<pet petClassName="Pet_QiongQi_1" swf="PetCardShow.swf"  className="Pet_QiongQi_1" />
		<pet petClassName="Pet_QiongQi_2" swf="PetCardShow.swf"  className="Pet_QiongQi_2" />
		<pet petClassName="Pet_QiongQi_3" swf="PetCardShow.swf"  className="Pet_QiongQi_3" />
		<pet petClassName="Pet_QiongQi_4" swf="PetCardShow.swf"  className="Pet_QiongQi_4" />
	</petShow>
	<rank rankId1="733" rankId2="734" effectiveTime="2013-10-21 00:00:00" reSetTimeLong="168"/>
	<rank rankId1="1559" rankId2="1558" effectiveTime="2015-08-24 00:00:00" reSetTimeLong="168"/>
	<rank rankId1="1561" rankId2="1560" effectiveTime="2015-08-24 00:00:00" reSetTimeLong="168"/>
	
	<Boss>
		
		<boss id="boss1" xmlPath="UIData/worldBoss/bosses/boss1.xml" className="UI.WorldBoss.Boss.AbleAttackedBoss.AttackUpgradeBoss" lostPowerValue="60" 
		gNAEclassName="UI.WorldBoss.GetNextStepActionEntities.GetNextStepActionEntities1" startX="1" startY="2">
			<!--<rank rankId1="733" rankId2="734" effectiveTime="2013-10-21 00:00:00" />-->
			<rewards>
			 <reward str="星期一">
				 <equipment id="11000002" num="1" />
				 <!--小经验丹-->
			 </reward>
			 <reward str="星期二">
				 <equipment id="11000002" num="1" />
				 <equipment id="11000005" num="1" />
				 <!--小经验丹-->
			 </reward>
			 <reward str="星期三">
				 <equipment id="11000004" num="1" />
				 <equipment id="11000018" num="1" />
			 </reward>
			 <reward str="星期四">
				 <equipment id="11900010" num="1" />
				 <equipment id="11900015" num="1" />
			 </reward>
			 <reward str="星期五">
				 <equipment id="10500030" num="1" />
				 <equipment id="11000002" num="1" />
			 </reward>
			 <reward str="星期六">
				 <equipment id="11100000" num="1" />
				 <equipment id="11000002" num="1" />
			 </reward>
			 <reward str="星期日">
				 <equipment id="11000019" num="1" />
				 <equipment id="11000013" num="1" />
			 </reward>
		 </rewards>
		</boss>
		<boss id="boss2" xmlPath="UIData/worldBoss/bosses/boss2.xml" className="UI.WorldBoss.Boss.CreateOwnImageBoss.CreateOwnImageBoss" lostPowerValue="60" 
		gNAEclassName="UI.WorldBoss.GetNextStepActionEntities.GetNextStepActionEntites2" startX="2" startY="2">
			<!--<rank rankId1="733" rankId2="734" effectiveTime="2013-10-21 00:00:00" />-->
			<rewards>
			 <reward str="星期一">
				 <equipment id="10800099" num="1" />
				 <!--小经验丹-->
			 </reward>
			 <reward str="星期二">
				 <equipment id="10800099" num="1" />
				 <equipment id="11000005" num="1" />
				 <!--小经验丹-->
			 </reward>
			 <reward str="星期三">
				 <equipment id="10800099" num="1" />
				 <equipment id="11000018" num="1" />
			 </reward>
			 <reward str="星期四">
				 <equipment id="10800099" num="1" />
				 <equipment id="11000006" num="1" />
			 </reward>
			 <reward str="星期五">
				 <equipment id="11000003" num="1" />
				 <equipment id="11000006" num="1" />
			 </reward>
			 <reward str="星期六">
				 <equipment id="11100000" num="1" />
				 <equipment id="10500033" num="1" />
			 </reward>
			 <reward str="星期日">
				 <equipment id="11000019" num="1" />
				 <equipment id="10500034" num="1" />
			 </reward>
		 </rewards>
		</boss>
	</Boss>
</data>