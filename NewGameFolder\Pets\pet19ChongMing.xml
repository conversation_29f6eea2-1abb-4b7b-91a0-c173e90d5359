<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet19" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="petIdle" />
		<walk defId="petWalk" />
		<run defId="petRun" />
		<attack defId="petAttack"  />
		<hurt defId="petHurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="petDie">
			<attackSourceData entityId="" skillId="" />
		</die>
	   
		<sound>
			
		</sound>
         <!--宠物技能1-->
	   <skill id="Skill_Pet18Skill" 
	          className="YJFY.Skill.PetSkills.Skill_Pet18Skill"
	           bodyDefId="petSkillBodyShow"
			  x="-480" 
			   y="-280" 
			   z="-1" 
			   xRange="960" 
			   yRange="600" 
			   zRange="500" 
			   bodyAttackReachFrameLabel="down" 
			   everyEntityAddShowDefId="changeStart" 
			   bodySkillEndFrameLabel="end^stop^" 
			   releaseSkillFrameLabel="disappearEnd^stop^"
			   attackReachFrameLabel="skillAttackReach" 
			   skillShowEndFrameLabel="skillShowEnd^stop^"  
			   skillEndFrameLabel="appearEnd^stop^"
			   appearBodyShowDefId="petSkill5BodyAppear" 
			   frontSkillShowDefId="petSkill5Effect" 
			   >
			<animationDefinition id="changeStart" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet19All.swf"
					showClass="ChangeStart" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>



		<animationDefinitions>
			<animationDefinition id="petIdle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet19All.swf"
					showClass="PetStand" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petWalk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet19All.swf"
					showClass="PetWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="petSkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet19All.swf"
					showClass="PetDaZhao_Disappear" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petSkill5Effect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet19All.swf"
					showClass="PetSkill" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petSkill5BodyAppear" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet19All.swf"
					showClass="PetDaZhao_Appear" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			
			
			
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			
		</shows>

	</animal>
</data>
