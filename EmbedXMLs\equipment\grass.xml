<?xml version="1.0" encoding="utf-8" ?>
<data>
<!-- 植物 -->
   <!-- harvestMinNum, harvestMaxNum： 收获是能获取到的最小，最大数量，  getSunMinNum, getSunMaxNum:收获是能得到的阳光数量
        lossLandValue:对土地的损害值,(及收获后土地在没有其他因素的影响下的回复时间将会是 lossLandValue * １分钟 )
		needSun： 种植该作物所需的阳光等级 
		
		converValue: 该草炼制成converTarget的值（成功率）  -->
   <item id="11200000" className="Plant_PuTongCaoYao" name="普通草药" level="0" description="漫山遍野都是这种草药。(用于在炼丹炉中炼制丹药)" price="5000" ticketPrice="0" owner=""         equipmentType="grass"  maxSuperposition="20" isAbleSell="1" messageBoxColor="0xffffff" isAbleAKeySell="0" harvestMinNum="1"         harvestMaxNum="3"  getSunMinNum="20" getSunMaxNum="40"  lossLandValue="60" needSunLevel="0" >
		 
		 
		 <!-- name: 该阶段的名称 time:从种下到该阶段的时间， decription:该阶段的描述  className该阶段的对应植物的素材（图片）, state阶段-->
		 <growData name="种子" time="0" description="点击清除" className="PuTongCaoYao_Show_1" state="seedState"/>
		 <growData name="发芽（幼苗）" time="0.5" description="" className="PuTongCaoYao_Show_2" state="sproutState"/>
		 <growData name="成长（开花）" time="1.5"  description=""  className="PuTongCaoYao_Show_3" state="growthState"/>
		 <growData name="成熟（结果）" time="2.5" description="" className="PuTongCaoYao_Show_4" state="maturityState"/>
		 <growData name="枯萎（收获后）" description="点击清除"   className="PuTongCaoYao_Show_5" state="witherState"/>
		 
   </item>
   
     <item id="11200001" className="Plant_BaiNianCaoYao" name="百年草药" level="0" description="较为罕见的草药。(用于在炼丹炉中炼制丹药)" price="8000" ticketPrice="0" owner=""         equipmentType="grass"  maxSuperposition="20" isAbleSell="1" messageBoxColor="0x00ff00" isAbleAKeySell="0" harvestMinNum="1"         harvestMaxNum="3"  getSunMinNum="70" getSunMaxNum="80"  lossLandValue="60" needSunLevel="2" >
		 
		 
		 <!-- name: 该阶段的名称 time:从种下到该阶段的时间， decription:该阶段的描述  className该阶段的对应植物的素材（图片）, state阶段-->
		 <growData name="种子" time="0" description="" className="BaiNianCaoYao_Show_1" state="seedState"/>
		 <growData name="发芽（幼苗）" time="0.5" description="" className="BaiNianCaoYao_Show_2" state="sproutState"/>
		 <growData name="成长（开花）" time="1.5"  description=""  className="BaiNianCaoYao_Show_3" state="growthState"/>
		 <growData name="成熟（结果）" time="2.5" description="" className="BaiNianCaoYao_Show_4" state="maturityState"/>
		 <growData name="枯萎（收获后）" description="点击清除"   className="BaiNianCaoYao_Show_5" state="witherState"/>
		 
   </item>
   
   <item id="11200002" className="Plant_QianNianCaoYao" name="千年草药" level="0" description="据说用此草药可以医治百病。(用于在炼丹炉中炼制丹药)" price="12000" ticketPrice="0" owner=""         equipmentType="grass"  maxSuperposition="20" isAbleSell="1" messageBoxColor="0x0389c2" isAbleAKeySell="0" harvestMinNum="1"         harvestMaxNum="3"  getSunMinNum="80" getSunMaxNum="120"  lossLandValue="60" needSunLevel="5" >
		 
		 
		 <!-- name: 该阶段的名称 time:从种下到该阶段的时间， decription:该阶段的描述  className该阶段的对应植物的素材（图片）, state阶段-->
		 <growData name="种子" time="0" description="" className="QianNianCaoYao_Show_1" state="seedState"/>
		 <growData name="发芽（幼苗）" time="0.5" description="" className="QianNianCaoYao_Show_2" state="sproutState"/>
		 <growData name="成长（开花）" time="1.5"  description=""  className="QianNianCaoYao_Show_3" state="growthState"/>
		 <growData name="成熟（结果）" time="2.5" description="" className="QianNianCaoYao_Show_4" state="maturityState"/>
		 <growData name="枯萎（收获后）" description=""   className="QianNianCaoYao_Show_5" state="witherState"/>
		 
   </item>
     <item id="11200003" className="Plant_WangNianCaoYao" name="万年草药" level="0" description="此草药世间基本绝迹了。(用于在炼丹炉中炼制丹药)" price="18000" ticketPrice="0" owner=""         equipmentType="grass"  maxSuperposition="20" isAbleSell="1" messageBoxColor="0xa31ec4" isAbleAKeySell="0" harvestMinNum="1"         harvestMaxNum="2"  getSunMinNum="100" getSunMaxNum="200"  lossLandValue="60" needSunLevel="7" >
		 
		 
		 <!-- name: 该阶段的名称 time:从种下到该阶段的时间， decription:该阶段的描述  className该阶段的对应植物的素材（图片）, state阶段-->
		 <growData name="种子" time="0" description="" className="WanNianCaoYao_Show_1" state="seedState"/>
		 <growData name="发芽（幼苗）" time="0.5" description="" className="WanNianCaoYao_Show_2" state="sproutState"/>
		 <growData name="成长（开花）" time="1.5"  description=""  className="WanNianCaoYao_Show_3" state="growthState"/>
		 <growData name="成熟（结果）" time="2.5" description="" className="WanNianCaoYao_Show_4" state="maturityState"/>
		 <growData name="枯萎（收获后）" description=""   className="WanNianCaoYao_Show_5" state="witherState"/>
		 
   </item>
     <item id="11200004" className="Plant_XianPingCaoYao" name="仙品草药" level="0" description="此物只应天上有。(用于在炼丹炉中炼制丹药)" price="25000" ticketPrice="0" owner=""         equipmentType="grass"  maxSuperposition="20" isAbleSell="1" messageBoxColor="0xfd8602" isAbleAKeySell="0" harvestMinNum="1"         harvestMaxNum="1"  getSunMinNum="100" getSunMaxNum="200"  lossLandValue="60" needSunLevel="7" >
		 
		 
		 <!-- name: 该阶段的名称 time:从种下到该阶段的时间， decription:该阶段的描述  className该阶段的对应植物的素材（图片）, state阶段-->
		 <growData name="种子" time="0" description="" className="XianPingCaoYao_Show_1" state="seedState"/>
		 <growData name="发芽（幼苗）" time="0.5" description="" className="XianPingCaoYao_Show_2" state="sproutState"/>
		 <growData name="成长（开花）" time="1.5"  description=""  className="XianPingCaoYao_Show_3" state="growthState"/>
		 <growData name="成熟（结果）" time="2.5" description="" className="XianPingCaoYao_Show_4" state="maturityState"/>
		 <growData name="枯萎（收获后）" description=""   className="XianPingCaoYao_Show_5" state="witherState"/>
		 
   </item>
</data>