<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet4" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet4Idle" />
		<walk defId="pet4Walk" />
		<run defId="pet4Run" />
		<attack defId="pet4Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet4Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet4Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	    <skill id="Skill_Pet4Skill" className="YJFY.Skill.PetSkills.Skill_Pet4Skill" x="-480"
			y="-280" z="-1" xRange="960" yRange="560" zRange="100" bodyDefId="pet4SkillBodyShow" bodyAttackReachFrameLabel="down" 
			bodySkillEndFrameLabel="end^stop^"  everyEntityAddShowDefId="pet4SkillEnemyBeAttack" 
			everyEntityAddShowIsFrontOfBody="1"  hurtDuration="5000">
			<animationDefinition id="pet4SkillEnemyBeAttack" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet4All.swf"
					showClass="PetSkill4Effect_BeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet4Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet4All.swf"
					showClass="PetStand4_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet4Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet4All.swf"
					showClass="PetWalk4_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet4SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet4All.swf"
					showClass="PetSkill4Attack_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet4SkillFrontShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet4All.swf"
					showClass="PetSkill4Effect_Word_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			<animationDefinition id="pet4SkillEnemyBeAttack" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet4All.swf"
					showClass="PetSkill4Effect_BeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet4Idle" eqClassName="Pet_JiQiRen_1"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetStand4_1"
				x_offset="0" y_offset="0" />
			<show defId="pet4Walk" eqClassName="Pet_JiQiRen_1"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetWalk4_1"
				x_offset="0" y_offset="0" />
           <show defId="pet4SkillBodyShow" eqClassName="Pet_JiQiRen_1"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetSkill4Attack_1"
				x_offset="0" y_offset="0" />
			<show defId="pet4SkillFrontShow" eqClassName="Pet_JiQiRen_1"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetSkill4Effect_Word_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet4Idle" eqClassName="Pet_JiQiRen_2"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetStand4_2"
				x_offset="0" y_offset="0" />
			<show defId="pet4Walk" eqClassName="Pet_JiQiRen_2"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetWalk4_2"
				x_offset="0" y_offset="0" />
           <show defId="pet4SkillBodyShow" eqClassName="Pet_JiQiRen_2"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetSkill4Attack_2"
				x_offset="0" y_offset="0" />
			<show defId="pet4SkillFrontShow" eqClassName="Pet_JiQiRen_2"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetSkill4Effect_Word_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet4Idle" eqClassName="Pet_JiQiRen_3"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetStand4_3"
				x_offset="0" y_offset="0" />
			<show defId="pet4Walk" eqClassName="Pet_JiQiRen_3"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetWalk4_3"
				x_offset="0" y_offset="0" />
           <show defId="pet4SkillBodyShow" eqClassName="Pet_JiQiRen_3"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetSkill4Attack_3"
				x_offset="0" y_offset="0" />
			<show defId="pet4SkillFrontShow" eqClassName="Pet_JiQiRen_3"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetSkill4Effect_Word_3"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
