<?xml version="1.0" encoding="UTF-8"?>
<RecaptureGold>
          <TargetData>
                    <!-- 要抓取的目标物品   classNameForPut 当物品停留在空中时的显示 classNameForCatch 当物品被抓住时的显示  catchFun物品住到时的处理方法 getFun获得物品时的处理方法 （好几个）原来是43 -->
                    <!-- 包含金子物品类-->
                    <item name="小金子" classNameForPut="GoldPut1"  classNameForCatch="GoldCatch1" getGoldNum="50_50"  weight="10_10" soundForCatch="GetSound"/>
                    <item name="金子1" classNameForPut="GoldPut2"  classNameForCatch="GoldCatch2" getGoldNum="100_100"  weight="20_20" soundForCatch="GetSound"/>
                    <item name="金子2" classNameForPut="GoldPut3"  classNameForCatch="GoldCatch3" getGoldNum="150_150" weight="35_35" soundForCatch="GetSound"/>
                    <item name="大金子" classNameForPut="GoldPut4"  classNameForCatch="GoldCatch4" getGoldNum="500_500" weight="43_43" soundForCatch="GetSound"/>
                    <item name="小石子" classNameForPut="RockPut1"  classNameForCatch="RockCatch1" getGoldNum="11_11"  weight="35_35" soundForCatch="Stone"/>
                    <item name="大石头" classNameForPut="RockPut2"  classNameForCatch="RockCatch2" getGoldNum="22_22"  weight="43_43" soundForCatch="Stone"/>
                    
                    <item name="钻石" classNameForPut="DiamondPut1" classNameForCatch="DiamondCatch1" getGoldNum="600_600"  weight="5_5" soundForCatch="GetSound"/>
					
					<item name="孔明灯" classNameForPut="KMDPut1" classNameForCatch="KMDCatch1" getGoldNum="3_3"  weight="5_5" soundForCatch="Other"/>
                    
                    
                    
                    <item name="小鸟" classNameForPut="BirdPut1"  classNameForCatch="BirdCatch1" getGoldNum="2_2"  weight="6_6"  soundForCatch="Bird"/>
					
				 <item name="小鸟和钻石" classNameForPut="BirdAndDiamondPUt1"  classNameForCatch="BirdAndDiamondCatch1" getGoldNum="602_602"  weight="6_6"  soundForCatch="Bird"/>
                    
                    <!-- 炸弹类 -->
                    <item name="炸弹" classNameForPut="BombPut1"  classNameForCatch="" animationNameForExplode="ExplodeAnimation1" explodeRadius="150_150" soundForExplode="ExplodeSound1" />
                    
                    <!-- 问号背包类 -->
                    <item name="问号背包" classNameForPut="WenHaoPackagePut1"  classNameForCatch="WenHaoPackageCatch1" weight="5_45"   goodProbability="0.9"  badProbability="0.1"  soundForCatch="Other">
                               <Goods>
                                     <good probability="0.5"   content="goldNum"   goldNum="100_800" />
                                     <good probability="0.1"   content="addStrength"    value="75_75" />
                                     <good probability="0.4"   content="getProps"  propClassName="PropBomb1" propNum="1_3" />  
                               </Goods>
                               <Bads>
                                      <bad probability="1" content="subStrength"  value="-20_-20" />
                               </Bads>
                    
                    </item>
          
          </TargetData>
		  <ThiefData>
		        <item classNameForPutThief="Thief1" animationNameAfterCatch="ThiefAnimationAfterCatch1" animationNameAfterExplodeDie="AnimationNameAfterExplodeDie1"/>
				<item classNameForPutThief="Thief2" animationNameAfterCatch="ThiefAnimationAfterCatch2" animationNameAfterExplodeDie="AnimationNameAfterExplodeDie2"/>
				<item classNameForPutThief="Thief3" animationNameAfterCatch="ThiefAnimationAfterCatch3" animationNameAfterExplodeDie="AnimationNameAfterExplodeDie3"/>
		  </ThiefData>
           <PropData>
                       <!-- 炸弹类 -->
                       <item  name="炸弹" classNameForShow="PropBomb1" animationBombHook="AnimationBombHook1" animationFlyToHook="AnimationFlyToHook1" price="50_300"  shopApearPro="0.6" description="炸弹：炸毁钩子上的道具，使钩子收回速度为空钩的速度"/>
                       <!-- 提升物品金子值类 -->

                       <item name="钻石收藏书" classNameForShow="PropDiamondToGoldBook1"   promoteValue="1.5"  price="200_800" shopApearPro="0.3" description="钻石收藏书：下一关钩到钻石，可以获得更多的金子。"/> 

					   <!--幸运草 -->

					   <item name="幸运草" classNameForShow="PropLuckGrass1"  target="WenHaoPackage" promoteValue="1.5" price="100_300" shopApearPro="0.3" description="幸运草：下一关可以从问号袋子出更好的物品。"/>

					   
					   <item name="大力药水" classNameForShow="PropStrengthPotion1" promoteValue="100" price="400_1500"  shopApearPro="0.1" description="大力药水：下一关力量增强，拉钩的速度提高" />
           </PropData>
		   
           <GameLevelData>
               <!-- 游戏关卡数据 -->
                      <LevelTimeLimit  limitMaxTime="60"  limitMinTime="60"/>
					  <!-- 公式：multi2 * level * (level - 1) + multi * level + add -->
                      <LevelGoldNumTarget   multi="773_773" multi2="117_117" add="-413_-413" />
                      <!-- 3_3_3表示前面每关的随机关数（everyLevelNum）
                      9（maxLevel)是前面指定关卡的关数，超过这个关数后，则开始大随机，任意关卡
                       -->
                      <LevelNum  everyLevelNum="3_3_3_3_3_3_3_3_3" maxLevel="9"/>
           </GameLevelData>
           <!-- 人物力量 -->
           <PlayerData normalPhysicalStrength="50" enbleHaveBombNum="9">
              <Animation playerType="SunWuKong" flewInto="FlewInto_SunWuKong"  normal="Normal_SunWuKong" throwRope="ThrowRope_SunWuKong"  dragRope1="DragRope1_SunWuKong"  dragRope2="DragRope2_SunWuKong" useBomb="UseBomb_SunWuKong" />
              
			  <Animation playerType="BaiLongMa" flewInto="FlewInto_Bailongma" normal="Normal_Bailongma" throwRope="ThrowRope_Bailongma"  dragRope1="DragRope1_Bailongma"  dragRope2="DragRope2_Bailongma" useBomb="UseBomb_Bailongma" />
              <Animation playerType="ErLangShen" flewInto="FlewInto_Yangjian" normal="Normal_Yangjian" throwRope="ThrowRope_Yangjian"  dragRope1="DragRope1_Yangjian"  dragRope2="DragRope2_Yangjian" useBomb="UseBomb_Yangjian" />
              <Animation playerType="ChangE" flewInto="FlewInto_Change" normal="Normal_Change" throwRope="ThrowRope_Change"  dragRope1="DragRope1_Change"  dragRope2="DragRope2_Change" useBomb="UseBomb_Change" />
              <Animation playerType="Fox" flewInto="FlewInto_Fox" normal="Normal_Fox" throwRope="ThrowRope_Fox"  dragRope1="DragRope1_Fox"  dragRope2="DragRope2_Fox" useBomb="UseBomb_Fox" />
           </PlayerData>
		   
		   
		   <SoundData  soundForThrowRope="Drag1"  soundForDragRope="Drag1"  soundForAddStrength="ExplodeSound1" soundForDecStrength="ExplodeSound1" soundForGetGold="GetGold" soundForGetProps="GetGold" soundBombForPropBomb="GetGold" successPassLevelSound="ExplodeSound1" failPassLevelSound="Lose" />
		        
		   <!-- maxNum 夺金最多次数 -->
           <OtherData maxNum="2">
              <GameUIAnimation startGame="MolderAniamtion" passLevel="PassLevelAnimation" passLevelFail="MolderAniamtion" showShop="MolderAniamtion" nextLevel="MolderAniamtion" returnMainPanel="MolderAniamtion" />
              <PowerToSpeed value="7" />
              <RankList rId="208" />
			  <Background  background1="RGBackGround1" background2="RGBackGround2" speed1="10" speed2="50"/>
			 <!-- 钩子摆动速度 ，越大越慢-->
			  <SwingPeriod value="1.2" />
			  <BuyNumData buyTickets="1_4_10_20_40_60_70_80_90_100" buyTicketIds="392_393_394_395_396_397_398_399_400_401" oneBuyNum="1"/>
           </OtherData>



</RecaptureGold>