<?xml version="1.0" encoding="utf-8" ?>
<data>
	<skillLinkData>
		<data part1SkillId="Skill_BossTeleport" part2SkillId="yushiSkill1" />
		<data part1SkillId="Skill_BossSkill1" part2SkillId="yushiSkill2" />
		<data part1SkillId="bossSkill1" part2SkillId="yushiSkill3" />
	</skillLinkData>
	<!--<hurtAnimation2 defId="hurt2_jiaoRen" playFrameLabel="1" recoverFrameLabel="recover^stop^" />-->
	<autoAttackAI className="YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetAI1" />
	<petAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="500" />
		<data att="unableAttackMaxInterval" value="1500" />
	</petAttackData>
    <petDouble isDouble="true" partnerId="fengbo" percentToChange="70" changeCD="60"/>
	<animal id="yushi" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="140" bodyHeight="180" walkSpeed="150"
		runSpeed="300">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="-20" y="-10" z="-1" xRange="200" yRange="20" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />

	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
			
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.BossMode.Boss1.BossDecSpeedDrop"  bodyDefId="skill1Animation" dropShowDefId="daZhaoDropShow"  addBuffId="debuff" attTimer="3" moveSpeed="0" 
			decPropertyDuration = "30000" decPropertyPercent = "50,50" decPropertyTypes = "hitRate,criticalRate" hide ="1">
			 <!--decPropertyDuration->持续时间 decPropertyPercent ->减属性幅度 decPropertyTypes->减属类型 类型对应defence,hitRate,criticalRate,attack 分别为防御命中暴击和攻击，用非中文,隔开可减多种类型  PS:decPropertyPercent和decPropertyTypes一一对应-->
			<dropAttackRange x="-50" y="-50" z="-1" xRange="300" yRange="200" zRange="200" />
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="ItemDropEffect" x_offset="50" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="debuff" rows="1"
				cols="1" walkable="true" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="Debuff" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>

		<skill id="bossSkill1" className="YJFY.BossMode.Boss1.BossDaZhao3"  bodyDefId="skill3Animation" dropShowDefId="daZhaoDropShow2" attTimer="1" moveSpeed="0" hurtDuration = "300">
			<dropAttackRange x="-50" y="-50" z="-1" xRange="250" yRange="200" zRange="150" />
			<animationDefinition id="daZhaoDropShow2" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="ItemDropEffect2" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		<skill id="SkillResurgence" className="YJFY.LevelMode2.Levels1.Skill_AllResurgence"  bodyDefId="skillShow_resurgence"
			x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="300">
		</skill>
		<!--双妖将显示-->
		<skill id="skill_changeshow" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
		x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
		bodyDefId="skillDoubleShow" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillShowEnd^stop^"
		 bodySkillEndFrameLabel="skillShowEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<skill id="skill_changehide" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillDoubleHide" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillhideEnd^stop^"
				 bodySkillEndFrameLabel="skillhideEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf" 
				showClass="CBossRun" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="CBossBeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="CBossDead" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="skillShow_resurgence" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="skill_resurgence" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill3Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="CBossSkill3Show" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow2" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="ItemDropEffect2" x_offset="100" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="debuff" rows="1"
				cols="1" walkable="true" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="Debuff" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="DisappearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="AppearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="5"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf"
					showClass="ShadowOfAutomaticPet1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>

			<!--双妖将-->
			<animationDefinition id="skillDoubleShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="YushiShowAgain" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillDoubleHide" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/yushi.swf"
					showClass="YushiShowHide" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>
         <shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="bossFootShadow1" showSeriesId="d"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet1"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="c"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet2"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="b"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet3"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="a"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet4"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="s"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet5"
				x_offset="0" y_offset="0" />
				
		</shows>


	</animal>
</data>