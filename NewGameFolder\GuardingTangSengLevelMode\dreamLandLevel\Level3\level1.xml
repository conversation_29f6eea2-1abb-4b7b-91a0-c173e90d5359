<?xml version="1.0" encoding="utf-8" ?>
<data id="Level3" swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/level.swf"
	className="LevelMap3" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound2.swf" className="SoundTT" />
	<!--totalWaveNum 用于显示波次总数 -->
	<Waves totalWaveNum="7">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="0" duration="2500" num="5" isFallDown="0" />
		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1"  startTime="10000" duration="2500" num="5" isFallDown="0" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="700" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1"  startTime="20000" duration="2500" num="5" isFallDown="0" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="30000" duration="2500" num="5" isFallDown="0" />
		</Wave>				
		<Wave waveCount="5" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1"  startTime="40000" duration="2500" num="5" isFallDown="0" />
		</Wave>				
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1"  startTime="50000" duration="6000" num="7" isFallDown="1" />
		</Wave>				
		<Wave waveCount="7" totalEnemyNum="10" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss1" startTime="60000" duration="2500" num="5" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss2" startTime="62000" duration="1000" num="1"  />
		</Wave>
		
	</Waves>

   <EqDrop>
	   <boss noDropProWeight="200">
	   		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="6" />
				   <numData num="2" proweight="4" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="3" proWeight="8" />
				   <numData num="4" proWeight="1" />
			   </bigDropNumData>
		   </dropNumData>		  
	   
	   
		   <!--proWeight 概率权重-->
 		  <!-- 无敌药水 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="5" />
   	      <!-- 龙蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Tyrannosaurs_S" proWeight="5" />
   	      <!-- 雷蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_XiaoPiKaQiu_S" proWeight="5" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="20" />
   	      <!-- 高级升级宝石磨具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="3" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="3" />
	     <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp1_S" proWeight="3" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp1_S" proWeight="3" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack1_S" proWeight="3" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp1_S" proWeight="3" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence1_S" proWeight="3" />    	              	      
           <!--  红药   -->
   	      <item dropClassName="Item_HpUp" proWeight="50" />
           <!--  蓝药  -->
   	      <item dropClassName="Item_MpUp" proWeight="50" /> 
           <!--    金币  -->
   	      <item dropClassName="Item_MoneyUp_10000" proWeight="10"  /> 
            <!--   灵兽石-->
   	      <item dropClassName="Item_StrengthenNum_10" proWeight="5"  /> 
      
	   </boss>
   </EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	

   <boss1>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="3000" />
		<data att="attack" value="200" />
		<data att="expOfDieThisEnemy" value="4000" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.01" />
		<data att="criticalRate" value="0.1" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0" />
		<data att="hitRate" value="0" />
	</enemyData>
	<animal id="boss1" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="120" bodyHeight="160" walkSpeed="25"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="idle_boss3" />
		<walk defId="walk_boss3" />
		<run defId="run_boss3" />
		<attack defId="attack_boss3" />
		<hurt defId="hurt_boss3" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss3" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="Walk_Boss_3" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="Walk_Boss_3" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss3" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_1" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="BeAttack_Boss_3" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="Attack_Boss_3" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="Dead_Boss_3" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</boss1>


   <boss2>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="30000" />
		<data att="attack" value="200" />
		<data att="expOfDieThisEnemy" value="40000" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.01" />
		<data att="criticalRate" value="0.1" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0" />
		<data att="hitRate" value="0" />
	</enemyData>
	<animal id="boss2" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="120" bodyHeight="160" walkSpeed="25"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="200" yRange="60"
			zRange="100" />

		<idle defId="idle_boss3" />
		<walk defId="walk_boss3" />
		<run defId="run_boss3" />
		<attack defId="attack_boss3" />
		<hurt defId="hurt_boss3" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss3" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="Walk_Boss_3" x_offset="0" y_offset="0"  x_scale="2" y_scale="2" >
					<cT rO="80" gO="5" bO="5" aO="5"/>
				</show>	
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="Walk_Boss_3" x_offset="0" y_offset="0"  x_scale="2" y_scale="2" >
					<cT rO="80" gO="5" bO="5" aO="5"/>
				</show>	
			</animationDefinition>
			<!-- <animationDefinition id="run_boss3" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_1" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="BeAttack_Boss_3" x_offset="0" y_offset="0" x_scale="2" y_scale="2"  >
					<cT rO="80" gO="5" bO="5" aO="5"/>
				</show>	
			</animationDefinition>
			<animationDefinition id="attack_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="Attack_Boss_3" x_offset="0" y_offset="0"  x_scale="2" y_scale="2" >
					<cT rO="80" gO="5" bO="5" aO="5"/>
				</show>	
			</animationDefinition>
			<animationDefinition id="die_boss3" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level3/Boss.swf"
					showClass="Dead_Boss_3" x_offset="0" y_offset="0" x_scale="2" y_scale="2"  >
					<cT rO="80" gO="5" bO="5" aO="5"/>
				</show>								
			</animationDefinition>


			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0"  x_scale="2" y_scale="2" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0"  x_scale="2" y_scale="2" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</boss2>
</data>
