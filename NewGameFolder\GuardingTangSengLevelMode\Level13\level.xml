<?xml version="1.0" encoding="utf-8" ?>
<data id="Level13" swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/level.swf"
	className="LevelMap13" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf" className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
		<Waves totalWaveNum="18">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="0" duration="5000" num="18" isFallDown="0" />
		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="15000" duration="5000" num="16" isFallDown="0" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="600" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="30000" duration="10000" num="20" isFallDown="1" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="45000" duration="50000" num="16" isFallDown="0" />
		</Wave>				
		<Wave waveCount="5" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="60000" duration="10000" num="20" isFallDown="1" />
		</Wave>		
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="75000" duration="5000" num="15" isFallDown="0" />
		</Wave>		
		<Wave waveCount="7" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="90000" duration="10000" num="15" isFallDown="1" />
		</Wave>		
		<Wave waveCount="8" totalEnemyNum="20" x="600" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="105000" duration="10000" num="20" isFallDown="1" />
		</Wave>		
		<Wave waveCount="9" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="120000" duration="5000" num="15" isFallDown="0" />
		</Wave>	
		<Wave waveCount="10" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="135000" duration="10000" num="15" isFallDown="1" />
		</Wave>	
		<Wave waveCount="11" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="150000" duration="5000" num="15" isFallDown="0" />
		</Wave>		
		<Wave waveCount="12" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="165000" duration="10000" num="30" isFallDown="0" />
		</Wave>	
		<Wave waveCount="13" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="180000" duration="10000" num="20" isFallDown="1" />
		</Wave>														
		<Wave waveCount="14" totalEnemyNum="20" x="600" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="195000" duration="10000" num="20" isFallDown="1" />
		</Wave>														
		<Wave waveCount="15" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="210000" duration="5000" num="15" isFallDown="0" />
		</Wave>														
		<Wave waveCount="16" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="225000" duration="10000" num="30" isFallDown="0" />
		</Wave>														
		<Wave waveCount="17" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="240000" duration="10000" num="25" isFallDown="1" />
		</Wave>														
		<Wave waveCount="18" totalEnemyNum="10" x="900" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="255000" duration="10000" num="25" isFallDown="1" />
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss" startTime="260000" duration="1000" num="1"  />
		</Wave>
		
	</Waves>

      <EqDrop>
	   <xiaoBing noDropProWeight="1000">
		   <!--proWeight 概率权重-->
		   
		  <!-- 无敌药水 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="1" />		 
   	      <!-- 龙蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Tyrannosaurs_S" proWeight="1" />
   	      <!-- 雷蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_XiaoPiKaQiu_S" proWeight="1" />   
   	      	        
   	      <!--  灵狐套装5磨具   --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_FoxWeapon5_S" proWeight="10" />   	      	        

   	      <!--  灵狐套装5磨具   --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_FoxClothes5_S" proWeight="10" /> 
		     <!-- 铁扇武器模具5  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_TieShanWeapon5_S" proWeight="10" /> 
		 <!-- 铁扇套装模具5 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_TieShanClothes5_S" proWeight="10" /> 
		     
		  
		  		    <!--后羿武器模具5  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_HouyiWeapon5_S" proWeight="10" />  
		   <!-- 后羿套装模具5 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_HouyiClothes5_S" proWeight="10" />  
		  
		  <!-- 棍 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_2_S" proWeight="10" />
   	      <!-- 杖 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_5_S" proWeight="10" />
   	      <!-- 剑 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_FengHuangKaiJia_S" proWeight="10" />
   	      <!-- 手枪 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_JiGuangShouQiang_S" proWeight="10" />
   	      <!-- 项链 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Necklace_ShengZheXiangLian_S" proWeight="10" />
   	      <!-- 猴子铠甲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_8_S" proWeight="10" />
   	      <!-- 龙马铠甲 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_11_S" proWeight="10" />
   	      <!-- 杨戬铠甲 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_ShengYi_S" proWeight="10" />
   	      <!-- 嫦娥铠甲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_JiXieKaiJia_S" proWeight="10" />
   	      <!-- 葫芦 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Gourd_ZhenTianHuLu_S" proWeight="10" />
   	      
  	      <!--  红药 -->   
   	      <item dropClassName="Item_HpUp" proWeight="100" />
   	      <!--  金币 -->  
   	      <item dropClassName="Item_MoneyUp" proWeight="300" />
   	      <!--  蓝药 -->  
   	      <item dropClassName="Item_MpUp" proWeight="100" /> 
 

    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Needlework_S" proWeight="10" />
    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.PiLiao_S" proWeight="10" /> 
     	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Iron_S" proWeight="10" />
<!--      	  材料
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LongJiao_S" proWeight="10" />
     	  材料
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_FangFengJing_S" proWeight="10" /> -->
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="10" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="1" />
   	          
	  </xiaoBing>
	  
	  
	   <boss noDropProWeight="0">
		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="5" />
				   <numData num="2" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="4" proWeight="8" />
				   <numData num="5" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>		  
		  <!-- 无敌药水 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="1" />		 
   	      <!-- 火人蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_HuoRen_S" proWeight="1" />
   	      <!-- 雷蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_XiaoPiKaQiu_S" proWeight="1" />   
   	      <!-- 火剑蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Jian_S" proWeight="1" />   
     	      	        
   	      <!--  灵狐套装5磨具   --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_FoxWeapon5_S" proWeight="10" />   	      	        

   	      <!--  灵狐套装5磨具   --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_FoxClothes5_S" proWeight="10" />  	      	        
		    <!-- 铁扇武器模具5  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_TieShanWeapon5_S" proWeight="10" /> 
		 <!-- 铁扇套装模具5 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_TieShanClothes5_S" proWeight="10" /> 
		  
		  		    <!--后羿武器模具5  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_HouyiWeapon5_S" proWeight="10" />  
		   <!-- 后羿套装模具5 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_HouyiClothes5_S" proWeight="10" />  
		     
		  <!-- 棍 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_2_S" proWeight="10" />
   	      <!-- 杖 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_5_S" proWeight="10" />
   	      <!-- 剑 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_GuiYan_S" proWeight="10" />
   	      <!-- 手枪 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_TeZhanShouQiang_S" proWeight="10" />

   	      <!-- 项链 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Necklace_ShengZheXiangLian_S" proWeight="10" />

   	      <!-- 猴子铠甲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_8_S" proWeight="10" />
   	      <!-- 龙马铠甲 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_11_S" proWeight="10" />
   	      <!-- 杨戬铠甲 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_LongWanKaiJia_S" proWeight="10" />
   	      <!-- 嫦娥铠甲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_TeZhanKaiJia_S" proWeight="10" />
   	      <!-- 葫芦 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Gourd_ZhenTianHuLu_S" proWeight="10" />
   	      
<!--   	       红药   
   	      <item dropClassName="Item_HpUp" proWeight="100" />
   	       金币  
   	      <item dropClassName="Item_MoneyUp" proWeight="300" />
   	       蓝药  
   	      <item dropClassName="Item_MpUp" proWeight="100" />  -->
     	  <!-- 铁扇套装材料5  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_TeiShan5cailiao_S" proWeight="20" />
		  
				   <!-- 后羿套装材料5  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Houyi5cailiao_S" proWeight="20" />  
	
		  
		  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_JinYinHua_S" proWeight="20" /> 
    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Needlework_S" proWeight="20" />
    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.PiLiao_S" proWeight="20" />
     	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Iron_S" proWeight="20" />
    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.SharkFin_S" proWeight="20" />
    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.TigerSkin_S" proWeight="20" /> 
     	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_FengHuangYuMao_S" proWeight="20" />
     	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_GangTianChiLun_S" proWeight="20" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="10" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="5" />
	   </boss>
	   
   </EqDrop>




	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   <xiaoBing>
	<!--敌人数据 -->
	<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
		<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
	</fallDownEffect>
	<enemyData>
	
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
		<data att="totalHp" value="15000" />
		<data att="attack" value="600" />
		<data att="expOfDieThisEnemy" value="900" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.01" />
		<data att="criticalRate" value="0.05" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0" />
		<data att="hitRate" value="0" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy13" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80" walkSpeed="18"
		runSpeed="100">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="40" yRange="60"
			zRange="100" />

		<idle defId="walk_enemy13" />
		<walk defId="walk_enemy13" />
		<run defId="walk_enemy13" />
		<attack defId="attack_enemy13" />
		<hurt defId="hurt_enemy13" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy13" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow1" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="walk_enemy13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Enemy.swf"
					showClass="Walk_Monster_13" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="hurt_enemy13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Enemy.swf"
					showClass="BeAttack_Monster_13" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Enemy.swf"
					showClass="Attack_Monster_13" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Enemy.swf"
					showClass="Dead_Monster_13" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
		</animationDefinitions>



	</animal>
</xiaoBing>
   <boss>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="45000" />   
		<data att="attack" value="500" />
		<data att="expOfDieThisEnemy" value="10000" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0" />
		<data att="criticalRate" value="0.1" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0" />
		<data att="hitRate" value="0" />
	</enemyData>
	<animal id="boss13" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="idle_boss13" />
		<walk defId="walk_boss13" />
		<run defId="run_boss13" />
		<attack defId="attack_boss13" />
		<hurt defId="hurt_boss13" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss13" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Boss.swf"
					showClass="Walk_Boss_13" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Boss.swf"
					showClass="Walk_Boss_13" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss13" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_13" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Boss.swf"
					showClass="BeAttack_Boss_13" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Boss.swf"
					showClass="Attack_Boss_13" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss13" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level13/Boss.swf"
					showClass="Dead_Boss_13" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</boss>
</data>
