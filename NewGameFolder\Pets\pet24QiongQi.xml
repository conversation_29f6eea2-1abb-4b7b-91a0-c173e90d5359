<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet24" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="petIdle" />
		<walk defId="petWalk" />
		<run defId="petRun" />
		<attack defId="petAttack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="petHurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="petDie">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	    <skill id="Skill_Pet23Skill" 
		       className="YJFY.Skill.PetSkills.Skill_Pet23Skill" 
			   x="-1200" 
			   y="-1200" 
			   z="-1" 
			   xRange="2400" 
			   yRange="2400" 
			   zRange="1200"    
			    >
			<animationDefinition id="debuff" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet24All.swf"
					showClass="Debuff" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="petIdle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet24All.swf"
					showClass="PetStand_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petWalk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet24All.swf"
					showClass="PetWalk_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="petSkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet24All.swf"
					showClass="PetSkill_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petSkillEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet24All.swf"
					showClass="petSkillEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="petIdle" eqClassName="Pet_QiongQi_1"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetStand_1"
				x_offset="0" y_offset="0" />
			<show defId="petWalk" eqClassName="Pet_QiongQi_1"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetWalk_1"
				x_offset="0" y_offset="0" />
           <show defId="petSkillBodyShow" eqClassName="Pet_QiongQi_1"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetSkill_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="petIdle" eqClassName="Pet_QiongQi_2"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetStand_2"
				x_offset="0" y_offset="0" />
			<show defId="petWalk" eqClassName="Pet_QiongQi_2"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetWalk_2"
				x_offset="0" y_offset="0" />
           <show defId="petSkillBodyShow" eqClassName="Pet_QiongQi_2"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetSkill_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="petIdle" eqClassName="Pet_QiongQi_3"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetStand_3"
				x_offset="0" y_offset="0" />
			<show defId="petWalk" eqClassName="Pet_QiongQi_3"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetWalk_3"
				x_offset="0" y_offset="0" />
           <show defId="petSkillBodyShow" eqClassName="Pet_QiongQi_3"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetSkill_3"
				x_offset="0" y_offset="0" />
			
		</shows>

	</animal>
</data>
