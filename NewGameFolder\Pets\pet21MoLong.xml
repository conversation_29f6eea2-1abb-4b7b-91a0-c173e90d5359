<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet21" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--����û�й��������Ժ���-->
		<!--������Χ��x���������η�Χ��ʵ�����ϵ�е�x��꣬ y:�������η�Χ��ʵ����꣬ width���������η�Χ��x�᷶Χ���ȣ� height���������η�Χ��y�᷶Χ���� -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="petIdle" />
		<walk defId="petWalk" />
		<run defId="petRun" />
		<attack defId="petAttack"  />
		<hurt defId="petHurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="petDie">
			<attackSourceData entityId="" skillId="" />
		</die>
	   
		<sound>
			
		</sound>
         <!--���＼��1-->
	   <skill id="Skill_Pet21Skill" 
	          className="YJFY.Skill.PetSkills.Skill_Pet21Skill"
	          bodyDefId="petSkillBodyShow"
			  x="-480" 
			   y="-280" 
			   z="-1" 
			   xRange="960" 
			   yRange="600" 
			   zRange="500" 
			   bodyAttackReachFrameLabel="down" 
			   everyEntityAddShowDefId="changeStart" 
			   bodySkillEndFrameLabel="end^stop^" 
			   releaseSkillFrameLabel="disappearEnd^stop^"
			   attackReachFrameLabel="skillAttackReach" 
			   skillShowEndFrameLabel="skillShowEnd^stop^"  
			   skillEndFrameLabel="appearEnd^stop^"
			   >
			<animationDefinition id="changeStart" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet21All.swf"
					showClass="ChangeStart" x_offset="0" y_offset="0" />
			</animationDefinition>
			<shakeView swfPath="NewGameFolder/PetSource/Pet21All.swf" className="ShakeView" />
		</skill>



		<animationDefinitions>
			<animationDefinition id="petIdle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet21All.swf"
					showClass="PetStand" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petWalk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet21All.swf"
					showClass="PetWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petSkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet21All.swf"
					showClass="PetSkill" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="petSkillShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet21All.swf"
					showClass="SkillShow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<!-- ����װ-->
		    <!--���ܹ���Ч��-->
			
			
			
		</animationDefinitions>

		<shows>
			<!--ͨ��defId �� eqClassName �ڸ���animalEntity��allAnimations�����������Ӧ�Ķ�����ݣ�Ȼ������³�ʼ��AnimationDefinition -->
			<!--��ʼװ -->
			
		</shows>

	</animal>
</data>
