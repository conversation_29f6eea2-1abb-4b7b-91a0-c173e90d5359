<?xml version="1.0" encoding="utf-8" ?>
<data id="Level24" swfPath="NewGameFolder/GuardingTangSengLevelMode/Level24/level.swf"
	className="LevelMap24" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf" className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
		<Waves totalWaveNum="15">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->																						

		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="0" duration="5000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="0" duration="5000" num="3"  isFallDown="0"  />
		
		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="15000" duration="5000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="15000" duration="5000" num="3"  isFallDown="0"  />

		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="30000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="30000" duration="10000" num="3" isFallDown="0" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="45000" duration="50000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="45000" duration="50000" num="3" isFallDown="0" />
		</Wave>				
		<Wave waveCount="5" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="60000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="60000" duration="10000" num="3" isFallDown="0" />
		</Wave>		
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="75000" duration="5000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="75000" duration="5000" num="3" isFallDown="0" />
		</Wave>		
		<Wave waveCount="7" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="90000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="90000" duration="10000" num="3" isFallDown="0" />
		</Wave>		
		<Wave waveCount="8" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="105000" duration="5000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="105000" duration="5000" num="3" isFallDown="0" />
		</Wave>		
		<Wave waveCount="9" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="120000" duration="5000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="120000" duration="5000" num="3" isFallDown="0" />
		</Wave>	
		<Wave waveCount="10" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="135000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="135000" duration="10000" num="3" isFallDown="0" />
		</Wave>	
		<Wave waveCount="11" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="150000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="150000" duration="10000" num="3" isFallDown="0" />
		</Wave>		
		<Wave waveCount="12" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="165000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="165000" duration="10000" num="3" isFallDown="0" />
		</Wave>	
		<Wave waveCount="13" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="180000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="180000" duration="10000" num="3" isFallDown="0" />
		</Wave>														
		<Wave waveCount="14" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="195000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="195000" duration="10000" num="3" isFallDown="0" />
		</Wave>																											
		<Wave waveCount="15" totalEnemyNum="10" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="3000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1"  startTime="3000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss4" startTime="3050" duration="1000" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss3" startTime="3050" duration="1000" num="1"  />
		</Wave>
	</Waves>
	
	
<EqDrop>
		   <boss noDropProWeight="200">
	   		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="6" />
				   <numData num="2" proweight="4" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="3" proWeight="8" />
				   <numData num="4" proWeight="1" />
			   </bigDropNumData>
		   </dropNumData>		  
	   
	   
		   <!--proWeight 概率权重-->
 		  <!-- 二级火 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_TwoFire_S" proWeight="2" />
		    <!-- 天王碎片 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.Material_HongSe_S"proWeight="1" />
 		  <!-- 无敌药水 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="5" />
   	      <!-- 龙蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Lu_S" proWeight="5" />
   	      <!-- 雷蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_LiHe_S" proWeight="5" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="20" />
   	      <!-- 高级升级宝石磨具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="3" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="3" /> 
 		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp3_S" proWeight="2" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp3_S" proWeight="2" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack3_S" proWeight="2" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp3_S" proWeight="2" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence3_S" proWeight="2" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="5" />   
   	      <!-- 碎石锤 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S" proWeight="5" />  	      
     	     <!-- 朱雀 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ZhuQueStone_S" proWeight="2" />   
   	      <!-- 玄武 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_XuanWuStone_S" proWeight="2" />      
     	     <!-- 青龙 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_QingLongStone_S" proWeight="2" />   
   	      <!-- 白虎 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_BaiHuStone_S" proWeight="2" />      
		  <!-- 灵狐 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LingHuShi_S" proWeight="5" />    	      
   	         	              	      
           <!--  红药   -->
   	      <item dropClassName="Item_HpUp" proWeight="50" />
           <!--  蓝药  -->
   	      <item dropClassName="Item_MpUp" proWeight="50" /> 
           <!--    金币  -->
   	      <item dropClassName="Item_MoneyUp_10000" proWeight="10"  /> 
           <!--    金币  -->
   	      <item dropClassName="Item_MoneyUp_50000" proWeight="5"  /> 
           <!--    金币  -->
   	      <item dropClassName="Item_MoneyUp_100000" proWeight="5"  /> 
               <!--   灵兽石-->
   	      <item dropClassName="Item_StrengthenNum_10" proWeight="5"  /> 
             <!--  召唤卷轴 -->
   	      <item dropClassName="Item_ZhaoHuan_1" proWeight="3"  /> 
      
	   </boss>
	   
   </EqDrop>


	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   
   <boss1>
   <attackShakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" className="AttackShakeView" />
	<wakeOfDeathData deathDuration="5000" />
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="3000000" />
		<data att="attack" value="8000" />
		<data att="expOfDieThisEnemy" value="200000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.25" />
	</enemyData>
	<animal id="boss23" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="40">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="walk_boss" />
		<walk defId="walk_boss" />
		<run defId="walk_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Walk_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss22" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="BeAttack_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Attack_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Dead_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="wakeOfDeath" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="WakeOfDeath_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>
        <shows>
			<show defId="walk_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Walk_Boss_2_23"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="hurt_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="BeAttack_Boss_2_23"
				  x_offset="0" 
				  y_offset="0" />
		    <show defId="attack_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Attack_Boss_2_23"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="die_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Dead_Boss_2_23"
				  x_offset="0" 
				  y_offset="0" />
		</shows>


	</animal>
</boss1>

   <boss2>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="3000000" />
		<data att="attack" value="8000" />
		<data att="expOfDieThisEnemy" value="200000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.25" />
	</enemyData>
	<animal id="boss22" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="180" walkSpeed="22"
		runSpeed="60">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="200" yRange="60"
			zRange="100" />

		<idle defId="walk_boss22" />
		<walk defId="walk_boss22" />
		<run defId="walk_boss22" />
		<attack defId="attack_boss22" />
		<hurt defId="hurt_boss22" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss22" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			
			<animationDefinition id="walk_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Walk_Boss_22" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss22" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="BeAttack_Boss_22" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Attack_Boss_22" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Dead_Boss_22" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="change" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Change" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!--<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>-->
		</animationDefinitions>
        <shows>
			<show defId="walk_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Walk_Boss_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="hurt_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="BeAttack_Boss_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
		    <show defId="attack_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Attack_Boss_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="die_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Dead_Boss_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
		</shows>


	</animal>
</boss2>

   <boss3>
   <attackShakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" className="AttackShakeView" />
	<wakeOfDeathData deathDuration="5000" />
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="15000000" />
		<data att="attack" value="8000" />
		<data att="expOfDieThisEnemy" value="200000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.25" />
	</enemyData>
	<animal id="boss23" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="40">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="walk_boss" />
		<walk defId="walk_boss" />
		<run defId="walk_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Walk_Boss_23" x_offset="0" y_offset="0" x_scale="2" y_scale="2">
						<cT  rO="0" gO="0" bO="255" aO="0" />
				</show>		
			</animationDefinition>
			<!-- <animationDefinition id="run_boss22" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="BeAttack_Boss_23" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="0" bO="255" aO="0" />
				</show>		
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Attack_Boss_23" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="0" bO="255" aO="0" />
				</show>		
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Dead_Boss_23" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="0" bO="255" aO="0" />
				</show>		
			</animationDefinition>


			<animationDefinition id="wakeOfDeath" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="WakeOfDeath_Boss_23" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="0" bO="255" aO="0" />
				</show>		
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="0" bO="255" aO="0" />
				</show>		
			</animationDefinition>
		</animationDefinitions>
        <shows>
			<show defId="walk_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Walk_Boss_2_23"
				  x_offset="0"  y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="50" bO="255" aO="0" />
				</show>	
			<show defId="hurt_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="BeAttack_Boss_2_23"
				  x_offset="0"  y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="50" bO="255" aO="0" />
				</show>	
		    <show defId="attack_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Attack_Boss_2_23"
				  x_offset="0"  y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="50" bO="255" aO="0" />
				</show>	
			<show defId="die_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Dead_Boss_2_23"
				  x_offset="0"  y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="0" gO="50" bO="255" aO="0" />
				</show>	
		</shows>
	
	
	
	


	</animal>
</boss3>

   <boss4>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="15000000" />
		<data att="attack" value="8000" />
		<data att="expOfDieThisEnemy" value="200000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.25" />
	</enemyData>
<animal id="boss22" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="180" walkSpeed="22"
		runSpeed="60">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="walk_boss22" />
		<walk defId="walk_boss22" />
		<run defId="walk_boss22" />
		<attack defId="attack_boss22" />
		<hurt defId="hurt_boss22" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss22" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			
			<animationDefinition id="walk_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Walk_Boss_22" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>		
			</animationDefinition>
			<!-- <animationDefinition id="run_boss22" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="BeAttack_Boss_22" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>		
			</animationDefinition>
			<animationDefinition id="attack_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Attack_Boss_22" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>		
			</animationDefinition>
			<animationDefinition id="die_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Dead_Boss_22" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>		
			</animationDefinition>


			<animationDefinition id="change" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Change" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>		
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>		
			</animationDefinition>
			<!--<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>-->
		</animationDefinitions>
        <shows>
			<show defId="walk_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Walk_Boss_InSuper_22"
                  x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>		
			<show defId="hurt_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="BeAttack_Boss_InSuper_22"
				  x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>	
		    <show defId="attack_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Attack_Boss_InSuper_22"
				 x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>	
			<show defId="die_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Dead_Boss_InSuper_22"
				  x_offset="0" y_offset="0"  x_scale="2" y_scale="2">
						<cT  rO="100" gO="50" bO="0" aO="0" />
				</show>	
		</shows>

	</animal>
</boss4>


</data>
