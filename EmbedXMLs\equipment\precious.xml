<?xml version="1.0" encoding="utf-8" ?>
<data>
<!-- 灵符 -->
	<item id="13000000" className="precious_tianshi" name="天使灵符" level="0"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="3"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0xffffff" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>

	</item>
	<item id="13000100" className="precious_tianshi" name="天使灵符" level="1"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="4"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0x66CC66" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     

	<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
		
	</item>
	<item id="13000200" className="precious_tianshi" name="天使灵符" level="2" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="5"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0x33FF00" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     

		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
		
	</item>
	
	<item id="13000300" className="precious_tianshi" name="天使灵符" level="3" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="6"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0x0066FF" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     

			<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
	</item>
	
	<item id="13000400" className="precious_tianshi" name="天使灵符" level="4"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="7"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0x0000FF" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
		
	</item>
	
	<item id="13000500" className="precious_tianshi" name="天使灵符" level="5" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="8"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0x9933ff" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     

		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
	</item>
	
	<item id="13000600" className="precious_tianshi" name="天使灵符" level="6" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="9"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0xff00c4" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     

		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
	</item>
	
	<item id="13000700" className="precious_tianshi" name="天使灵符" level="7" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="10"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0xffd105" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     

		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
	</item>
	
	<item id="13000800" className="precious_tianshi" name="天使灵符" level="8" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="11"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0xFF3300" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="90" totalweight="150"/>
		<saddWeight value="2" weightmin="91" weightmax="130" totalweight="150"/>
		<saddWeight value="3" weightmin="131" weightmax="150" totalweight="150"/>

		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
	</item>
	
	<item id="13000900" className="precious_tianshi" name="天使灵符" level="9" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="12"
		description="天使灵符，综合提高人物防御系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="1"
		isAbleSell="1" messageBoxColor="0x990000" upgradeValue="0" material="13000000"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		<addWeight value="3" weightmin="1" weightmax="50" totalweight="150"/>
		<addWeight value="3" weightmin="51" weightmax="100" totalweight="150"/>
		<addWeight value="3" weightmin="101" weightmax="150" totalweight="150"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     

		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
	</item>


	
	
		
<!-- 灵符 -->
	<item id="13000001" className="precious_emo" name="恶魔灵符" level="0"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="3"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0xffffff" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
		

	</item>
	<item id="13000101" className="precious_emo" name="恶魔灵符" level="1"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="4"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x66CC66" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
		
	</item>
	<item id="13000201" className="precious_emo" name="恶魔灵符" level="2"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="4"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x66CC66" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
		
	</item>
	<item id="13000301" className="precious_emo" name="恶魔灵符" level="3" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="6"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x0066FF" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
	</item>
	
	<item id="13000401" className="precious_emo" name="恶魔灵符" level="4"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="7"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x0000FF" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
		
	</item>
	
	<item id="13000501" className="precious_emo" name="恶魔灵符" level="5" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="8"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x9933ff" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
	</item>
	
	<item id="13000601" className="precious_emo" name="恶魔灵符" level="6" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="9"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0xff00c4" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
	</item>
	
	<item id="13000701" className="precious_emo" name="恶魔灵符" level="7" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="10"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0xffd105" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
	</item>
	
	<item id="13000801" className="precious_emo" name="恶魔灵符" level="8" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="11"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0xFF3300" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
	</item>
	
	<item id="13000901" className="precious_emo" name="恶魔灵符" level="9" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="12"
		description="恶魔灵符，综合提高人物攻击系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x990000" upgradeValue="0" material="13000001"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="60" totalweight="100"/>
		<addWeight value="2" weightmin="61" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="2" minupgrade="0.3" maxupgrade="1.2" weight="5" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="1" minupgrade="0.1" maxupgrade="2" weight="0" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.2" maxupgrade="1.1" weight="1" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="0" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="2" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="8" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="0" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="0" totalweight="100"/>
	
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="2" totalweight="100"/>
		
	</item>
	
	
	<!-- 灵符 -->
	<item id="13000002" className="precious_Nzhuan" name="逆转灵符" level="0"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="3"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0xffffff" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
		

	</item>
	<item id="13000102" className="precious_Nzhuan" name="逆转灵符" level="1"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="4"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x66CC66" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
		
	</item>
	<item id="13000202" className="precious_Nzhuan" name="逆转灵符" level="2"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="4"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x66CC66" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
		<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 -->  

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
		
	</item>
	<item id="13000302" className="precious_Nzhuan" name="逆转灵符" level="3" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="6"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x0066FF" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
	</item>
	
	<item id="13000402" className="precious_Nzhuan" name="逆转灵符" level="4"
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="7"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x0000FF" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
	</item>
	
	<item id="13000502" className="precious_Nzhuan" name="逆转灵符" level="5" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="8"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x9933ff" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
	</item>
	
	<item id="13000602" className="precious_Nzhuan" name="逆转灵符" level="6" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="9"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0xff00c4" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 
		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
	</item>
	
	<item id="13000702" className="precious_Nzhuan" name="逆转灵符" level="7" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="10"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0xffd105" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
	</item>
	
	<item id="13000802" className="precious_Nzhuan" name="逆转灵符" level="8" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="11"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"   implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0xFF3300" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
	</item>
	
	<item id="13000902" className="precious_Nzhuan" name="逆转灵符" level="9" 
		maxLevel="9" upgradeSuccessRate="0.5" upgradePrice="1000000" upgradeGemNum="12"
		description="逆转灵符，综合提高人物全系属性，并拥有多种特殊属性。" price="300" owner="" equipmentType="precious"
		minRenPin="0" maxRenPin="0"  implictProPlayerId="LingFu" detectiontype="2"
		isAbleSell="1" messageBoxColor="0x990000" upgradeValue="0" material="13000002"
		isAbleAKeySell="1" maxHoleNum="5">
			<!-- 基础属性随机个数的权重算法 --> 
		
		<addWeight value="1" weightmin="1" weightmax="70" totalweight="100"/>
		<addWeight value="2" weightmin="71" weightmax="90" totalweight="100"/>
		<addWeight value="3" weightmin="91" weightmax="100" totalweight="100"/>

		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="2" minupgrade="0.5" maxupgrade="1.7" weight="10" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="3" minupgrade="0.3" maxupgrade="1.5" weight="2" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="2" minupgrade="0.1" maxupgrade="2" weight="3" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="2" minupgrade="0.2" maxupgrade="1.1" weight="6" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="4" minupgrade="0.3" maxupgrade="1.8" weight="2" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="2" minupgrade="0.5" maxupgrade="1.6" weight="0" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="3" minupgrade="1" maxupgrade="2" weight="3" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="120" totalweight="150"/>
		<saddWeight value="2" weightmin="121" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="25" weight="2" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="1.5" weight="1" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="5" weight="5" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="50" weight="8" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="1.6" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="1" totalweight="100"/>
	
		<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
		<sAddAttr addAttName="increaseMingzhong" addAttValue="1" avgValue="10" minvalue="0.5" maxvalue="1.6" weight="0" totalweight="100"/>
		
	</item>
	
	
	
	
	
	
	
	
</data>