<?xml version="1.0" encoding="UTF-8"?>
<Farm>
	<!-- impactValueToHarvest: 该土地对在其上的植物收获成果的影响值  这样子最后的收获数为（impactValueToHarvest * (random * (harvestMaxNum - 
	     harvestMinNum) + harvestMinNum));
	     recoverability: 该土地的回复能力  这样子最后的土地回复时间为（算上植物对土地的损害lossLandValue) lossLandValue * 1m / recoverability;
		 impactValueToGeneration: 该土地对在其上的植物的成长时间的影响值， impactValueToGeneration * 生成时间，
		 price:升级土地的价格 -->
	<landData H_width="3" V_height="3" isEnableMove="0" />
	<land id="100100" className="Land_OrdinaryLand" name="普通土地" level="1" impactValueToHarvest="0.8" impactValueToGeneration="1" recoverability="1" price="10000"   
	recoverLandTickets="2_6_12_20_30_42_56_72_90_110_132_156_182_200" recoverLandTicketIds="374_375_376_377_378_379_380_381_382_383_384_385_386_387" />
	
	
	<otherItem id="1000" name="炼丹炉" className="UI.Farm.Btn.RefineFactoryBtn" H_width="3" V_height="3" isEnableMove="1" 
	  description="丹药炼制(可移动)"/>
	<otherItem id="1001" name="农场标牌" className="FarmLabel" H_width="1" V_height="1" isEnableMove="1" 
	  description="这里是农场！(可移动)"/>
	<otherItem id="1002" name="限时全开" className="UI.Farm.Btn.FarmOpenAll" H_width="3" V_height="3" isEnableMove="0" 
	  description="限时全开按钮！(不可移动)"/>

	<scene id="1000" className="CloseShot_1" name="近景1" 
	coords="7_9 7_8 8_8 5_7 6_7 7_7 8_7 9_7 5_6 6_6 7_6 8_6 9_6 6_5 7_5 8_5 8_4 7_4 9_4 8_3 9_3 7_2 8_2 9_2 7_1 8_1 9_1 7_-1 8_-1 9_-1 7_-2 8_-2 9_-2 6_-3 7_-3 8_-3 9_-3 5_-4 6_-4 7_-4 8_-4 9_-4 4_-5 5_-5 6_-5 7_-5 8_-5 9_-5 1_-6 2_-6 3_-6 4_-6 5_-6 6_-6 7_-6 8_-6 9_-6 1_-7 2_-7 3_-7 4_-7 5_-7 6_-7 7_-7 8_-7 9_-7 12_-4 -9_-7 -9_-6 -10_-6 -11_-5 -12_-4 -13_6 -12_7 -11_8 -10_9 -9_10 -8_11 -7_12 -6_13"/>
	
	<openLandPrice ticketPrices="0_10_50_100_200_300_400_500_600" ticketIds="1_366_367_368_369_370_371_372_373" />
	
	<openallinfo ticketPrice="499" ticketId="3236" />
	
	<farmBlock id="100" leftMinCo="-13" rightMaxCo="8" upMinCo="-7" downMaxCo="13" ticket="0" ticketId=""  />
	<!--土地多开
	<farmBlock id="101" leftMinCo="-16" rightMaxCo="-11" upMinCo="-8" downMaxCo="0"  ticket="100"/>
	<farmBlock id="102" leftMinCo="-16" rightMaxCo="-11" upMinCo="1" downMaxCo="10"  ticket="100"/> 
	-->
	
	
	<farmLevel>
	
	<farmLevel sunValue="0"  level="1" />
	<farmLevel sunValue="200" level="2" />
	<farmLevel sunValue="700" level="3" />
	<farmLevel sunValue="1700" level="4" />
	<farmLevel sunValue="3200" level="5" />
	<farmLevel sunValue="5200" level="6" />
	<farmLevel sunValue="9200" level="7" />
	
	<farmLevel sunValue="17200" level="8" />

	<farmLevel sunValue="20000" level="9" />
	<farmLevel sunValue="60000" level="10" />

	<farmMaxLevel maxLevel="7" maxSunValue="17200"/>
	</farmLevel>
	
	
	<!-- showNameOne:炼丹炉在空闲时的显示  showNameTwo:炼丹炉在炼制时的显示   showNameThree: 炼丹炉的完成炼制时的显示
	     impactValueToRefineRate ： 炼丹炉对炼制率的影响    impactValueToRefineTime:  炼丹炉对炼制时间的影响 -->
	<lianDanFurnace id="100100"  showNameOne="LianDanFurnace_Ordinary"  showNameOneToTwo="LianDanFurnace_OrdinaryToRefine" showNameTwo="LianDanFurnace_Refining"  showNameThree="LianDanFurnace_Complete" name="普通炼丹炉" level="1" impactValueToRefineRate="1" impactValueToRefineTime="1" atOnceCompleteRefinePriceValue="1_2_3_4_5_6_7_8"/>
</Farm>