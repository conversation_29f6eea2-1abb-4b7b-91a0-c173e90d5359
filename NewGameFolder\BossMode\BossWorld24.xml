<?xml version="1.0" encoding="utf-8" ?>
<data id="boss24" swfPath="NewGameFolder/BossMode/Scene3.swf"
	className="BossModeMap1" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound5.swf" className="SoundCC" />
    <startShow swfPath="NewGameFolder/BossMode/Boss27.swf" className="CBossStoryShow" />
	<bossHead swfPath="NewGameFolder/BossMode/Boss27.swf" className="CBossHead" />
	<lottery>
		<!-- 只能12个-->
		<!-- 幽冥宝珠青 -->
		<item id="10500088" num="2" proWeight="2" />
		<!-- 幽冥宝珠红 -->
		<item id="10500081" num="1" proWeight="4" />
		   <!-- 哪吒契约碎片 -->
        <item id="10500107" num="2" proWeight="1" />
        <!-- 深渊宝石 -->
        <item id="10500073" num="1" proWeight="20" />
          <!-- 六级攻击 -->
        <item id="11900031" num="1" proWeight="6" />
        <!-- 六级防御 -->
        <item id="11900034" num="1" proWeight="15" />
        <!-- 六级人品 -->
        <item id="11900037" num="1" proWeight="20" />
        <!-- 六级魔法 -->
        <item id="11900028" num="1" proWeight="25" />
        <!-- 六级生命-->
        <item id="11900025" num="1" proWeight="5" />
        <!--  多啦时装 -->
        <item id="10900011" num="1" proWeight="1" />
        <!-- 妖将领悟药剂 -->
        <item id="10500067" num="1" proWeight="8" />
        <!-- 魔龙宠物蛋-->
        <item id="10800011" num="1" proWeight="20" />
	</lottery>
	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>

	<screenskills>
		<screenskill>10101000</screenskill>
		<screenskill>10101100</screenskill>
		<screenskill>10101200</screenskill>
		<screenskill>10101300</screenskill>
		
		<screenskill>10102004</screenskill>
		<screenskill>10102104</screenskill>
		<screenskill>10102204</screenskill>
		<screenskill>10102304</screenskill>
		
		<screenskill>10104004</screenskill>
		<screenskill>10104104</screenskill>
		<screenskill>10104204</screenskill>
		<screenskill>10104304</screenskill>
		
		<screenskill>10105004</screenskill>
		<screenskill>10105104</screenskill>
		<screenskill>10105204</screenskill>
		<screenskill>10105304</screenskill>
		
		<screenskill>10106004</screenskill>
		<screenskill>10106104</screenskill>
		<screenskill>10106204</screenskill>
		<screenskill>10106304</screenskill>
		
		<screenskill>10107004</screenskill>
		<screenskill>10107104</screenskill>
		<screenskill>10107204</screenskill>
		<screenskill>10107304</screenskill>

		<screenskill>10109004</screenskill>
		<screenskill>10109104</screenskill>
		<screenskill>10109204</screenskill>
		<screenskill>10109304</screenskill>
	</screenskills>
	<!-- start表示从第几只开始  hp1表示第一只boss的血量界线 hp2表示第二只 RecoverHp表示第一只boss被切换掉之后每秒恢复百分之一的血-->
	<bossinfo start="1" hp1="70000" hp2="70000"  cdtime="20" />
	<boss type="YJFY.BossMode.Boss1.BossThorMan">
	<!--敌人数据 -->
    <bossData hpSegment="50000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_BossTeleport" hurtMulti="0" costMp="10" cdTime="5000" priorityForRun="0" priorityForRunInHurt="1"  
	isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
	<recover RecoverHp="1.1"/>
	<enemyData>
		<data att="totalHp" value="3500000" />
		<data att="attack" value="16000" />
		<data att="expOfDieThisEnemy" value="1200000" />
		<data att="defence" value="8000" />
		<data att="dogdeRate" value="0.4" />
		<data att="criticalRate" value="2.5" />
		<data att="criticalMuti" value="2.0" />
		<data att="deCriticalRate" value="2.2" />
		<data att="hitRate" value="2" />
		
		
		<data att="totalMp" value="400" />
		
     <!--回血回魔-->	
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="10" />
	</enemyData>
	<skillInvincible>true</skillInvincible>
	<skillinfo times="1" value="30" cd="8" /> <!-- 攻击times次 每次增加百分之value 每隔cd发动一次 -->
	<skill2info cd="15" remaintime="5" value="10" /> <!-- 技能2cd 持续remaintime 下降value -->
	<animal id="thorman1" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="200" walkSpeed="60"
		runSpeed="120">
		<attackRange x="-20" y="-10" z="-1" xRange="200" yRange="80" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>
		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.BossMode.Boss1.Skill_BossSkill1" x="0"
			y="0" z="0" xRange="0" yRange="0" zRange="0" bodyDefId="skill1Animation" skillAttackReachFrameLabel="skillReach"
			skillEndFrameLabel="skillEnd^stop^">
		</skill>

		<skill id="skill_boss1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-200" y="-200" z="-1" xRange="400" yRange="400" zRange="50" attackInterval="500"
				bodyDefId="skill1Animation" hurtDuration="1000" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skilllaserEnd^stop^"
				 bodySkillEndFrameLabel="skilllaserEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>
		<skill id="skill_boss2" className="YJFY.BossMode.Boss1.BossThorSkill" hurtDuration="1000"
			 x="-200" y="-200" z="-1" xRange="400" yRange="400" zRange="50"
			 skillTimeOfDuration="0" bodyDefId="skill5Animation" imageDurationTime="8000">
			<image id="imageSun1" >
				<attackRange x="-200" y="-200" z="-1" xRange="400" yRange="400" zRange="100" />
				<idle defId="sunFlow1" />
				<attack defId="sunFlow2" />
				<die defId="sunDisapear" />
				<animationDefinitions>
					<animationDefinition id="sunFlow1" rows="1"
						cols="1" walkable="false" overlap="false" frameInterval="1"
						defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
						<show swfPath="NewGameFolder/BossMode/Boss27.swf"
							showClass="Boss27Chuizi" x_offset="0" y_offset="0" />
					</animationDefinition>
					<animationDefinition id="sunFlow2" rows="1"
						cols="1" walkable="false" overlap="false" frameInterval="1"
						defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
						<show swfPath="NewGameFolder/BossMode/Boss27.swf"
							showClass="Boss27Skill1_effect" x_offset="0" y_offset="0" />
					</animationDefinition>
					<animationDefinition id="sunDisapear" rows="1"
						cols="1" walkable="false" overlap="false" frameInterval="1"
						defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
						<show swfPath="NewGameFolder/BossMode/Boss27.swf"
							showClass="sunDisapear" x_offset="0" y_offset="0" />
					</animationDefinition>
				</animationDefinitions>
			</image>
		</skill>

		<skill id="skill_changeshow" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill2Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillShowEnd^stop^"
				 bodySkillEndFrameLabel="skillShowEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<skill id="skill_changehide" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill3Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillhideEnd^stop^"
				 bodySkillEndFrameLabel="skillhideEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<skill id="skill_open" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill4Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillShowEnd^stop^"
				 bodySkillEndFrameLabel="skillShowEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27idle" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27walk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/BossMode/Boss27.swf" 
				showClass="Boss27run" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27beattack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27attack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27die" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27Skill2" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skill2Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27showagain" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skill3Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27showhide" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skill4Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27showagain" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skill5Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27Skill1" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="skillEffectAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27Skill1_effect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillEffectAnimation1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27Chuizi" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="sunDisapear" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="sunDisapear" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27TeleHide" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="Boss27TeleShow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss27.swf"
					showClass="CBossChallengeBossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss>


<boss type="YJFY.BossMode.Boss1.BossClairaudient">
	<!--敌人数据 -->
    <bossData hpSegment="50000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_BossTeleport" hurtMulti="0" costMp="10" cdTime="5000" priorityForRun="0" priorityForRunInHurt="1"  
	isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
	<recover RecoverHp="1.1"/>
	<enemyData>
			<data att="totalHp" value="3500000" />
		<data att="attack" value="15000" />
		<data att="expOfDieThisEnemy" value="1200000" />
		<data att="defence" value="6000" />
		<data att="dogdeRate" value="0.4" />
		<data att="criticalRate" value="2.5" />
		<data att="criticalMuti" value="2.0" />
		<data att="deCriticalRate" value="2.2" />
		<data att="hitRate" value="2" />
		
		
		<data att="totalMp" value="400" />
		
     <!--回血回魔-->	
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="10" />
	</enemyData>
	<skillInvincible>true</skillInvincible>
	<skill2info cd="8" isrunskill="2" cd2="5" /> <!-- cd表示第一个技能cd isrunskill是否开第二个技能 1表示开 2表示不开  cd2第二个技能的cd -->
	<animal id="clairaudient" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="200" walkSpeed="60"
		runSpeed="120">
		<attackRange x="-20" y="-10" z="-1" xRange="200" yRange="80" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>
		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.BossMode.Boss1.Skill_BossSkill1" x="0"
			y="0" z="0" xRange="0" yRange="0" zRange="0" bodyDefId="skill1Animation" skillAttackReachFrameLabel="skillReach"
			skillEndFrameLabel="skillEnd^stop^">
		</skill>

<!-- 		<skill id="skill_boss2" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-200" y="-200" z="-1" xRange="400" yRange="400" zRange="50" attackInterval="500"
				bodyDefId="skill1Animation" hurtDuration="1000" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEyeEnd^stop^"
				 bodySkillEndFrameLabel="skillEyeEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill> -->

	<!-- 	<skill id="skill_boss2" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-200" y="-200" z="-1" xRange="400" yRange="400" zRange="50" attackInterval="500"
				bodyDefId="skill5Animation" hurtDuration="1000" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEyeEnd^stop^"
				 bodySkillEndFrameLabel="skillEyeEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill> -->

		<skill id="skill_boss2" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao" x="-250"
			y="-100" z="-1" xRange="500" yRange="200" zRange="100" bodyDefId="skill1Animation"  hurtDuration="5000" 
			bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEyeEnd^stop^" effectAddtoTargetId="yunQuan"
			everyEntityAddShowIsFrontOfBody="0">
			<shakeView swfPath="NewGameFolder/AutomaticPetSource/ZengZhang3.swf" className="ShakeView" />
		    <animationDefinition id="yunQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/ZengZhang3.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>

		<skill id="skill_changeshow" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill2Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillShowEnd^stop^"
				 bodySkillEndFrameLabel="skillShowEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<skill id="skill_changehide" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill3Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillhideEnd^stop^"
				 bodySkillEndFrameLabel="skillhideEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<skill id="skill_open" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill4Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillShowEnd^stop^"
				 bodySkillEndFrameLabel="skillShowEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30idle" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30walk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/BossMode/Boss30.swf" 
				showClass="Boss30run" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30beattack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30attack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30die" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30Skill1" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skill2Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30showagain" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skill3Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30showhide" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skill4Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30showagain" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skill5Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30Skill1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30TeleHide" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="Boss30TeleShow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss30.swf"
					showClass="CBossChallengeBossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss>
</data>
