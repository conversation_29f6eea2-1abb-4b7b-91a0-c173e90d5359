<?xml version="1.0" encoding="utf-8" ?>
<data id="boss2" maxLevel="15" className="UI.WorldBoss.Boss.AbleAttackedBoss.AttackUpgradeBoss"  showSwf="WorldBoss2.swf"  
showClassName="Boss2CardShow" changeColorNum="1" >
	
	<attackViewData>
			 <shakeView className="UI.WorldBoss.ShakeView.ShakeView1" >
		        <att attName="setTimes" attValue="2" />
		        <att attName="setOffset" attValue="1" />
		        <att attName="setSpeed" attValue="32" />
		    </shakeView>
		    <flashView className="UI.WorldBoss.FlashView.FlashView1" >
		        <att attName="setColor" attValue="0x000000" />
		        <att attName="setTintAmount" attValue="0.1" />
		        <att attName="setAppearDuration" attValue="0.1" />
		         <att attName="setDisappearDuration" attValue="0.5" />
		    </flashView>
	 </attackViewData>
	 <daZhaoViewData>
			 <shakeView className="UI.WorldBoss.ShakeView.ShakeView1" >
		        <att attName="setTimes" attValue="2" />
		        <att attName="setOffset" attValue="2" />
		        <att attName="setSpeed" attValue="32" />
		    </shakeView>
		    <flashView className="UI.WorldBoss.FlashView.FlashView1" >
		        <att attName="setColor" attValue="0x000000" />
		        <att attName="setTintAmount" attValue="0.2" />
		        <att attName="setAppearDuration" attValue="0.1" />
		         <att attName="setDisappearDuration" attValue="0.5" />
		    </flashView>
	</daZhaoViewData>
     <data level="1"  attack="3000" defence="200" criticalRate="0.1" criticalMuti="0.1" riotValue="0.08" hit="0.12" dodge="0.015"  hurtVolume="100000" >
		 <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="1" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"  roundNumOfImageLife="1" />
		 
	 </data>
	 <data level="2"  attack="4000" defence="200" criticalRate="0.2" criticalMuti="0.2" riotValue="0.15" hit="0.14" dodge="0.016"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="2" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="2" />
	
	 </data>
	 <data level="3"  attack="5000" defence="200" criticalRate="0.3" criticalMuti="0.3" riotValue="0.15" hit="0.16" dodge="0.03"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="2" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="2" />
	
	 </data>
	 <data level="4"  attack="6000" defence="200" criticalRate="0.4" criticalMuti="0.4" riotValue="0.25" hit="0.18" dodge="0.018"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="2" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"    roundNumOfImageLife="3"/>
		
	 </data>
	 <data level="5"  attack="7000" defence="200" criticalRate="0.5" criticalMuti="0.4" riotValue="0.35" hit="0.20" dodge="0.019"  hurtVolume="100000" > 
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="3"/>
	
	 </data>
     <data level="6"  attack="8000" defence="200" criticalRate="0.6" criticalMuti="0.3" riotValue="0.45" hit="0.25" dodge="0.02"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="3"/>
		
	 </data>
	 <data level="7"  attack="9000" defence="200" criticalRate="0.7" criticalMuti="0.3" riotValue="0.55" hit="0.30" dodge="0.021"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="3"/>
		  
	 </data>
	 <data level="8"  attack="10000" defence="200" criticalRate="1.1" criticalMuti="0.3" riotValue="0.75" hit="0.35" dodge="0.022"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="3"/>
		
	 </data>
	 <data level="9"  attack="11000" defence="200" criticalRate="1.3" criticalMuti="0.3" riotValue="0.85" hit="0.40" dodge="0.023"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="3"/>
		
	 </data>
	 <data level="10"  attack="12000" defence="200" criticalRate="1.5" criticalMuti="5" riotValue="0.95" hit="0.45" dodge="0.024"  hurtVolume="100000" > 
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="3"/>
		 
	 </data>
     <data level="11"  attack="15000" defence="200" criticalRate="1.7" criticalMuti="0.3" riotValue="0.95" hit="0.80" dodge="0.025"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="3"/>
		
	 </data>
	 <data level="12"  attack="18000" defence="200" criticalRate="1.9" criticalMuti="0.3" riotValue="0.95" hit="1.05" dodge="0.026"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="4"/>
		 
	 </data>
	 <data level="13"  attack="22000" defence="200" criticalRate="2.1" criticalMuti="0.3" riotValue="0.95" hit="1.20" dodge="0.027"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="4" />
		 
	 </data>
	 <data level="14"  attack="26000" defence="200" criticalRate="2.1" criticalMuti="0.3" riotValue="0.95" hit="1.65" dodge="0.028"  hurtVolume="100000" >
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"  roundNumOfImageLife="4" />
		 
	 </data>
	 <data level="15"  attack="29200" defence="200" criticalRate="3.1" criticalMuti="5" riotValue="0.95" hit="5.7" dodge="0.05"  hurtVolume="100000" > 
		  <daZhao className="UI.WorldBoss.DaZhao.NotAttackDaZhao.CreateOwnImageDaZhao" roundNum="1"  createImageNum="3" 
		 imageBossClassName="UI.WorldBoss.Boss.CreateOwnImageBoss.ImageBoss"   roundNumOfImageLife="4"/>
		 
	 </data>
	 
	 
	
		 <imageBoss id="imageBoss1" maxLevel="15" className="UI.WorldBoss.Boss.AbleAttackedBoss.AttackUpgradeBoss"  showSwf="WorldBoss2.swf"  
		           showClassName="Boss2CpyCardShow" >
	
	        <attackViewData>
			 <shakeView className="UI.WorldBoss.ShakeView.ShakeView1" >
		        <att attName="setTimes" attValue="2" />
		        <att attName="setOffset" attValue="1" />
		        <att attName="setSpeed" attValue="32" />
		    </shakeView>
		    <flashView className="UI.WorldBoss.FlashView.FlashView1" >
		        <att attName="setColor" attValue="0x000000" />
		        <att attName="setTintAmount" attValue="0.1" />
		        <att attName="setAppearDuration" attValue="0.1" />
		         <att attName="setDisappearDuration" attValue="0.5" />
		    </flashView>
	      </attackViewData>
	      <daZhaoViewData>
			 <shakeView className="UI.WorldBoss.ShakeView.ShakeView1" >
		        <att attName="setTimes" attValue="2" />
		        <att attName="setOffset" attValue="2" />
		        <att attName="setSpeed" attValue="32" />
		    </shakeView>
		    <flashView className="UI.WorldBoss.FlashView.FlashView1" >
		        <att attName="setColor" attValue="0x000000" />
		        <att attName="setTintAmount" attValue="0.2" />
		        <att attName="setAppearDuration" attValue="0.1" />
		         <att attName="setDisappearDuration" attValue="0.5" />
		    </flashView>
	      </daZhaoViewData>
          <data level="1"  attack="600" defence="200" criticalRate="0.1" criticalMuti="0.1" riotValue="0.08" hit="0.006" dodge="0.015"  hurtVolume="100000" >
		       <!--<daZhao className="UI.WorldBoss.DaZhao.ChangeHurtDaZhao.RoundHurtMultiDaZhao" hurtMulti="2"  roundNum="5" />-->
		 </data>
	 </imageBoss>
	

	 
</data>