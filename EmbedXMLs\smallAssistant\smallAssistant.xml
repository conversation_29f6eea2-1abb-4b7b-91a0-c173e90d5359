<?xml version="1.0" encoding="utf-8" ?>
<data>
	<activeTaskData>
		<rewardDatas>
			<rewardData id="r1" needMinActiveValue="30">
			    <!-- 升级宝石-->
				<equipment id="10500008" num="3" />
				<!-- 魔法药水 -->
				<equipment id="11000016" num="1" />
				<!-- 幸运宝石-->
				<equipment id="10500000" num="1" />
	
	
			</rewardData>
			
			
			<rewardData id="r2" needMinActiveValue="60">
			    <!-- 宠物大经验丹-->
				<equipment id="11000006" num="1" />
				<!-- 魔法药水 -->
				<equipment id="11000016" num="3" />
				<!-- 背包钥匙-->
				<equipment id="11100003" num="1" />
			</rewardData>
			
			
			<rewardData id="r3" needMinActiveValue="100">
			    <!-- 要将内丹-->
				<equipment id="10500065" num="3" />
				<!-- 超进化鲜果 -->
				<equipment id="10500030" num="1" />
				<!--幸运蛋-->
				<equipment id="10800099" num="1" />
			</rewardData>
			
			
			<rewardData id="r4" needMinActiveValue="120">
			    <!-- 五色土-->
				<equipment id="10500085" num="3" />
				<!-- 魔法药水 -->
				<equipment id="10500064" num="1" />
				<!-- 幽冥宝珠青-->
				<equipment id="10500088" num="1" />
			</rewardData>
			
			

		</rewardDatas>
 



		<activeValueDatas>
			<activeValueData taskId="active1" value="10" skipStr="openSignPanel" />
			<activeValueData taskId="active2" value="10" skipStr="openTaskPanel" />
			<activeValueData taskId="active3" value="10" skipStr="openHatchPanel" />
			<activeValueData taskId="active4" value="10" skipStr="openFarm" />
			<activeValueData taskId="active5" value="10" skipStr="openWorldBoss" />
			<activeValueData taskId="active6" value="10" skipStr="openOnePK" />
			<activeValueData taskId="active7" value="15" skipStr="openRouteMap1" />
			<activeValueData taskId="active8" value="15" skipStr="openBoss" />
			<activeValueData taskId="active9" value="20" skipStr="openShopWall" />
			<activeValueData taskId="active10" value="10" skipStr="openWeaponMake" />


			<!--<activeValueData taskId="task2" value="3"/> <activeValueData taskId="task3" 
				value="3"/> <activeValueData taskId="task4" value="3"/> <activeValueData 
				taskId="task5" value="3"/> <activeValueData taskId="task6" value="3"/> <activeValueData 
				taskId="task7" value="3"/> <activeValueData taskId="task8" value="3"/> <activeValueData 
				taskId="task39" value="3"/> <activeValueData taskId="task40" value="3"/> -->
		</activeValueDatas>
	</activeTaskData>
	
	
	
	<!--等级礼包-->
	<levelTaskData>
		<rewardDatas>
			<rewardData id="r1" needMinLevel="10">
				<equipment id="10800000" num="1" />
				<equipment id="11000007" num="1" />
				<equipment id="11000008" num="1" />
				<equipment id="10500000" num="1" />
			</rewardData>


			<rewardData id="r2" needMinLevel="20">
				<equipment id="10500008" num="5" />
				<equipment id="11000007" num="3" />
				<equipment id="11000008" num="3" />
				<equipment id="10500000" num="1" />
			</rewardData>

			<rewardData id="r3" needMinLevel="30">
				<equipment id="10800008" num="1" />
				<equipment id="10500008" num="10" />
				<equipment id="10500001" num="10" />
				<equipment id="10500004" num="10" />
				<equipment id="10500000" num="1" />
			</rewardData>
			
			<rewardData id="r4" needMinLevel="40">
				<equipment id="10500000" num="3" />
				<equipment id="10500008" num="10" />
				<equipment id="10500023" num="1" />
				<equipment id="11000013" num="1" />
				<equipment id="11000002" num="1" />
				<equipment id="12000001" num="1" />
			</rewardData>
			
			<rewardData id="r5" needMinLevel="50">
				<equipment id="10500000" num="5" />
				<equipment id="10500008" num="15" />
				<equipment id="11000009" num="1" />
				<equipment id="11000013" num="1" />
				<equipment id="10500030" num="1" />
				<equipment id="11000004" num="1" />
			</rewardData>
			
			<rewardData id="r6" needMinLevel="60">
				<equipment id="10500000" num="8" />
				<equipment id="10500065" num="15" />
				<equipment id="10500064" num="3" />
				<equipment id="11000013" num="1" />
				<equipment id="10500035" num="1" />
				<equipment id="10500022" num="5" />
			</rewardData>
			
			<rewardData id="r7" needMinLevel="70">
				<equipment id="10500000" num="10" />
				<equipment id="10500065" num="15" />
				<equipment id="11100000" num="1" />
				<equipment id="11100002" num="3" />
				<equipment id="10500041" num="5" />
				<equipment id="10500022" num="10" />
			</rewardData>
			
			<rewardData id="r8" needMinLevel="80">
				<equipment id="11000004" num="30" />
				<equipment id="11100000" num="4" />
				<equipment id="11000031" num="1" />
				<equipment id="10900011" num="1" />
				<equipment id="10500134" num="20" />
				<equipment id="10500136" num="20" />
			</rewardData>
				
	         <rewardData id="r9" needMinLevel="90">
				<equipment id="11000004" num="30" />
				<equipment id="10500135" num="50" />
				<equipment id="10500134" num="50" />
				<equipment id="10500136" num="50" />
				<equipment id="11000030" num="5" />
				<equipment id="10900013" num="1" />
			</rewardData>
				

		</rewardDatas>
	</levelTaskData>
	
	<!--小贴士-->
	<tips>
		<tips>
			<tip text1="成为VIP，每日都有海量奖励拿哦" skipStr="openVIPPanel" />		
			<tip text1="挑战西游征途关卡，获得大量经验" skipStr="openRouteMap1" />		
			<tip text1="快去完成每日任务，获得丰厚奖励" skipStr="openTaskPanel" />
			<tip text1="挑战天宫BOSS，获得经验和道具" skipStr="openBoss" />
			<tip text1="去练功房修炼，轻松获得海量经验" skipStr="openCollectTimePanel" />
			<tip text1="闯过降魔副本，获得海量奖励道具" skipStr="openXiangMoLevelPanel" />

		</tips>
		<tips>
			<tip text1="成为VIP，每日都有海量奖励拿哦" skipStr="openVIPPanel" />		
			<tip text1="每日竞技获得装备和勋章提升自己" skipStr="openOnePK" />
			<tip text1="强化装备镶嵌宝石让你变得更强大" skipStr="openWeaponMake" />
			<tip text1="宠物幻化是提高人物属性的好办法" skipStr="openPetMiragePanel" />
			<tip text1="拥有一只强大的妖将是实力的象征" skipStr="openAutomaticPetPanel" />

		</tips>
		<tips>
			<tip text1="成为VIP，每日都有海量奖励拿哦" skipStr="openVIPPanel" />
			<tip text1="快去完成每日任务，获得丰厚奖励" skipStr="openTaskPanel" />
			<tip text1="挑战天宫BOSS，获得金币和道具" skipStr="openBoss" />
			<tip text1="闯过降魔副本，获得海量奖励道具" skipStr="openXiangMoLevelPanel" />
		</tips>
		<tips>
			<tip text1="挑战西游征途关卡，可获得装备哦" skipStr="openRouteMap1" />
			<tip text1="前去杂货店，老板出售各式装备哦" skipStr="openShopPanel" />
			<tip text1="商城中有各式各样的装备材料出售" skipStr="openShopWall" />
		</tips>
		<tips>
			<tip text1="前去商城中各种宠物等你来选择哦" skipStr="openShopWall" />		
			<tip text1="挑战西游征途关卡，可获得宠物蛋" skipStr="openRouteMap1" />

		</tips>
	</tips>
</data>