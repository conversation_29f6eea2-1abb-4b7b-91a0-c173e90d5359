<?xml version="1.0" encoding="utf-8" ?>
<data id="level1" swfPath="NewGameFolder/LevelMode2/Level2/Level1.swf"
	className="LevelMap" x="0" y="0" z="0" xRange="1920" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound7.swf" className="SoundQ" />	
	<startShow swfPath="NewGameFolder/LevelMode2/Level2/StartShow3.swf" className="StartShow" />
	<Waves totalWaveNum="5">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		
		<!-- 第一屏 -->
		
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="4000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="4000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="4000" duration="2500" num="1"  />
		</Wave>
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="8000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="8000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="8000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="xiaoBing2" startTime="8000" duration="2500" num="1"  />
		</Wave>		
			<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="boss2" startTime="8000" duration="2500" num="1"  />
		</Wave> 

		
		<!-- 第二屏 -->
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="4000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="4000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="4000" duration="2500" num="1"  />
		</Wave>
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="8000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="8000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="xiaoBing2" startTime="8000" duration="2500" num="1"  />
		</Wave>	
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="boss3" startTime="8000" duration="2500" num="1"  />
		</Wave> 

		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="boss4" startTime="8000" duration="2500" num="1"  />
		</Wave> 
		
		
		<!-- 第三屏 -->

		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="xiaoBing2" startTime="0" duration="2500" num="1"  />
		</Wave>
		
		

		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="4000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="4000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="xiaoBing2" startTime="4000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
					<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="boss" startTime="4000" duration="2500" num="1"  />
			</Wave> 
		
		
		
		
	</Waves>
    <!--抽奖配置-->
	<lottery>
		<!-- 只能12个-->
<!-- 龙王契约碎片 -->
		<item id="10500100" num="1" proWeight="3" />
		<!-- 11 -->
		<item id="10600098" num="1" proWeight="1" />
		   <!-- 天王契约碎片 -->
        <item id="10500107" num="3" proWeight="8" />
		<!-- moju-->
        <item id="10800010" num="1" proWeight="10" />
       <!-- 进阶丹 -->
        <item id="10500064" num="2" proWeight="3" />
        <!-- 内丹-->
        <item id="10500065" num="3" proWeight="3" />
		<!-- 幸运宝石-->
        <item id="10500000" num="8" proWeight="8" />
        <!--  小虾米 -->
        <item id="12000004" num="1" proWeight="5" />
        <!-- 超进化仙果 -->
        <item id="10500067" num="1" proWeight="3" />
		<!-- 深渊宝石-->
        <item id="10500073" num="1" proWeight="8" />
		<!-- 冰晶凤凰蛋 -->
        <item id="10800011" num="1" proWeight="3" />
		<!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
		
		
	
       

	</lottery>
   <EqDrop>
   <xiaoBing noDropProWeight="500">
		   <!--proWeight 概率权重-->
	       
		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp4_S" proWeight="10" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp4_S" proWeight="10" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack4_S" proWeight="10" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp4_S" proWeight="10" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence4_S" proWeight="10" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="10" />   
	
	  <!-- 凤凰蛋 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.Egg_FengHuang_S"
			  proWeight="1" />
		  <item dropClassName="UI.Equipments.SceneEquipments.Egg_ChongMing_S"
			  proWeight="1" />	
		 <item dropClassName="UI.Equipments.SceneEquipments.Egg_Molong_S"
			  proWeight="1" />	
		  <!-- 蓝宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp3_S"
			  proWeight="8" />
		  <!-- 人品宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp3_S"
			  proWeight="8" />
		  <!-- 攻击宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack3_S"
			  proWeight="8" />
		  <!-- 生命宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp3_S"
			  proWeight="11" />
		  <!-- 防御宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence3_S"
			  proWeight="12" />
		  <!-- 开孔灵符 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S"
			  proWeight="12" />
		  <!-- 碎石锤 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S"
			  proWeight="6" />
		  
		  
		  
		  
		  

  	      <!--  红药 -->   
   	      <item dropClassName="Item_HpUp" proWeight="50" />
   	      <!--  金币 -->  
   	      <item dropClassName="Item_MoneyUp" proWeight="50" />
   	      <!--  蓝药 -->  
   	      <item dropClassName="Item_MpUp" proWeight="100" />   	      	        

   	      
 
 
   	          
	  </xiaoBing>
	   <boss noDropProWeight="20">
		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="5" />
				   <numData num="2" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="4" proWeight="8" />
				   <numData num="5" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>		  
	       <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp6_S" proWeight="5" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp6_S" proWeight="5" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack6_S" proWeight="5" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp6_S" proWeight="5" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence6_S" proWeight="5" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="10" /> 
   	      <!-- 碎石锤 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S" proWeight="10" /> 
		  
	  <!-- 凤凰蛋 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.Egg_FengHuang_S"
			  proWeight="5" />
		  <item dropClassName="UI.Equipments.SceneEquipments.Egg_ChongMing_S"
			  proWeight="5" />	
		 <item dropClassName="UI.Equipments.SceneEquipments.Egg_Molong_S"
			  proWeight="5" />	  
		  <!-- 蓝宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp7_S"
			  proWeight="1" />
		  <!-- 人品宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp7_S"
			  proWeight="1" />
		  <!-- 攻击宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack7_S"
			  proWeight="1" />
		  <!-- 生命宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp7_S"
			  proWeight="1" />
		  <!-- 防御宝石 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence7_S"
			  proWeight="1" />
		  <!-- 开孔灵符 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S"
			  proWeight="12" />
		  <!-- 碎石锤 -->
		  <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S"
			  proWeight="6" />
   	      
   	        
   	      <!-- 高级防御书 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.PetSkillBook_6" proWeight="1" />   
	   </boss>
	   
   </EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   <xiaoBing1>
	<!--敌人数据 -->
	  <hpShowData frameLabel="pianfu" />
	  <hurtAnimation2 defId="hurt2_enemy" playFrameLabel="1" recoverFrameLabel="recover^stop^" />
	  <enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="5000" />
		<data att="unableAttackMaxInterval" value="10000" />
	</enemyAttackData>
	<enemyData>
	     
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
	<data att="totalHp" value="6400000" />
		<data att="attack" value="12000" />
		<data att="expOfDieThisEnemy" value="60000" />
		<data att="defence" value="4000" />
		<data att="dogdeRate" value="0.05" />
		<data att="criticalRate" value="0.8" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="1.3" />
		<data att="hitRate" value="0.1" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy1" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="60" bodyHeight="80" walkSpeed="40"
		runSpeed="80">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->



		<isFly>true</isFly><!-- 是否是天上飞的怪物 -->
		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-35" z="-1" xRange="100" yRange="100"
			zRange="100" />

		<idle defId="idle_enemy" />
		<walk defId="walk_enemy" />
		<run defId="run_enemy" />
		<attack defId="attack_enemy" />
		<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>
		<hurt2 defId="hurt2_enemy" />

		<die defId="die_enemy" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="idle_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/SmallShrimp.swf"
					showClass="IdleOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/SmallShrimp.swf"
					showClass="WalkOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
            <animationDefinition id="run_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/SmallShrimp.swf"
					showClass="RunOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/SmallShrimp.swf"
					showClass="Hurt1OfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/SmallShrimp.swf"
					showClass="Hurt2OfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/SmallShrimp.swf"
					showClass="AttackOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/SmallShrimp.swf"
					showClass="DieOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/SmallShrimp.swf"
					showClass="ShadowOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
		</animationDefinitions>



	</animal>
</xiaoBing1>
   <xiaoBing2>
	<!--敌人数据 -->
	<hurtAnimation2 defId="hurt2_enemy" playFrameLabel="1" recoverFrameLabel="recover^stop^"/>
	<bossData hpSegment="1000"> <!--用于血条显示，一条的容量-->
		<skill skillId="XiaobingSkill1" hurtMulti="2" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
	isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
	<hpShowData frameLabel="tiefuguai" />
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="5000" />
		<data att="unableAttackMaxInterval" value="10000" />
	</enemyAttackData>
	<enemyData>
	      
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
	   	<data att="totalHp" value="8400000" />
		<data att="attack" value="15000" />
		<data att="expOfDieThisEnemy" value="60000" />
		<data att="defence" value="6000" />
		<data att="dogdeRate" value="0.05" />
		<data att="criticalRate" value="0.8" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="1.3" />
		<data att="hitRate" value="0.5" />
		<data att="totalMp" value="10000" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy2" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="60" bodyHeight="80" walkSpeed="30"
		runSpeed="60">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->


 

		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-50" z="-1" xRange="270" yRange="120"
			zRange="100" />

		<idle defId="idle_enemy" />
		<walk defId="walk_enemy" />
		<run defId="run_enemy" />
		<attack defId="attack_enemy" />
		<attackEffect defId="enemyAttackEffect" />
		<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow" />
		
		<skill id="XiaobingSkill1" className="YJFY.BossMode.Boss1.BossDaZhao3"  bodyDefId="daZhaoBodyShow" dropShowDefId="daZhaoDropShow" attTimer="1" moveSpeed="200">
			<dropAttackRange x="-50" y="-50" z="-1" xRange="100" yRange="100" zRange="100" />
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="idle_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="IdleOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="WalkOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
            <animationDefinition id="run_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="RunOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="Hurt1OfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="Hurt2OfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="AttackOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="DieOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="ShadowOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="enemyAttackEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/Tortoise.swf"
					showClass="AttackEffectOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</xiaoBing2>
<boss >
	<!--敌人数据 -->
	<hpShowData frameLabel="mihuojindi" />
   <bossData hpSegment="1000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_Boss_1" hurtMulti="2" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
				 isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="Skill_Boss_2" hurtMulti="2" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
				 isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="Skill_Boss_3" hurtMulti="2" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
				 isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
	<hurtAnimation2 defId="hurt2_boss" playFrameLabel="1" recoverFrameLabel="recover^stop^"/>
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="3000" />
		<data att="unableAttackMaxInterval" value="6000" />
	</enemyAttackData>
	<enemyData>
		 
		 
	   <data att="totalHp" value="16800000" />
		<data att="attack" value="16000" />
		<data att="expOfDieThisEnemy" value="100000" />
		<data att="defence" value="10000" />
		<data att="dogdeRate" value="0.25" />
		<data att="criticalRate" value="0.8" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="1.8" />
		<data att="hitRate" value="1.9" />
		 
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="2" />
	</enemyData>
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="120" walkSpeed="60"
		runSpeed="120">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="80" y="-100" z="-1" xRange="160" yRange="200" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt1_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
		<skill id="Skill_Boss_1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="-250" y="-250" z="-1" xRange="500" yRange="500" zRange="50" attackInterval="500"
				bodyDefId="skill1Animation" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<skill id="Skill_Boss_2" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="-250" y="-250" z="-1" xRange="500" yRange="500" zRange="50" attackInterval="500"
				bodyDefId="skill2Animation" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>

		<skill id="Skill_Boss_3" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="-250" y="-250" z="-1" xRange="500" yRange="500" zRange="50" attackInterval="500"
				bodyDefId="skill3Animation" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
					showClass="IdleOfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
					showClass="WalkOfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf" 
				showClass="RunOfLongWang" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt1_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
					showClass="HurtOfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
					showClass="DownOfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
					showClass="AttackOfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
					showClass="DieOfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill1Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
						showClass="Skill1OfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill2Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
						showClass="Skill2OfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill3Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
						showClass="Skill3OfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mihuojindi.swf"
					showClass="ShadowOfLongWang" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss>


<boss2 >
	<!--敌人数据 -->
	<hpShowData frameLabel="mengpo" />
   <bossData hpSegment="1000"> <!--用于血条显示，一条的容量-->
		<skill skillId="BossSkill1" hurtMulti="2" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
	isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
	<hurtAnimation2 defId="hurt2_boss" playFrameLabel="1" recoverFrameLabel="recover^stop^"/>
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="3000" />
		<data att="unableAttackMaxInterval" value="6000" />
	</enemyAttackData>
	<enemyData>
		 
		 
	   <data att="totalHp" value="10600000" />
		<data att="attack" value="16000" />
		<data att="expOfDieThisEnemy" value="100000" />
		<data att="defence" value="10000" />
		<data att="dogdeRate" value="0.25" />
		<data att="criticalRate" value="0.8" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="1.3" />
		<data att="hitRate" value="1.3" />
		 
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="2" />
	</enemyData>
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="120" walkSpeed="60"
		runSpeed="120">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="80" y="-100" z="-1" xRange="160" yRange="200" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt1_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    
		<skill id="BossSkill1" className="YJFY.BossMode.Boss1.BossDaZhao1"  bodyDefId="daZhaoBodyShow" dropShowDefId="daZhaoDropShow" >
			<dropAttackRange x="-50" y="-80" z="-1" xRange="100" yRange="160" zRange="100" />
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="IdleOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="IdleOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf" 
				showClass="RunOfJiaoRen" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt1_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="Hurt1OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="Hurt2OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="AttackOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="DieOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/mengpo.swf"
					showClass="ShadowOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss2>

<boss3>
	<!--敌人数据 -->
	<hpShowData frameLabel="baiwuchang" />
   <bossData hpSegment="1000"> <!--用于血条显示，一条的容量-->
		<skill skillId="BossSkill1" hurtMulti="2" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
	isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
 	<hurtAnimation2 defId="hurt2_boss" playFrameLabel="1" recoverFrameLabel="recover^stop^"/> 
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="3000" />
		<data att="unableAttackMaxInterval" value="6000" />
	</enemyAttackData>
	<enemyData>
		 
		 
	  	      <data att="totalHp" value="12200000" />
		<data att="attack" value="20000" />
		<data att="expOfDieThisEnemy" value="100000" />
		<data att="defence" value="8000" />
		<data att="dogdeRate" value="0.25" />
		<data att="criticalRate" value="2.2" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="1.3" />
		<data att="hitRate" value="1.1" />
		 
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="2" />
	</enemyData>
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="120" walkSpeed="60"
		runSpeed="60">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="0" z="-1" xRange="200" yRange="90" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt1_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
		<skill id="BossSkill1" className="YJFY.BossMode.Boss1.Skill_BossBWCZhengPing"  bodyDefId="daZhaoBodyShow" dropShowDefId="daZhaoDropShow" shakeFrameLabel="shake" >
			<dropAttackRange x="0" y="0" z="-1" xRange="400" yRange="220" zRange="100" />
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<shakeView swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf" className="ShakeView" />
		</skill>
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="IdleOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf" 
				showClass="RunOfJiaoRen" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt1_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="Hurt1OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="Hurt2OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="AttackOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="DieOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/baiwuchang.swf"
					showClass="ShadowOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss3>

<boss4 >
	<!--敌人数据 -->
	<hpShowData frameLabel="heiwuchang" />
   <bossData hpSegment="1000"> <!--用于血条显示，一条的容量-->
		<skill skillId="BossSkill1" hurtMulti="2" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
	isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
	<hurtAnimation2 defId="hurt2_boss" playFrameLabel="1" recoverFrameLabel="recover^stop^"/> 
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="3000" />
		<data att="unableAttackMaxInterval" value="6000" />
	</enemyAttackData>
	<enemyData>
		 
		 
	   <data att="totalHp" value="13800000" />
		<data att="attack" value="18000" />
		<data att="expOfDieThisEnemy" value="100000" />
		<data att="defence" value="10000" />
		<data att="dogdeRate" value="0.25" />
		<data att="criticalRate" value="1.8" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="1.6" />
		<data att="hitRate" value="1.1" />
		 
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="2" />
	</enemyData>
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="120" walkSpeed="60"
		runSpeed="120">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="0" z="-1" xRange="270" yRange="90" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt1_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
		<skill id="BossSkill1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" x="0"
			y="0" z="-1" xRange="460" yRange="150" zRange="100" bodyDefId="daZhaoBodyShow"  hurtDuration="1000"  
			skillAttackEffectDefId="bossSkill1AttackEffect" randomPlaceXRange="10" randomPlaceYRange="-60" attackInterval="500"
			skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^" >
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
		</skill>
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/heiwuchang.swf"
					showClass="IdleOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/LevelMode2/Level2/heiwuchang.swf" 
				showClass="RunOfJiaoRen" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt1_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/heiwuchang.swf"
					showClass="Hurt1OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/heiwuchang.swf"
					showClass="Hurt2OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/heiwuchang.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/heiwuchang.swf"
					showClass="AttackOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/heiwuchang.swf"
					showClass="DieOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level2/heiwuchang.swf"
					showClass="ShadowOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss4>

</data>
