<?xml version="1.0" encoding="utf-8" ?>
<data id="Boss19" swfPath="NewGameFolder/BossMode/Scene2.swf"
	className="BossModeMap1" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound5.swf" className="SoundCC" />	

    <startShow swfPath="NewGameFolder/BossMode/Boss19.swf" className="CBossStoryShow" />
    <bossHead swfPath="NewGameFolder/BossMode/Boss19.swf" className="CBossHead" />
    <!--抽奖配置-->
	<lottery>
		
		<!-- 只能12个-->
		<!-- 幽冥宝珠青 -->
		<item id="10500088" num="2" proWeight="3" />
		<!-- 幽冥宝珠红 -->
		<item id="10500081" num="1" proWeight="5" />
		   <!-- 哪吒契约碎片 -->
        <item id="10500107" num="1" proWeight="1" />
        <!-- 深渊宝石 -->
        <item id="10500073" num="1" proWeight="20" />
          <!-- 五级攻击 -->
        <item id="11900014" num="1" proWeight="6" />
        <!-- 五级防御 -->
        <item id="11900019" num="1" proWeight="15" />
        <!-- 五级人品 -->
        <item id="11900024" num="1" proWeight="20" />
        <!-- 五级魔法 -->
        <item id="11900009" num="1" proWeight="25" />
        <!-- 五级生命-->
        <item id="11900004" num="1" proWeight="5" />
        <!--  雪人时装 -->
        <item id="10900007" num="1" proWeight="1" />
        <!-- 妖将领悟药剂 -->
        <item id="10500067" num="1" proWeight="8" />
        <!-- 重明鸟宠物蛋-->
        <item id="10800010" num="1" proWeight="20" />
	</lottery>
	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
	
   <boss type="YJFY.BossMode.Boss1.Boss1">
	<!--敌人数据 -->
	<!--技能伤害hurtMulti=X倍数*attack-->
  <bossData hpSegment="50000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_BossTeleport" hurtMulti="0" costMp="20" cdTime="12000" priorityForRun="0" priorityForRunInHurt="1"  
	      isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" />
	      
		<skill skillId="Skill_BossChangeSupper" hurtMulti="0" costMp="20" cdTime="0" priorityForRun="0" priorityForRunInHurt="1"  
	      isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
	      
		<skill skillId="Skill_BossBackSupper" hurtMulti="0" costMp="20" cdTime="0" priorityForRun="0" priorityForRunInHurt="1"  
	      isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
	  
	  <skill skillId="Skill_BossSkill1" hurtMulti="1" costMp="20" cdTime="8000" priorityForRun="1" priorityForRunInHurt="0"  
	   isInvincibleInRun="1" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
	   
 	  <skill skillId="BossGenSuiTarget" hurtMulti="1" costMp="20" cdTime="6000" priorityForRun="1" priorityForRunInHurt="0" 
 	   isInvincibleInRun="1" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" timeForBianshen="12" timeForHurt="12" hpPercentage="10"/>  
		<skill skillId="BossDaZhao" hurtMulti="2" costMp="30" cdTime="10000" priorityForRun="2" priorityForRunInHurt="0"  
	isInvincibleInRun="1" isAbleRunInHurt="1"  className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO"/> <!--cdTime 毫秒-->
		</bossData>
		
    <!--BOSS属性-->	
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
	<enemyData>
	<data att="totalHp" value="2500000" />
		<data att="attack" value="7500" />
		<data att="expOfDieThisEnemy" value="1200000" />
		<data att="defence" value="5000" />
		<data att="dogdeRate" value="0.4" />
		<data att="criticalRate" value="2.1" />
		<data att="criticalMuti" value="1.7" />
		<data att="deCriticalRate" value="2.2" />
		<data att="hitRate" value="1" />
		
		
		<data att="totalMp" value="400" />
		
     <!--回血回魔-->	
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="10" />
	</enemyData>
	
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="80" bodyHeight="120" walkSpeed="60"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		<notBePushed>true</notBePushed><!--不能被推 -->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="-20" y="-10" z="-1" xRange="200" yRange="80" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
		</skill>
	    <skill id="Skill_BossChangeSupper" className="YJFY.BossMode.Boss1.Skill_BossChangeSupper" changeBodyId="changeAnimation" >
		</skill>
	    <skill id="Skill_BossBackSupper" className="YJFY.BossMode.Boss1.Skill_BossBackSupper" backBodyId="backAnimation">
		</skill>
		
		<skill id="BossGenSuiTarget" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" x="-180"
			y="-120" z="-1" xRange="360" yRange="240" zRange="100" bodyDefId="bianShenZhuan"  hurtDuration="1000"  
			skillAttackEffectDefId="bossSkill1AttackEffect" randomPlaceXRange="10" randomPlaceYRange="-60" attackInterval="500"
			skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="bianShenskillEnd^stop^" >
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao" x="-160"
			y="-1200" z="-1" xRange="320" yRange="240" zRange="100" bodyDefId="skill1Animation"  hurtDuration="5000" 
			bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^" effectAddtoTargetId="yuanQuan"
			everyEntityAddShowIsFrontOfBody="0">
			<shakeView swfPath="NewGameFolder/BossMode/Boss19.swf" className="ShakeView" />
		    <animationDefinition id="yuanQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<skill id="BossDaZhao" className="YJFY.BossMode.Boss1.BossNaZhaKunBang"  bodyDefId="daZhaoBodyShow" addBuffId="kunbang" dropShowDefId="daZhaoDropShow" attTimer="4" moveSpeed="500">
			<dropAttackRange x="-50" y="-50" z="-1" xRange="100" yRange="100" zRange="100" />
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="kunbang" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="KunBang" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</skill>
		
		
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/BossMode/Boss19.swf" 
				showClass="CBossRun" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossBeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossDead" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="bianShenZhuan" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="SkillNormalWaitEffect_bianshen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="changeAnimation" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/BossMode/Boss19.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>
			<animationDefinition id="backAnimation" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/BossMode/Boss19.swf"
						showClass="Back" x_offset="0" y_offset="0" />
				</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="DisappearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="AppearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossChallengeBossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
			<animationDefinition id="kunbang" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="KunBang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="yuanQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>
		<shows>

				<show defId="walk_boss" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossWalk_bianshen" x_offset="0" y_offset="0" />
				<show defId="run_boss" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossRun_bianshen" x_offset="0" y_offset="0" />
				<show defId="idle_boss" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossStand_bianshen" x_offset="0" y_offset="0" />
				<show defId="attack_boss" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossAttack_bianshen" x_offset="0" y_offset="0" />
				<show defId="bianShenZhuan" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="SkillNormalWaitEffect_bianshen" x_offset="0" y_offset="0" />
		</shows>
		<backshows>
				<show defId="walk_boss" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
				<show defId="run_boss" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossRun" x_offset="0" y_offset="0" />
				<show defId="idle_boss" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<show defId="attack_boss" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
				<show defId="skill1Animation" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
				<show defId="daZhaoBodyShow" eqClassName="change"
					swfPath="NewGameFolder/BossMode/Boss19.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
		</backshows>


	</animal>
</boss>
</data>
