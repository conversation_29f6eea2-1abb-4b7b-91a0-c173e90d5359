<?xml version="1.0" encoding="utf-8" ?>
<data id="Boss13" swfPath="NewGameFolder/BossMode/Scene2.swf"
	className="BossModeMap1" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound5.swf" className="SoundCC" />	

    <startShow swfPath="NewGameFolder/BossMode/Boss13.swf" className="CBossStoryShow" />
    <bossHead swfPath="NewGameFolder/BossMode/Boss13.swf" className="CBossHead" />
    <!--抽奖配置-->
	<lottery>
		
		<!-- 只能12个-->
		<!-- 龙王契约碎片 -->
		<item id="10500063" num="2" proWeight="3" />
		<!-- 天王契约碎片 -->
		<item id="10500079" num="2" proWeight="10" />
		<!-- 妖将契约（龙王） -->
        <item id="12000003" num="1" proWeight="20" />
        <!-- 妖将契约（鲛人 ）-->
        <item id="12000002" num="1" proWeight="20" />
        <!-- 深渊宝石 -->
        <item id="10500073" num="1" proWeight="1" />
        <!-- 四级攻击 -->
        <item id="11900013" num="1" proWeight="5" />
        <!-- 四级防御 -->
        <item id="11900018" num="1" proWeight="5" />
        <!-- 四级人品 -->
        <item id="11900023" num="1" proWeight="5" />
        <!-- 四级魔法 -->
        <item id="11900008" num="1" proWeight="5" />
       <!-- 进阶丹 -->
        <item id="10500064" num="1" proWeight="5" />
        <!-- 四级生命-->
        <item id="11900003" num="1" proWeight="3" />
        <!-- 冰晶凤凰宠物蛋-->
        <item id="10800009" num="1" proWeight="1" />
	</lottery>
	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
	
   <boss type="YJFY.BossMode.Boss1.Boss1">
	<!--敌人数据 -->
	<!--技能伤害hurtMulti=X倍数*attack-->
   <bossData hpSegment="50000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_BossTeleport" hurtMulti="0" costMp="20" cdTime="5000" priorityForRun="0" priorityForRunInHurt="1"  
	      isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="BossDaZhao" hurtMulti="1" costMp="20" cdTime="12000" priorityForRun="1" priorityForRunInHurt="0"  
	   isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="Skill_BossBall" hurtMulti="2" costMp="30" cdTime="3000" priorityForRun="2" priorityForRunInHurt="0"  
	  isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO"/> <!--cdTime 毫秒-->
		
		</bossData>
		
    <!--BOSS属性-->		
	<enemyData>
		<data att="totalHp" value="800000" />
		<data att="attack" value="5500" />
		<data att="expOfDieThisEnemy" value="1200000" />
		<data att="defence" value="5000" />
		<data att="dogdeRate" value="0.4" />
		<data att="criticalRate" value="1.5" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0.8" />
		<data att="hitRate" value="0.05" />
		
		
		<data att="totalMp" value="300" />
		
     <!--回血回魔-->	
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="5" />
	</enemyData>
	
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="80" bodyHeight="120" walkSpeed="60"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="-20" y="-10" z="-1" xRange="200" yRange="20" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
			
		</skill>
		
		<skill id="BossDaZhao" className="YJFY.BossMode.Boss1.BossDaZhao3"  bodyDefId="skill1Animation" addBuffId="skillEffect" dropShowDefId="daZhaoDropShow" attTimer="3" moveSpeed="350">
			<dropAttackRange x="-50" y="-50" z="-1" xRange="100" yRange="100" zRange="100" />
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="Skill5Effect_FlashLoop" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</skill>
		
		<skill id="Skill_BossBall" className="YJFY.BossMode.Boss1.BossDaZhao4"  bodyDefId="skill3Animation" dropShowDefId="daZhaoBallShow" bombShowDefId="daZhaoBombShow">
			<dropAttackRange x="-50" y="-50" z="-1" xRange="100" yRange="100" zRange="100" />
			<animationDefinition id="daZhaoBallShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="ItemSkill2Ball" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBombShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="ItemSkill2Bomb" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/BossMode/Boss13.swf" 
				showClass="CBossRun" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="CBossBeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="CBossDead" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="Skill5Effect_FlashLoop" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBallShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="ItemSkill2Ball" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBombShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="ItemSkill2Bomb" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill3Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="CBossSkill3Show" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="DisappearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="AppearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="CBossChallengeBossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
			<animationDefinition id="yunQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss13.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss>
</data>
