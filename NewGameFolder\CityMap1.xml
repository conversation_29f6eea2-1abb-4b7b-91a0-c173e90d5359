<?xml version="1.0" encoding="utf-8" ?>
<data swfPath="NewGameFolder/CityMap1.swf" className="CityMap1" x ="0" y="0" z="0" xRange="2924" yRange="400" zRange="100">
	<mainGameProjectLayerData  sx="0" sy="" sz="100"   name="middleMap">
		<npc name="storeNpcBtn"   />
		<npc name="shopNpcBtn"  />
		<npc name="weaponNpcBtn"   />
		<npc name="petNpcBtn"  />
		<npc name="vipNpcBtn"  />
		<npc name="taskNpcBtn"  />
		<npc name="rankListNpcBtn" />
		<npc name="societyNpcBtn" />
		<npc name="shareStoreNpcBtn"  />
		<npc name="pkMode2Btn" />
		<npc name="resetNpcBtn" />
		<npc name="dreamLandNpcBtn" />
		<npc name="adNpcBtn" />
		<npc name="equipMagicNpcBtn" />
		<npc name="nextAreaChooseBtn" isNextAreaDoor="1"/>
		<npc name="levelChooseBtn" isNextAreaDoor="1"/>
	</mainGameProjectLayerData>
	<!--背景-->
    <projectLayer sx="0" sy="2245" sz="500" name="backMap" />
	<projectLayer sx="0" sy="-220" sz="60"  name="frontMap" />
	<backgroundMusic id="cityMapMusic1" name="cityMapMusic1"
				swfPath="NewGameFolder/CityMapMusic.swf" className="CityMapMusic" />
	
	<playerInitData player1InitPositionX="2700" player1InitPositionY="300" player1InitPositionZ="0" 
	player2InitPositionX="2650" player2InitPositionY="250" player2InitPositionZ="0" />
	
	
	 
  <sharedAnimationDefinitions>
        
  </sharedAnimationDefinitions>
</data>