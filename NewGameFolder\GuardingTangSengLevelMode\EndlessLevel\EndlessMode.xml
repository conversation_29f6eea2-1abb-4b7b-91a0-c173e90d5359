<?xml version="1.0" encoding="utf-8" ?>
<data>
    <fuBen fuBenId="rongYanDiXue" maxPhysical="160" needPhysical="90" needPrice="20" goodsId="2111" preLevelPrice="20" preLevelId="2112" jumpLevelPrice="50" jumpLevelId="2113" 
     showFrameLabel="rongYanDiXue" description="地底深处熔岩涌动，幽冥恶鬼封印其中，鬼怪不除阴霾不散，突破此界试炼方有小成。">
        <eq>
       		<showEq  id="11000002" num="2"/>
			<showEq  id="11000005" num="2"/>
			<showEq  id="10800099" num="1"/>
			<showEq  id="11400000" num="2"/> 
			<showEq  id="10900008" num="1"/>
			<showEq  id="10500000" num="10"/>
        </eq>
        <level>
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level1.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level2.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level3.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level4.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level5.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level6.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level7.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level8.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level9.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level10.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level11.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level12.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level13.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level14.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level15.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level16.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level17.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level18.xml" />
			
        </level>
		
	</fuBen>
    <fuBen fuBenId="wangYangZhiSeng"  showFrameLabel="wangYangZhiSeng" description="海洋深处鱼龙混杂，幻象尽显珍奇无数，未知恐惧也伴随其中，无畏畅游其中方显神将本色。">
   		<eq>
       		<showEq  id="11000004" num="1"/>
            <showEq  id="12000003" num="1"/>
			<showEq  id="10800099" num="1"/>
			<showEq  id="10900009" num="1"/>
			<showEq  id="10500000" num="15"/>
			<showEq  id="10500030" num="5"/>
			
        </eq>
        <level>
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level1.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level2.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level3.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level4.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level5.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level6.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level7.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level8.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level9.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level10.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level11.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level12.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level13.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level14.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level15.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level16.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level17.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level18.xml" />
        </level>
	</fuBen>
    <fuBen fuBenId="fanHuaShiSu" showFrameLabel="fanHuaShiSu" description="人世繁华绚烂多姿，但执着生老病死，看似安乐内心却难以超脱，亲历所有则顿悟自生。">
		<eq>
            <showEq  id="10800099" num="1"/>
            <showEq  id="10900012" num="1"/>
            <showEq  id="10500000" num="50"/>
			<showEq  id="11900013" num="2"/>
			<showEq  id="11900003" num="2"/>
			<showEq  id="11900018" num="2"/>
        </eq>
        <level>
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level1.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level2.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level3.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level4.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level5.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level6.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level7.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level8.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level9.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level10.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level11.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level12.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level13.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level14.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level15.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level16.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level17.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level18.xml" />
        </level>
	</fuBen>
	
	 <fuBen fuBenId="shiGuanShanDian" showFrameLabel="shiGuanShanDian" description="超脱俗世历尽繁华，终识得自我，不执着生死权贵却不知归属何地，任凭自由飘荡于树冠山巅。">
		<eq>
       		<showEq  id="10900002" num="1"/>
            <showEq  id="11900031" num="2"/>
            <showEq  id="11900034" num="2"/>
            <showEq  id="11900025" num="2"/>
            <showEq  id="11900028" num="2"/>
            <showEq  id="11900037" num="2"/>
        </eq>
        <level>
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level1.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level2.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level3.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level4.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level5.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level6.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level7.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level8.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level9.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level10.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level11.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level12.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level13.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level14.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level15.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level16.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level17.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level18.xml" />
        </level>
	</fuBen>
	 <fuBen fuBenId="yingAoJiuTian" showFrameLabel="yingAoJiuTian" description="凌云直上遨游天地之间，不在意何来何往，不理会人间天庭，天地之大任我自由翱翔其中。">
		<eq>
       		<showEq  id="10500084" num="4"/>
            <showEq  id="10500080" num="4"/>
            <showEq  id="10500081" num="2"/>
            <showEq  id="10500082" num="2"/>
            <showEq  id="10500083" num="2"/>
            <showEq  id="10500088" num="2"/>
        </eq>
        <level>
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level1.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level2.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level3.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level4.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level5.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level6.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level7.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level8.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level9.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level10.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level11.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level12.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level13.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level14.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level15.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level16.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level17.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level18.xml" />
        </level>
	</fuBen>
	 <fuBen fuBenId="jiuXiaoYunWai" showFrameLabel="jiuXiaoYunWai" description="万物看尽无我其中，天地易识万物囊括于心，天地翱翔却难寻真我，万物膜拜踏云直上九天。">
		<eq>
       		<showEq  id="10600096" num="1"/>
            <showEq  id="10900003" num="1"/>
            <showEq  id="10800009" num="1"/>
            <showEq  id="10500030" num="10"/>
            <showEq  id="11900033" num="2"/>
            <showEq  id="11900027" num="2"/>
        </eq>
        <level>
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level1.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level2.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level3.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level4.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level5.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level6.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level7.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level8.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level9.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level10.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level11.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level12.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level13.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level14.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level15.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level16.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level17.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level18.xml" />
        </level>
	</fuBen>
	 <fuBen fuBenId="wuJinXingChen" showFrameLabel="wuJinXingChen" description="宇宙静默独荡其中，看星辰变换观前世今生，人世为何？天地为何？宇宙为何？哪里是开始？哪里是尽头？">
		<eq>
       		<showEq  id="10900011" num="1"/>
            <showEq  id="10500100" num="5"/>
            <showEq  id="10800010" num="1"/>
            <showEq  id="10500030" num="20"/>
            <showEq  id="11900042" num="2"/>
            <showEq  id="11900040" num="2"/>
        </eq>
        <level>
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level1.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level2.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level3.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level4.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level5.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level6.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level7.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level8.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level9.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level10.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level11.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level12.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level13.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level14.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level15.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level16.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level17.xml" />
	        <wave levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/EndlessLevel/Level1/Level18.xml" />
        </level>
	</fuBen>
	
</data>