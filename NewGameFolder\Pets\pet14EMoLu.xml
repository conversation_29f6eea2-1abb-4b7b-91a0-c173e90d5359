<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet14" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet14Idle" />
		<walk defId="pet14Walk" />
		<run defId="pet14Run" />
		<attack defId="pet14Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet14Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet14Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	   <skill id="Skill_pet14Skill" className="YJFY.Skill.PetSkills.Skill_Pet14Skill" x="-480"
			y="-280" z="-1" xRange="960" yRange="480" zRange="100" bodyDefId="pet14SkillBodyShow" bodyAttackReachFrameLabel="skillAttackReach" 
			bodySkillEndFrameLabel="skillEnd^stop^"  everyEntityAddShowDefId=""  everyEntityAddShowIsFrontOfBody="0"   hurtDuration="3000"
			 createRandomShowInterval="200" randomEntityShowId="pet14SkillYu" addEffectOnRandomEntitysId="pet14SkillYuDaoDaEffect" 
			addGroundEffectOneRandomEntityId="pet14SkillGroundEffect" effectAddtoTargetId="" randomEntityReachGroundFrameLabel="shanDianreachGround">
			<animationDefinition id="pet14SkillGroundEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetSkill14Effect_FlashGroundEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet14SkillYuDaoDaEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetSkill14Effect_FlashBeAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet14SkillYu" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetSkill14Effect_FlashAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<shakeView swfPath="NewGameFolder/PetSource/Pet14All.swf" className="PetSkill14ShakeView" />
			<flashView swfPath="NewGameFolder/PetSource/Pet14All.swf" className="PetSkill14FlashView" />
			
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet14Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetStand14_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet14Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetWalk14_4" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet14SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetSkill14AttackAnimation" x_offset="0" y_offset="0" />
			</animationDefinition>
		
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			
			<animationDefinition id="pet14SkillGroundEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetSkill14Effect_FlashGroundEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet14SkillYuDaoDaEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetSkill14Effect_FlashBeAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet14SkillYu" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet14All.swf"
					showClass="PetSkill14Effect_FlashAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			
		</shows>

	</animal>
</data>
