<?xml version="1.0" encoding="utf-8" ?>
<data id="Level5" swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/level.swf"
	className="LevelMap5" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound3.swf" className="SoundD" />
	<!--totalWaveNum 用于显示波次总数 -->
	<Waves totalWaveNum="8">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="0" duration="2500" num="8" isFallDown="0" />
		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="10000" duration="2500" num="10" isFallDown="0" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="20000" duration="2500" num="10" isFallDown="0" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="30000" duration="6000" num="16" isFallDown="0" />
		</Wave>				
		<Wave waveCount="5" totalEnemyNum="20" x="700" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="40000" duration="6000" num="10" isFallDown="1" />
		</Wave>				
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="50000" duration="2500" num="10" isFallDown="0" />
		</Wave>				
		<Wave waveCount="7" totalEnemyNum="20" x="600" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="60000" duration="6000" num="10" isFallDown="1" />
		</Wave>				
			
		<Wave waveCount="8" totalEnemyNum="10" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.XiaoBing" xmlPath="xiaoBing" startTime="70000" duration="2500" num="6" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="boss" startTime="72000" duration="1000" num="1"  />
		</Wave>
		
	</Waves>

    <EqDrop>
	   <xiaoBing noDropProWeight="1200">
		   <!--proWeight 概率权重-->
		   
		   <!-- 紫霞套装模具3 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_ZixiaClothes3_S" proWeight="20" />  
		    <!-- 紫霞武器模具3  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_ZixiaWeapon3_S" proWeight="20" /> 
		  
		   
		   <!-- 铁扇套装模具3 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_TieShanClothes3_S" proWeight="20" />  
		    <!-- 铁扇武器模具3  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_TieShanWeapon3_S" proWeight="20" /> 
		  
		    <!-- 后羿套装模具3 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_HouyiClothes3_S" proWeight="20" />  
		    <!-- 后羿武器模具3  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_HouyiWeapon3_S" proWeight="20" /> 
		   
		   
		  <!-- 无敌药水 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="1" />		 
   	      <!-- 龙蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Tyrannosaurs_S" proWeight="1" />
   	      <!-- 雷蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_XiaoPiKaQiu_S" proWeight="1" />   
    	   <!-- 灵狐武器3  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_FoxWeapon3_S" proWeight="20" />
   	      <!--  灵狐套装3   --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_FoxClothes3_S" proWeight="20" />	  	      	        
		  <!-- 刺牙棍 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_0_S" proWeight="20" />
   	      <!-- 蓝翎杖 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_3_S" proWeight="20" />
   	      <!-- 玄冰 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_XuanBingJian_S" proWeight="20" />
   	      <!-- 狙击手枪 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_JuJiShouQiang_S" proWeight="20" />
   	      <!-- 天使项链 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Necklace_TianShiXiangLian_S" proWeight="20" />
   	      <!-- 猴子铠甲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_6_S" proWeight="20" />
   	      <!-- 龙马铠甲 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_9_S" proWeight="20" />
   	      <!-- 狮鹫铠甲 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_ShiJiuKaiJiu_S" proWeight="20" />
   	      <!-- 士兵铠甲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_ShiBinKaiJia_S" proWeight="20" />
   	      <!-- 葫芦 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Gourd_JuMoHuLu_S" proWeight="20" />
   	      
   	      
	       <!-- 红药  -->  
   	      <item dropClassName="Item_HpUp" proWeight="100" />
   	      <!--  金币 -->  
   	      <item dropClassName="Item_MoneyUp" proWeight="300" />
   	      <!--  蓝药 -->  
   	      <item dropClassName="Item_MpUp" proWeight="100" /> 
 

    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Needlework_S" proWeight="10" />
    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.PiLiao_S" proWeight="10" /> 
     	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Iron_S" proWeight="10" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="8" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="1" />
   	          
	  </xiaoBing>
	  
	  
	   <boss noDropProWeight="0">
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="5" />
				   <numData num="2" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="4" proWeight="8" />
				   <numData num="5" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>	   
		   <!--proWeight 概率权重-->
		  <!-- 无敌药水 -->
		  
		    <!-- 紫霞套装2  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Clothes_ZixiaiClothes_2_S" proWeight="20" />
	   <!-- 紫霞武器21  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Weapon_ZixiaiWeapon_2_S" proWeight="20" />  
		    
		   <!-- 铁扇套装2  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Clothes_TieShanClothes2_S" proWeight="20" />
		 <!-- 铁扇武器2  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Weapon_TieShanWeapon2_S" proWeight="20" /> 
		  
		  		  		 <!-- 后羿套装2  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Clothes_HouyiClothes_2_S" proWeight="20" />
	   <!-- 后羿武器2  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Weapon_HouyiWeapon_2_S" proWeight="20" /> 
		  
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="1" />		 
   	      <!-- 龙蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Tyrannosaurs_S" proWeight="1" />
   	      <!-- 雷蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_XiaoPiKaQiu_S" proWeight="1" /> 
     	  <!-- 灵狐武器2  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Weapon_FoxWeapon2_S" proWeight="30" />
   	      <!--  灵狐套装2   --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Clothes_FoxClothes2_S" proWeight="30" />       	           	         	        	        
		  <!-- 试炼棍 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Weapon_ShiLianGun_S" proWeight="20" />
   	      <!-- 翡绿杖 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Weapon_FeiLv_S" proWeight="20" />
   	      <!-- 羽瞳剑 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Weapon_YuTongJian_S" proWeight="20" />
   	      <!-- 冲锋手枪 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Weapon_ChongFengShouQiang_S" proWeight="20" />
   	      <!-- 虎齿项链 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Necklace_HuChiXiangLian_S" proWeight="20" />
   	      <!-- 鸡装 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Clothes_Ji_S" proWeight="20" />
   	      <!-- 蟹装 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Clothes_Xie_S" proWeight="20" />
   	      <!-- 花蝶铠甲 --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Clothes_HuaDieKaiJia_S" proWeight="20" />
   	      <!-- 警察铠甲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Clothes_JingChaKaiJia_S" proWeight="20" />
   	      <!-- 青竹葫芦 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Gourd_QingZhuHuLu_S" proWeight="20" />
   	        	      
   	      <!-- 红药    -->
   	      <item dropClassName="Item_HpUp" proWeight="100" />
   	      <!-- 金币   -->
   	      <item dropClassName="Item_MoneyUp" proWeight="300" />
   	      <!-- 蓝药   -->
   	      <item dropClassName="Item_MpUp" proWeight="100" />
 
    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Needlework_S" proWeight="10" />
    	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.PiLiao_S" proWeight="10" /> 
     	  <!-- 材料 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Iron_S" proWeight="10" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="8" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="1" />
   	           
	   </boss>
	   
   </EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   <xiaoBing>
	<!--敌人数据 -->
	<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
		<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
	</fallDownEffect>
	<enemyData>
		<data att="totalHp" value="800" />
		<data att="attack" value="150" />
		<data att="expOfDieThisEnemy" value="200" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.01" />
		<data att="criticalRate" value="0.05" />
		<data att="criticalMuti" value="2" />
		
		<data att="deCriticalRate" value="0" />
		<data att="hitRate" value="0" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy5" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="60" bodyHeight="80" walkSpeed="16"
		runSpeed="100">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="40" yRange="60"
			zRange="100" />

		<idle defId="walk_enemy5" />
		<walk defId="walk_enemy5" />
		<run defId="walk_enemy5" />
		<attack defId="attack_enemy5" />
		<hurt defId="hurt_enemy5" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy5" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow1" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="walk_enemy5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Enemy.swf"
					showClass="Walk_Monster_5" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="hurt_enemy5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Enemy.swf"
					showClass="BeAttack_Monster_5" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Enemy.swf"
					showClass="Attack_Monster_5" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Enemy.swf"
					showClass="Dead_Monster_5" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
		</animationDefinitions>



	</animal>
</xiaoBing>
   <boss>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="5000" />
		<data att="attack" value="150" />
		<data att="expOfDieThisEnemy" value="2000" />
		<data att="defence" value="0" />
		<data att="dogdeRate" value="0.02" />
		<data att="criticalRate" value="0.1" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0" />
		<data att="hitRate" value="0" />
	</enemyData>
	<animal id="boss5" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="120" bodyHeight="160" walkSpeed="20"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="idle_boss5" />
		<walk defId="walk_boss5" />
		<run defId="run_boss5" />
		<attack defId="attack_boss5" />
		<hurt defId="hurt_boss5" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss5" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Boss.swf"
					showClass="Walk_Boss_5" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Boss.swf"
					showClass="Walk_Boss_5" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss5" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_1" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Boss.swf"
					showClass="BeAttack_Boss_5" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Boss.swf"
					showClass="Attack_Boss_5" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss5" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level5/Boss.swf"
					showClass="Dead_Boss_5" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</boss>
</data>
