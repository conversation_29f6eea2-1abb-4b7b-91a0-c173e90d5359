<?xml version="1.0" encoding="utf-8" ?>
<data>
	<defau>  <!--当没有相应装备的配置时，所要使用的默认配置-->
		<defau> 
			<hole1 successRate="1" needMoney="100000" />

			<hole2 successRate="1" needMoney="150000" />
	
			
			<hole3 successRate="1" needMoney="200000" />
			
			<hole4 successRate="1" needMoney="250000" />
			<hole5 successRate="1" needMoney="500000" />
			


	   </defau>
		<gem num="1" id1="">
			<hole1 />
		</gem>
	</defau>

	<!-- <equipment num="300" id1="10101006" id2="10101106" id3="10101206" id4="10101306" id5="10101406" id6="10101506" id7="10101606" id8="10101706" id9="10101806" id10="10101906"
	id11="10102006" id12="10102106" id13="10102206" id14="10102306" id15="10102406" id16="10102506" id17="10102606" id18="10102706" id19="10102806" id20="10102906"
	id21="10104006" id22="10104106" id23="10104206" id24="10104306" id25="10104406" id26="10104506" id27="10104606" id28="10104706" id29="10104806" id30="10104906"
	id31="10105006" id32="10105106" id33="10105206" id34="10105306" id35="10105406" id36="10105506" id37="10105606" id38="10105706" id39="10105806" id40="10105906"
	id41="10701006" id42="10701106" id43="10701206" id44="10701306" id45="10701406" id46="10701506" id47="10701606" id48="10701706" id49="10701806" id50="10701906"
	id51="10702006" id52="10702106" id53="10702206" id54="10702306" id55="10702406" id56="10702506" id57="10702606" id58="10702706" id59="10702806" id60="10702906"	
	id61="10704006" id62="10704106" id63="10704206" id64="10704306" id65="10704406" id66="10704506" id67="10704606" id68="10704706" id69="10704806" id70="10704906"
	id71="10705006" id72="10705106" id73="10705206" id74="10705306" id75="10705406" id76="10705506" id77="10705606" id78="10705706" id79="10705806" id80="10705906"
	id81="10200006" id82="10300106" id83="10200206" id84="10200306" id85="10200406" id86="10200506" id87="10200606" id88="10200706" id89="10200806" id90="10200906"
	id91="10300006" id92="10300106" id93="10300206" id94="10300306" id95="10300406" id96="10300506" id97="10300606" id98="10300706" id99="10300806" id100="10300906"
	
    id101="10101007" id102="10101107" id103="10101207" id104="10101307" id105="10101407" id106="10101507" id107="10101607" id108="10101707" id109="10101807" id110="10101907"
	id111="10102007" id112="10102107" id113="10102207" id114="10102307" id115="10102407" id116="10102507" id117="10102607" id118="10102707" id119="10102807" id120="10102907"
	id121="10104007" id122="10104107" id123="10104207" id124="10104307" id125="10104407" id126="10104507" id127="10104607" id128="10104707" id129="10104807" id130="10104907"
	id131="10105007" id132="10105107" id133="10105207" id134="10105307" id135="10105407" id136="10105507" id137="10105607" id138="10105707" id139="10105807" id140="10105907"
	id141="10701007" id142="10701107" id143="10701207" id144="10701307" id145="10701407" id146="10701507" id147="10701607" id148="10701707" id149="10701807" id150="10701907"
	id151="10702007" id152="10702107" id153="10702207" id154="10702307" id155="10702407" id156="10702507" id157="10702607" id158="10702707" id159="10702807" id160="10702907"	
	id161="10704007" id162="10704107" id163="10704207" id164="10704307" id165="10704407" id166="10704507" id167="10704607" id168="10704707" id169="10704807" id170="10704907"
	id171="10705007" id172="10705107" id173="10705207" id174="10705307" id175="10705407" id176="10705507" id177="10705607" id178="10705707" id179="10705807" id180="10705907"
	id181="10200007" id182="10300107" id183="10200207" id184="10200307" id185="10200407" id186="10200507" id187="10200607" id188="10200707" id189="10200807" id190="10200907"
	id191="10300007" id192="10300107" id193="10300207" id194="10300307" id195="10300407" id196="10300507" id197="10300607" id198="10300707" id199="10300807" id200="10300907"	
	
	 id201="10101008" id202="10101108" id203="10101208" id204="10101308" id205="10101408" id206="10101508" id207="10101608" id208="10101708" id209="10101808" id210="10101908"
	id211="10102008" id212="10102108" id213="10102208" id214="10102308" id215="10102408" id216="10102508" id217="10102608" id218="10102708" id219="10102808" id220="10102908"
	id221="10104008" id222="10104108" id223="10104208" id224="10104308" id225="10104408" id226="10104508" id227="10104608" id228="10104708" id229="10104808" id230="10104908"
	id231="10105008" id232="10105108" id233="10105208" id234="10105308" id235="10105408" id236="10105508" id237="10105608" id238="10105708" id239="10105808" id240="10105908"
	id241="10701008" id242="10701108" id243="10701208" id244="10701308" id245="10701408" id246="10701508" id247="10701608" id248="10701708" id249="10701808" id250="10701908"
	id251="10702008" id252="10702108" id253="10702208" id254="10702308" id255="10702408" id256="10702508" id257="10702608" id258="10702708" id259="10702808" id260="10702908"	
	id261="10704008" id262="10704108" id263="10704208" id264="10704308" id265="10704408" id266="10704508" id267="10704608" id268="10704708" id269="10704808" id270="10704908"
	id271="10705008" id272="10705108" id273="10705208" id274="10705308" id275="10705408" id276="10705508" id277="10705608" id278="10705708" id279="10705808" id280="10705908"
	id281="10200008" id282="10300108" id283="10200208" id284="10200308" id285="10200408" id286="10200508" id287="10200608" id288="10200708" id289="10200808" id290="10200908"
	id291="10300008" id292="10300108" id293="10300208" id294="10300308" id295="10300408" id296="10300508" id297="10300608" id298="10300708" id299="10300808" id300="10300908"	
	>
		<defau>   当没有相应的宝石时，所要使用的默认配置
			<hole1 successRate="1" needMoney="100000" />

			<hole2 successRate="1" needMoney="150000" />
	
			
			<hole3 successRate="1" needMoney="200000" />
			
			<hole4 successRate="1" needMoney="250000" />
	
		</defau>
		<gem num="1" id1="">
			<hole1 />
		</gem>
	</equipment> -->
</data>