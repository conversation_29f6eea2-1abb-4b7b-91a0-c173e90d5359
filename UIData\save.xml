<root>
  <Data uid="123456" name="test" nickName="%23456" idx="1">
    <Fox name="<PERSON>" playerId="playerOne" experiencePercent="1.0000" bloodPercent="1.0000" magicPercent="0.9696" level="65" money="67429480" sF="1">
      <Package>
        <item id="10105906" attack="522" renPin="8">
          <hole id="11900014" num="1"/>
          <hole id="11900014" num="1"/>
          <hole id="11900014" num="1"/>
        </item>
        <item id="10705906" defence="321" riot="0.3100" renPin="17">
          <hole id="11900019" num="1"/>
          <hole id="11900019" num="1"/>
          <hole id="11900019" num="1"/>
        </item>
        <item id="11900005" num="1"/>
        <item id="10200003" maxMagic="280" renPin="10"/>
        <item id="10500070" num="3"/>
        <item id="10400312" petLevel="20" experiencePercent="0.5779" essentialPercent="0.8800" talent="10103200">
          <passiveSkill id="10203301" promoteValue="0"/>
          <passiveSkill id="10203302" promoteValue="0"/>
          <passiveSkill id="10203307" promoteValue="0"/>
          <passiveSkill id="10203308" promoteValue="0"/>
          <activeSkill id="10103312" currentCDTime="0"/>
        </item>
        <item id="10800005"/>
        <item id="10400415" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10203200">
          <passiveSkill id="10203406" promoteValue="0"/>
          <passiveSkill id="10203405" promoteValue="0"/>
          <passiveSkill id="10203403" promoteValue="0"/>
          <activeSkill id="10103115" currentCDTime="0"/>
          <awakeSkill id="petAwakePassive_GaoJiTiZzhi_1"/>
        </item>
        <item id="11000009" isB="1" num="1"/>
        <item id="11000018" isB="1" num="1"/>
        <item id="11000018" isB="1" num="1"/>
        <item id="11000018" num="1"/>
        <item id="11000006" isB="1" num="15"/>
        <item id="11000004" num="6"/>
        <item id="11000016" isB="1" num="5"/>
        <item id="11000016" isB="1" num="10"/>
        <item id="11000016" isB="1" num="10"/>
        <item id="11000016" isB="1" num="10"/>
        <item id="11000016" isB="1" num="10"/>
        <item id="11000016" isB="1" num="10"/>
        <item id="11000016" isB="1" num="10"/>
        <item id="11000016" isB="1" num="10"/>
        <item id="11000016" isB="1" num="10"/>
        <item id="11000017" isB="1" num="2"/>
        <item id="11000017" isB="1" num="10"/>
        <item id="11000017" isB="1" num="10"/>
        <item id="11100003" num="2"/>
        <item id="10800008" isB="1"/>
        <item id="10800000"/>
        <item id="10800099" isB="1"/>
        <item id="10800099" isB="1"/>
        <item id="10800099" isB="1"/>
        <item id="10800099"/>
        <item id="11500003"/>
        <item id="11900011" num="1"/>
        <item id="11900011" num="1"/>
        <item id="11900011" num="1"/>
        <item id="11900011" num="1"/>
        <item id="11900016" num="1"/>
        <item id="11900000" num="1"/>
        <item id="11900000" num="1"/>
        <item id="11900000" num="1"/>
        <item id="11900000" num="1"/>
        <item id="11900000" num="1"/>
        <item id="11900000" num="1"/>
        <item id="11900000" num="1"/>
        <item id="11900000" num="1"/>
        <item id="11900015" num="1"/>
        <item id="11900015" num="1"/>
        <item id="11900015" num="1"/>
        <item id="11900015" num="1"/>
        <item id="11900015" num="1"/>
        <item id="10600024"/>
        <item id="10600024"/>
        <item id="10600024"/>
        <item id="10600024"/>
        <item id="10600024"/>
        <item id="10600022"/>
        <item id="10600022"/>
        <item id="10600038"/>
        <item id="10600035"/>
        <item id="10600035"/>
        <item id="10600001"/>
        <item id="10600007"/>
        <item id="10500000" isB="1" num="17"/>
        <item id="10500000" num="14"/>
        <item id="10500000" num="100"/>
        <item id="10500037" num="2"/>
        <item id="10500038" num="2"/>
        <item id="10500034" num="10"/>
        <item id="10500033" num="100"/>
        <item id="10500020" num="4"/>
        <item id="10500025" num="5"/>
        <item id="10500063" num="1"/>
        <item id="10500032" num="40"/>
        <item id="10500031" num="4"/>
        <item id="10500008" isB="1" num="37"/>
        <item id="10500008" num="49"/>
        <item id="10500008" num="100"/>
        <item id="10500008" isB="1" num="100"/>
        <item id="10500004" num="7"/>
        <item id="10500028" num="5"/>
        <item id="10500009" num="1"/>
        <item id="12000004"/>
        <item id="10500022" num="10"/>
        <item id="11900000" num="1"/>
        <item id="10600024"/>
        <item id="10600024"/>
        <item id="11900010" num="1"/>
        <item id="10600024"/>
      </Package>
      <Storage>
        <item id="10105006" attack="327" renPin="3"/>
        <item id="10705006" defence="192" riot="0.0300" renPin="16"/>
        <item id="10200806" maxMagic="850" renPin="14">
          <hole id="0"/>
          <hole id="0"/>
          <hole id="0"/>
        </item>
        <item id="10300906" criticalRate="42" renPin="5"/>
        <item id="10400410" petLevel="20" experiencePercent="1.0000" essentialPercent="0.4660" talent="10303300">
          <passiveSkill id="10203403" promoteValue="1338"/>
          <passiveSkill id="10203401" promoteValue="0"/>
          <passiveSkill id="10203408" promoteValue="25"/>
          <passiveSkill id="10203402" promoteValue="0"/>
          <passiveSkill id="10203407" promoteValue="0"/>
          <activeSkill id="10103110" currentCDTime="0"/>
          <awakeSkill id="petAwakePassive_GaoJiFuZhu_1"/>
        </item>
        <item id="11000009" isB="1" num="17"/>
        <item id="11000003" isB="1" num="22"/>
        <item id="11000011" isB="1" num="17"/>
        <item id="11000011" isB="1" num="100"/>
        <item id="11000011" num="2"/>
        <item id="11000010" isB="1" num="2"/>
        <item id="11000004" num="38"/>
        <item id="11000013" num="1"/>
        <item id="11000013" num="1"/>
        <item id="11000002" isB="1" num="92"/>
        <item id="11000005" isB="1" num="16"/>
        <item id="11900002" num="1"/>
        <item id="11900002" num="1"/>
        <item id="11900002" num="1"/>
        <item id="11900017" num="1"/>
        <item id="11900017" num="1"/>
        <item id="11900017" num="1"/>
        <item id="11900012" num="1"/>
        <item id="11900012" num="1"/>
        <item id="11900012" num="1"/>
        <item id="11900016" num="1"/>
        <item id="11200002" num="20"/>
        <item id="11200002" num="20"/>
        <item id="10500022" num="5"/>
        <item id="10500022" num="5"/>
        <item id="10500022" num="5"/>
        <item id="10500022" num="5"/>
        <item id="10500022" num="5"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500000" num="100"/>
        <item id="10500000" num="100"/>
        <item id="10500000" num="100"/>
        <item id="10500000" num="100"/>
        <item id="10500000" num="100"/>
        <item id="10500030" isB="1" num="3"/>
        <item id="10500030" isB="1" num="5"/>
        <item id="10500030" num="2"/>
        <item id="10500030" isB="1" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" isB="1" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500008" num="100"/>
        <item id="10500032" num="100"/>
        <item id="10500032" num="100"/>
        <item id="11000002" num="20"/>
        <item id="11000005" num="1"/>
        <item id="11000019" num="1"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
      </Storage>
      <InformationPanel>
        <item id="0"/>
        <item id="10706006" defence="208" riot="0.0400" renPin="16"/>
        <item id="10200906" maxMagic="892" renPin="23">
          <hole id="11900014" num="1"/>
          <hole id="11900014" num="1"/>
          <hole id="11900014" num="1"/>
        </item>
        <item id="0"/>
        <item id="10106006" attack="305" renPin="7"/>
        <item id="10300906" criticalRate="44" renPin="25">
          <hole id="11900014" num="1"/>
          <hole id="11900014" num="1"/>
          <hole id="11900014" num="1"/>
        </item>
      </InformationPanel>
      <PetPanel>
        <item id="10400414" petLevel="20" experiencePercent="1.0000" essentialPercent="0.1972" talent="10203300">
          <passiveSkill id="10203407" promoteValue="0"/>
          <passiveSkill id="10203405" promoteValue="215"/>
          <passiveSkill id="10203402" promoteValue="0"/>
          <passiveSkill id="10203401" promoteValue="250"/>
          <passiveSkill id="10203404" promoteValue="200"/>
          <activeSkill id="10103114" currentCDTime="0"/>
          <awakeSkill id="petAwakePassive_GaoJingLiLiang_1"/>
        </item>
      </PetPanel>
      <DMPanel>
        <attack l1="11300000_11300000" l2="11300002_11300002_11300002" l3="11300003_11300003_11300003_11300003" l4="11300004_11300004_11300004_11300004_11300004" l5="11300005_11300005_11300005_11300005_11300005_11300005"/>
        <defence l1="11300001_11300001" l2="11300008_11300008_11300008" l3="11300009_11300009_11300009_11300009" l4="11300010_11300010_11300010_11300010_11300010" l5="11300011_11300011_11300011_11300011_11300011_11300011"/>
      </DMPanel>
      <ShiTu xLUsedNum="0" xLDate="">
        <xiuLian id="attackXL_sF_5" xLValue="95"/>
        <xiuLian id="defenceXL_sF_5" xLValue="90"/>
        <xiuLian id="bloodXL_sF_5" xLValue="145"/>
        <xiuLian id="dodgeXL_sF_3" xLValue="150"/>
        <xiuLian id="hitXL_sF_2" xLValue="0"/>
      </ShiTu>
      <Hatch sT="2014-08-07 09:05:05" type="s"/>
      <Skill>
        <item id="10106100" currentCDTime="0"/>
        <item id="10106101" currentCDTime="0"/>
        <item id="10106102" currentCDTime="0"/>
        <item id="10106103" currentCDTime="0"/>
        <item id="10106104" currentCDTime="0"/>
      </Skill>
    </Fox>
    <ErLangShen name="ErLangShen" playerId="playerTwo" experiencePercent="1.0000" bloodPercent="1.0000" magicPercent="0.9439" level="65" money="66046312" sF="1">
      <Package>
        <item id="10400410" petLevel="20" experiencePercent="1.0000" essentialPercent="1.0000" talent="10303300">
          <passiveSkill id="10203403" promoteValue="8366"/>
          <passiveSkill id="10203409" promoteValue="28"/>
          <passiveSkill id="10203402" promoteValue="0"/>
          <passiveSkill id="10203408" promoteValue="23"/>
          <passiveSkill id="10203401" promoteValue="0"/>
          <activeSkill id="10103110" currentCDTime="0"/>
          <awakeSkill id="petAwakePassive_GaoJiFuZhu_1"/>
        </item>
        <item id="10500020" num="2"/>
        <item id="10500015" num="1"/>
        <item id="11900010" num="1"/>
        <item id="10600024"/>
        <item id="11900020" num="1"/>
        <item id="10400408" petLevel="20" experiencePercent="1.0000" essentialPercent="0.3859" talent="10203300">
          <passiveSkill id="10203402" promoteValue="0"/>
          <passiveSkill id="10203405" promoteValue="214"/>
          <passiveSkill id="10203403" promoteValue="5728"/>
          <passiveSkill id="10203404" promoteValue="243"/>
          <passiveSkill id="10203401" promoteValue="301"/>
          <activeSkill id="10103108" currentCDTime="0"/>
          <awakeSkill id="petAwakePassive_GaoJingLiLiang_1"/>
        </item>
        <item id="0"/>
        <item id="10400112" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10303100">
          <passiveSkill id="10203109" promoteValue="0"/>
          <passiveSkill id="10203104" promoteValue="0"/>
          <passiveSkill id="10203102" promoteValue="0"/>
          <passiveSkill id="10203107" promoteValue="0"/>
          <activeSkill id="10103112" currentCDTime="0"/>
        </item>
        <item id="10400112" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10303100">
          <passiveSkill id="10203103" promoteValue="0"/>
          <passiveSkill id="10203108" promoteValue="0"/>
          <passiveSkill id="10203102" promoteValue="0"/>
          <passiveSkill id="10203109" promoteValue="0"/>
          <passiveSkill id="10203105" promoteValue="0"/>
          <activeSkill id="10103112" currentCDTime="0"/>
        </item>
        <item id="10400100" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10103100">
          <passiveSkill id="10203101" promoteValue="0"/>
          <activeSkill id="10103100" currentCDTime="0"/>
        </item>
        <item id="11000009" isB="1" num="2"/>
        <item id="11000003" isB="1" num="46"/>
        <item id="11000005" isB="1" num="1"/>
        <item id="0"/>
        <item id="11000000" num="74"/>
        <item id="10500000" num="38"/>
        <item id="10500000" num="100"/>
        <item id="10500030" num="5"/>
        <item id="10500030" num="5"/>
        <item id="10500008" num="42"/>
        <item id="10500031" num="18"/>
        <item id="10500032" num="90"/>
        <item id="0"/>
        <item id="10500030" num="2"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
      </Package>
      <Storage>
        <item id="10400410" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10303100">
          <passiveSkill id="10203409" promoteValue="0"/>
          <passiveSkill id="10203406" promoteValue="0"/>
          <activeSkill id="10103110" currentCDTime="0"/>
          <awakeSkill id="petAwakePassive_FuZhu_1"/>
        </item>
        <item id="10400410" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10103100">
          <passiveSkill id="10203401" promoteValue="0"/>
          <passiveSkill id="10203402" promoteValue="0"/>
          <passiveSkill id="10203408" promoteValue="0"/>
          <activeSkill id="10103110" currentCDTime="0"/>
          <awakeSkill id="petAwakePassive_FuZhu_1"/>
        </item>
        <item id="10400410" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10203100">
          <passiveSkill id="10203405" promoteValue="0"/>
          <passiveSkill id="10203408" promoteValue="0"/>
          <activeSkill id="10103110" currentCDTime="0"/>
          <awakeSkill id="petAwakePassive_FuZhu_1"/>
        </item>
        <item id="10400300" petLevel="6" experiencePercent="0.0768" essentialPercent="1.0000" talent="10103100">
          <passiveSkill id="10203302" promoteValue="0"/>
          <passiveSkill id="10203309" promoteValue="0"/>
          <activeSkill id="10103300" currentCDTime="0"/>
        </item>
        <item id="10400306" petLevel="20" experiencePercent="1.0000" essentialPercent="1.0000" talent="10103300">
          <passiveSkill id="10203309" promoteValue="0"/>
          <passiveSkill id="10203305" promoteValue="0"/>
          <passiveSkill id="10203303" promoteValue="0"/>
          <passiveSkill id="10203301" promoteValue="0"/>
          <passiveSkill id="10203308" promoteValue="0"/>
          <activeSkill id="10103306" currentCDTime="0"/>
        </item>
        <item id="10400306" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10103100">
          <passiveSkill id="10203302" promoteValue="0"/>
          <passiveSkill id="10203303" promoteValue="0"/>
          <passiveSkill id="10203309" promoteValue="0"/>
          <activeSkill id="10103306" currentCDTime="0"/>
        </item>
        <item id="10400206" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10203100">
          <passiveSkill id="10203206" promoteValue="0"/>
          <passiveSkill id="10203202" promoteValue="0"/>
          <passiveSkill id="10203203" promoteValue="0"/>
          <activeSkill id="10103206" currentCDTime="0"/>
        </item>
        <item id="10400106" petLevel="1" experiencePercent="0.0000" essentialPercent="1.0000" talent="10203100">
          <passiveSkill id="10203101" promoteValue="0"/>
          <passiveSkill id="10203102" promoteValue="0"/>
          <passiveSkill id="10203108" promoteValue="0"/>
          <activeSkill id="10103106" currentCDTime="0"/>
        </item>
        <item id="11000019" num="5"/>
        <item id="11000010" isB="1" num="1"/>
        <item id="11000011" isB="1" num="27"/>
        <item id="11800005"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="10500023" num="1"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
      </Storage>
      <InformationPanel>
        <item id="0"/>
        <item id="10704906" defence="329" riot="0.3300" renPin="1">
          <hole id="11900018" num="1"/>
          <hole id="11900018" num="1"/>
          <hole id="11900018" num="1"/>
        </item>
        <item id="10200806" maxMagic="852" renPin="0">
          <hole id="11900019" num="1"/>
          <hole id="11900019" num="1"/>
          <hole id="11900019" num="1"/>
        </item>
        <item id="0"/>
        <item id="10104906" attack="500" renPin="22">
          <hole id="11900018" num="1"/>
          <hole id="11900018" num="1"/>
          <hole id="11900013" num="1"/>
        </item>
        <item id="10300906" criticalRate="44" renPin="17">
          <hole id="11900019" num="1"/>
          <hole id="11900019" num="1"/>
          <hole id="11900019" num="1"/>
        </item>
      </InformationPanel>
      <PetPanel/>
      <DMPanel>
        <attack l1="11300000_11300000" l2="11300002_11300002_11300002" l3="11300003_11300003_11300003_11300003" l4="11300004_11300004_11300004_11300004_11300004" l5="11300005_11300005_11300005_11300005_11300005_11300005"/>
        <defence l1="11300001_11300001" l2="11300008_11300008_11300008" l3="11300009_11300009_11300009_11300009" l4="11300010_11300010_11300010_11300010_11300010" l5="11300011_11300011_11300011_11300011_11300011_11300011"/>
      </DMPanel>
      <ShiTu xLUsedNum="0" xLDate="">
        <xiuLian id="attackXL_sF_5" xLValue="25"/>
        <xiuLian id="defenceXL_sF_2" xLValue="50"/>
        <xiuLian id="bloodXL_sF_5" xLValue="35"/>
        <xiuLian id="dodgeXL_sF_0" xLValue="0"/>
        <xiuLian id="hitXL_sF_0" xLValue="0"/>
      </ShiTu>
      <Hatch/>
      <Skill>
        <item id="10104300" currentCDTime="0"/>
        <item id="10104301" currentCDTime="0"/>
        <item id="10104302" currentCDTime="0"/>
        <item id="10104303" currentCDTime="0"/>
        <item id="10104304" currentCDTime="0"/>
      </Skill>
    </ErLangShen>
    <PublicStorage>
      <item id="10105905" attack="275" renPin="3">
        <hole id="0"/>
      </item>
      <item id="10104906" attack="501" renPin="19"/>
      <item id="10704906" defence="325" riot="0.3100" renPin="9"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
    </PublicStorage>
    <TaskGoals/>
    <Task reCount="0" reDate="2013-11-19">
      <item id="11003" state="2"/>
      <item id="11004" state="2"/>
      <item id="11002" state="2"/>
      <item id="11005" state="2"/>
    </Task>
    <Farm sunValue="38662">
      <land id="100100" state="0"/>
      <land id="100100" state="0"/>
      <land id="100100" state="0"/>
      <other id="1000" hv="-11_2"/>
      <other id="1001" hv="6_-1"/>
      <other id="1002" hv="4_2"/>
    </Farm>
    <LDF id="100100" state="0"/>
    <Buff/>
    <Protect gD="2013-11-30 16:48:58"/>
    <VIP oldDateGetGift="2014-01-08"/>
    <EGD gGN="buChangLiBao2_christmas_buChangLiBao20140409"/>
    <Mirage mirageDate="2014-03-19" mirageNum="1"/>
    <PK allMatch="1497" winMatch="1459" failMatch="39" pkPoint="3628" allMatch2="1" winMatch2="1" failMatch2="0" matchD2="2014-09-04 09:17:43" isReseted="1"/>
    <Sign num="1" signD="2014-01-08 02:06:17" gAD="2014-01-06"/>
    <RG rGNum="1" rGDate="2014-08-07" buyNum="0"/>
    <ExE haveNum="0" exeNum="20"/>
    <NNameData rId="179"/>
    <OnLineGiftBag remainTime="45" id="gB1" dayTime="2014-09-04 09:17:43"/>
    <MainLineTask phase="phase_4">
      <task id="task27" isGotReward="1"/>
      <task id="task28">
        <taskGoal id="taskGoal28" num="12"/>
      </task>
      <task id="task29" isGotReward="1"/>
      <task id="task30">
        <taskGoal id="taskGoal30" num="2"/>
      </task>
      <task id="task31" isGotReward="1"/>
      <task id="task32">
        <taskGoal id="taskGoal32" num="28"/>
      </task>
      <task id="task55" isGotReward="1"/>
      <task id="task56" isGotReward="1"/>
      <task id="task57" isGotReward="1"/>
      <task id="task58" isGotReward="1"/>
      <task id="task59" isGotReward="1"/>
      <task id="task60" isGotReward="1"/>
      <task id="task67" isGotReward="1"/>
      <task id="task68" isGotReward="1"/>
      <task id="task69" isGotReward="1"/>
      <task id="task70" isGotReward="1"/>
    </MainLineTask>
    <WorldBoss p="120" pT="2014-01-08 02:34:00" bossId="boss1" dataTime="2014-03-19 14:43:06"/>
    <Society>
      <sign sT="" sN="0" gT=""/>
      <contri eN="0" mN="0" tN="0" time=""/>
      <shop/>
    </Society>
    <Boss t="2014-09-04 09:17:43">
      <boss id="1" s="a"/>
      <boss id="2" s="a"/>
      <boss id="3" s="a"/>
      <boss id="4" s="a"/>
      <boss id="5" s="a"/>
      <boss id="6" s="a"/>
    </Boss>
    <PetBoss t="2014-09-04 09:17:43"/>
    <PK2>
      <onePK d="2014-04-09 09:21:45">
        <player pkI="0" uid="test123" sI="1" pkS="2" p1T="ChangE"/>
        <player pkI="1" uid="test123" sI="1" pkS="2" p1T="ChangE"/>
      </onePK>
      <twoPK d="2014-04-09 09:09:09">
        <player pkI="0" uid="test123" sI="1" pkS="2" p1T="ChangE"/>
        <player pkI="1" uid="test123" sI="1" pkS="2" p1T="ChangE"/>
        <player pkI="2" uid="test123" sI="1" pkS="2" p1T="ChangE"/>
      </twoPK>
    </PK2>
    <GYP t="2014-09-04 09:17:43" mapLevel="26"/>
    <XM t="2014-08-11 09:54:38">
      <one id="haiDiXunBao" n="0"/>
      <one id="daZhanLongWan" n="0"/>
      <one id="jingLongZhenShen" n="3"/>
    </XM>
    <AutoPets s="377">
      <autoPet id="smallShrimp" level="1" hp="0" mp="10" exp="0" playerId="playerOne" pJId="d">
        <activeSkill id="smallShrimpAttack" level="1" cd="0"/>
      </autoPet>
      <autoPet id="tortoise" level="1" hp="2000" mp="10" exp="0" pJId="d">
        <activeSkill id="tortoiseAttack" level="1" cd="0"/>
      </autoPet>
      <autoPet id="dragonKingPet" level="1" hp="4000" mp="20" exp="0" pJId="d">
        <activeSkill id="dragonKingAttack" level="1" cd="0"/>
        <activeSkill id="dragonKingSkill1" level="1" cd="0"/>
        <activeSkill id="dragonKingSkill2" level="1" cd="0"/>
      </autoPet>
      <autoPet id="jiaoRen" level="1" hp="0" mp="20" exp="0" playerId="playerTwo" pJId="d">
        <activeSkill id="jiaoRenAttack" level="1" cd="0"/>
        <activeSkill id="jiaoRenSkill1" level="1" cd="0"/>
      </autoPet>
    </AutoPets>
    <CollectTime/>
    <SmallAssistant>
      <ActiveTask t="2014-09-04 09:17:43">
        <task id="active1"/>
        <task id="active7">
          <taskGoal id="activeTaskGoal5" num="1"/>
        </task>
        <task id="active10"/>
        <task id="active2"/>
        <task id="active3"/>
        <task id="active4"/>
        <task id="active5"/>
        <task id="active9"/>
        <task id="active6"/>
        <task id="active8"/>
      </ActiveTask>
      <LevelTask/>
    </SmallAssistant>
    <PK21 rT="2014-08-11 10:01:09" allRN="27" failRN="27" pkNT="2014-08-11 10:01:09" pkN="12">
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
    </PK21>
    <svA isBuyX="1"/>
    <Ada t="2014-09-04 09:17:43"/>
    <dL/>
    <Mounts sPN="11">
      <mount id="mount1" l1="1" l2="0" pN="10" sN="0" pId="playerOne"/>
      <mount id="mount2" l1="1" l2="0" pN="9" sN="0" pId="playerTwo"/>
    </Mounts>
    <Other unAbleBuyEqs="11100001_11100004">
      <cheatData/>
    </Other>
  </Data>
</root>