<?xml version="1.0" encoding="UTF-8"?>
<Task>
	<TaskGoal>
		<!-- 任务目标 -->
		<item id="10001" name="皮搋子僵尸" str="enemy4" />
		<item id="10002" name="白骨精" str="boss5" />
		<item id="10003" name="金角大王" str="boss7" />
		<item id="10004" name="银角大王" str="boss6" />
		<item id="10005" name="铁铲僵尸" str="enemy8" />
		<item id="10006" name="长剑僵尸" str="enemy9" />
		<item id="10007" name="虎力大仙" str="boss12" />
		<item id="10008" name="鹿力大仙" str="boss11" />
		<item id="10009" name="羊力大仙" str="boss10" />
		<item id="10010" name="牛魔王" str="boss13" />
		<item id="10011" name="双叉岭僵尸" str="enemy1" />
		<item id="10012" name="六耳猕猴" str="boss16" />
		<item id="10013" name="寅将军" str="boss1" />
		<item id="10014" name="山贼头子" str="boss18" />
		<item id="10015" name="黑熊精" str="boss2" />
		<item id="10016" name="魔鱼精" str="boss3" />
		<item id="10017" name="红孩儿" str="boss4" />
		<item id="10018" name="狮怪" str="boss8" />
		<item id="10019" name="蜘蛛精" str="boss9" />
		<item id="10020" name="猿长老" str="boss14" />
		<item id="10021" name="暴怒猿将" str="boss15" />
		<item id="10022" name="偷瓜贼" str="enemy18" />
		<item id="10023" name="签到" str="gotoQunZu" />
		<item id="10024" name="僵尸恶鬼" str="enemy19" />
		<item id="10025" name="千年僵尸王" str="boss19" />
		<item id="10026" name="幻影僵尸" str="enemy21" />
		<item id="10027" name="幻影魔王" str="boss21" />
		<item id="10028" name="食人巨魔" str="boss20" />
		<item id="10029" name="大目鱼妖头领" str="boss17" />
		<item id="10030" name="野鬼" str="enemy22" />
		<item id="10031" name="嗜血妖王" str="boss27" />
		<item id="10032" name="晶石巨人" str="boss28" />
		<item id="10033" name="彻骨冰魔" str="boss29" />
		<item id="10034" name="丛林之王" str="boss30" />
		<item id="10035" name="远古巨章" str="boss31" />
		<item id="10036" name="毒龙守卫" str="boss32" />
		<item id="10037" name="广目天王（普通级）" str="bossWorld12" />
		<item id="10038" name="神圣守卫" str="boss34" />
		<item id="10039" name="召唤哪吒妖将" str="autoPet_Nezha3" />
		<item id="10040" name="无尽闯关（层）" str="endlessGetAward" />
         <item id="10041" name="无尽闯关（级）" str="endless" />
		<item id="10042" name="单人转双人" str="oneToTwo" /> 
		<item id="10043" name="单人PK前50" str="PKmakeup1" /> 
		<item id="10044" name="单人PK51-500" str="PKmakeup2" /> 
		<item id="10045" name="单人PK501-10000" str="PKmakeup3" /> 
		<item id="10046" name="蟾蜍之王" str="chanchuking" />
		
		 

	</TaskGoal>
	<TaskReward>

		
	</TaskReward>
	<Task>
		<!-- 任务 -->
		<!-- id： 任务id ， name：任务名称， type：任务类型， resetType：任务重置类型， description：任务描述 -->
		<!-- 任务类型（type）有4种：everyDayTask:每日任务， limitingTimeTask:节日任务， accumulatedTask：累积（车轮）任务 
			limitingTimeAccumulatedTask 有时间限制的车轮任务 -->
		<!-- 任务重置类型（resetType）有三种： everyDayReset：每日重置， once:任务只能进行一次， DieRevive：完成后重置 -->
		<!-- 节日任务额外增加的属性：formerData(开始时间） latterData（结束时间） -->
		<!-- 累积任务额外增加的属性：taskCount（任务要完成的总次数） -->

		<!-- 僵尸星球大战 -->

		
			<!--钥匙任务 -->
		
			
		
			<item id="13034" name="[单次]消失的神印补偿礼包" type="limitingTimeAccumulatedTask" resetType="once"   taskCount="1"  progressBar="UI.TaskPanel.Bar.NewYearTaskBar"  isNew="1" isGoto="1"
            description="这些礼物送给那些杂货店里购买神印套装而后又不翼而飞的玩家，更送给所有一直支持西游的玩家，送上一些金币和宝石作为小小补偿，希望你们对我们的爱一如既往！" formerData="2015-05-20 00:00:00"  latterData="2016-08-18 00:00:00"  >
           
            <taskGoal ids="10013" nums="2" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_1" />
			</activitytask>
                  
            <taskReward type="equipmentReward"  description="金袋X3_八级生命宝石X4" ids="11100000_11900027" nums="3_4" binding="true" />

            <taskReward type="experienceReward" description="经验"  value="521314" />
            <taskReward type="moneyReward"  description="金币" value="521"  />
          </item>	 
		  	
			
			
			
		<item id="13033" name="[单次]灵阁符地新玩法" type="limitingTimeAccumulatedTask" resetType="once"   taskCount="1"  progressBar="UI.TaskPanel.Bar.NewYearTaskBar"  isNew="1" isGoto="1"
            description="灵阁符地新副本开启，快快去体验一下吧，注意不要被怪物的灯光照到，不要携带妖将和开启全屏技能，不然师傅可要被煮熟了，快快去用智慧击败蟾蜍之王吧！" formerData="2015-05-20 00:00:00"  latterData="2017-10-02 00:00:00"  >
           
            <gototask type="1"> </gototask>
            <taskGoal ids="10046" nums="2" />
			<activitytask type="2" >
			</activitytask>
                  
            <taskReward type="equipmentReward"  description="天使灵符（模具）X1_天使灵符碎片X3" ids="12100101_10500123" nums="1_3" binding="true" />

            <taskReward type="experienceReward" description="经验"  value="66666" />
            <taskReward type="moneyReward"  description="金币" value="66666"  />
          </item>	 
		  
		
		  
		  
		  
		
		  <item id="10025" name="勇闯蟾蜍地穴" type="everyDayTask" resetType="everyDayReset"
				  isNew="1" description="蟾蜍地穴的蟾蜍之王竟然胆敢抓走师傅，快快去教训他救回师傅！" isGoto="1">
				  <taskGoal ids="10046" nums="1" />
	  				<everygototask type="1"></everygototask>
				  <taskReward type="equipmentReward" description="天使灵符碎片X1"
					  ids="10500123" nums="1" />
	  
				  <taskReward type="experienceReward" description="经验"
					  value="80000" />
				  <taskReward type="moneyReward" description="金币" value="80000" />
			  </item>
			
			
			
			
			
				<item id="13032" name="[单次]全民回馈任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1"
			isNew="1" isGoto="1"
			description="由于游戏内容的不断增加，素材与存档持续增大，很多玩家出现了白屏卡顿现象，我们正在积极修复这些问题，感谢玩家的积极反馈，同样感谢玩家对我们的支持，我们也将逐步优化游戏让玩家有更好的游戏体验(该任务于12月21日凌晨0点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-12-21 00:00:00">

			<taskGoal ids="10031" nums="1" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_18" />
			</activitytask>

			<taskReward type="equipmentReward" description="多闻契约碎片X5_哪吒契约碎片X5_妖将内丹X10"
				ids="10500100_10500107_10500065" nums="5_5_10" binding="true" />

			<taskReward type="experienceReward" description="经验"
				value="8" />
			<taskReward type="moneyReward" description="金币" value="8" />
		</item>	
		
			
			
			
		<item id="13029" name="[单次]单人PK补偿任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" isGoto="1"
			isNew="1"
			description="由于服务器动荡单人PK10月月榜数据丢失导致大部分玩家无法领取相应勋章奖励，为补偿玩家损失在本月20日前进入单人排行榜月榜前50名的玩家皆可获得以下奖励(该任务于11月21日凌晨0点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-11-21 00:00:00">
			<activitytask type="3" >
			</activitytask>
			<taskGoal ids="10043" nums="1" />


			<taskReward type="equipmentReward" description="帝王勋章X1_哪吒契约模具X1_变形飞机时装X1"
				ids="11600000_10600098_10900000" nums="1_1_1" binding="true" />

			<taskReward type="experienceReward" description="经验"
				value="8" />
			<taskReward type="moneyReward" description="金币" value="8" />
		</item>	
		
		
			<item id="13030" name="[单次]单人PK补偿任务2" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" isGoto="1"
			isNew="1"
			description="由于服务器动荡单人PK10月月榜数据丢失导致大部分玩家无法领取相应勋章奖励，为补偿玩家损失在本月20日前进入单人排行榜月榜51-500名的玩家皆可获得以下奖励(该任务于11月21日凌晨0点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-11-21 00:00:00">
			<activitytask type="3" >
			</activitytask>
			<taskGoal ids="10044" nums="1" />


			<taskReward type="equipmentReward" description="大元帅勋章X1_雪人时装X1"
				ids="11600002_10900007" nums="1_1_1" binding="true" />

			<taskReward type="experienceReward" description="经验"
				value="8" />
			<taskReward type="moneyReward" description="金币" value="8" />
		</item>	
		
			
			
			<item id="13031" name="[单次]单人PK补偿任务3" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" isGoto="1"
			isNew="1"
			description="由于服务器动荡单人PK10月月榜数据丢失导致大部分玩家无法领取相应勋章奖励，为补偿玩家损失在本月20日前进入单人排行榜月榜501-10000名的玩家皆可获得以下奖励(该任务于11月21日凌晨0点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-11-21 00:00:00">

			<taskGoal ids="10045" nums="1" />

			<activitytask type="3" >
			</activitytask>
			<taskReward type="equipmentReward" description="副将勋章X1_忍者时装X1"
				ids="11600004_10900002" nums="1_1_1" binding="true" />

			<taskReward type="experienceReward" description="经验"
				value="8" />
			<taskReward type="moneyReward" description="金币" value="8" />
		</item>	
			
			
			
			
			
		<item id="13026" name="[单次]好事成双" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" isGoto="1"
			isNew="1"
			description="单人转双人系统开启，为尽快提升新增存档战力，使用单人转双人存档的玩家将得到以下奖励(该任务于9月30日凌晨0点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-09-30 24:00:00">

			<taskGoal ids="10042" nums="1" />
			<activitytask type="4" >
			</activitytask>

			<taskReward type="equipmentReward" description="圣域项链X1_大经验丹X20"
				ids="10300011_11000004" nums="1_20" binding="true" />

			<taskReward type="experienceReward" description="经验"
				value="8" />
			<taskReward type="moneyReward" description="金币" value="8" />
		</item>
		
		
			
			
		<item id="13025" name="[单次]无尽之境补偿任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" isGoto="1"
			isNew="1"
			description="本周因为系统设置问题导致无尽之境单双人排行榜数据丢失，为补偿玩家损失接受此任务后玩家通关无尽之境任意一级都将获得以下奖励(该任务于9月13日凌晨0点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-09-13 00:00:00">

			<taskGoal ids="10041" nums="1" />
			<activitytask type="5" >
			</activitytask>

			<taskReward type="equipmentReward" description="勇者勋章X1_四级攻击宝石X2_四级生命宝石X2"
				ids="11600029_11900013_11900003" nums="1_2_2" binding="true" />

			<taskReward type="experienceReward" description="经验"
				value="8" />
			<taskReward type="moneyReward" description="金币" value="8" />
		</item>
		
			
		

			
			
			
	<item id="13024" name="[单次]全民战神补偿任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1"
			isNew="1" isGoto="1"
			description="本周因为系统设置问题导致无尽之境单双人排行榜数据丢失，为补偿玩家损失接受此任务后玩家通关无尽之境任意一层都将获得以下奖励(该任务于9月13日凌晨0点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-09-13 00:00:00">

			<taskGoal ids="10040" nums="1" />

			<activitytask type="5" >
			</activitytask>
			<taskReward type="equipmentReward" description="战神勋章X1_七级攻击宝石X2_七级生命宝石X2"
				ids="11600023_11900032_11900026" nums="1_2_2" binding="true" />

			<taskReward type="experienceReward" description="经验"
				value="8" />
			<taskReward type="moneyReward" description="金币" value="8" />
		</item>
		
		
		
		
			
			
			
				
				<item id="13028" name="[单次]哪吒皮肤免费领" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" isGoto="1"
			isNew="1"
			description="哪吒天王妖将横空出世，在11月10日之前做出哪吒妖将的小伙伴将免费获得哪吒妖将美队皮肤，美队皮肤不光样子萌萌哒还能增加妖将百分之十的基础属性哦！(该节日任务于11月10日凌晨24点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-11-11 00:00:00">

			<taskGoal ids="10039" nums="1" />
			<activitytask type="7" >
			</activitytask>

			<taskReward type="equipmentReward" description="美队变身卡"
				ids="11000028" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="8" />
			<taskReward type="moneyReward" description="金币" value="8" />
		</item>
	

			
			
			
			<item id="10024" name="先人一步" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="神域套装即将解开封印，强化材料神域冥火已提前出现在神圣之域，击败神圣之域的守卫BOSS，提前拥有神域冥火吧！">
			
			<taskGoal ids="10038" nums="2" />
			<everygototask type="2">
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap4.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap4.xml" />
				<classname value="RouteMap4" />
				<gotoname value="levelMaptBtn_1" />
			</everygototask>
			<taskReward type="equipmentReward" description="神域冥火X3_幸运宝石X4"
				ids="10500091_10500000" nums="3_4" />

			<taskReward type="experienceReward" description="经验"
				value="1800000" />
			<taskReward type="moneyReward" description="金币" value="800000" />
		</item>

			
			
		<item id="12003" name="端午节任务" type="accumulatedTask" resetType="once"
			taskCount="6" progressBar="UI.TaskPanel.Bar.HuanYingXingQiuBar" isNew="1"
			description="端午节到了，外星人却不想让我们安心的过节，幻影星球的幻影大王带着他的小伙伴前来捣乱了，每天教训他三次让他知道到我们地球人的厉害！(一旦接取任务后开始计时，6天内都完成任务即可获得任务奖励，中断则任务失败。该活动6月18日开启。)"
			formerData="2015-06-18 00:00:00" latterData="2015-06-29 00:00:00">
			<taskGoal ids="10027" nums="3" />

			<taskReward type="equipmentReward" description="哆啦A梦时装（30天）*1_幽冥宝珠（昊白）*2" ids="10900011_10500082" nums="1_2" binding="true" />
			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="300000" />
		</item>
			
		<item id="12004" name="端午节任务" type="accumulatedTask" resetType="once" isGoto="1"
			taskCount="5" progressBar="UI.TaskPanel.Bar.HuanYingXingQiuBar" isNew="1"
			description="端午节到了，外星人却不想让我们安心的过节，幻影星球的幻影大王带着他的小伙伴前来捣乱了，每天教训他三次让他知道到我们地球人的厉害！(一旦接取任务后开始计时，5天内都完成任务即可获得任务奖励，中断则任务失败。该活动6月7日开启。)"
			formerData="2016-06-06 00:00:00" latterData="2016-06-20 00:00:00">
			<taskGoal ids="10027" nums="3" />

			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_17" />
			</activitytask>

			<taskReward type="equipmentReward" description="兔宝宝时装（30天）*1_附魔大礼包*1" ids="10900013_11100008"
				nums="1_1" binding="true" />
			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="300000" />
		</item>
		
		
		<item id="13022" name="[单次]飞跃童年" isGoto="1" type="limitingTimeAccumulatedTask" resetType="once"   taskCount="1"  progressBar="UI.TaskPanel.Bar.NewYearTaskBar"  isNew="1"
            description="儿童节到了，让我们再一次欺负最弱弱的僵尸吧！(该节日任务于6月1日24点消失)" formerData="2015-05-20 00:00:00"  latterData="2015-06-02 00:00:00"  >
           
            <taskGoal ids="10013" nums="5" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_1" />
			</activitytask>
                  
            <taskReward type="equipmentReward"  description="哆啦A梦时装（七天）X1" ids="10900012" nums="1" binding="true" />

            <taskReward type="experienceReward" description="经验"  value="61000" />
            <taskReward type="moneyReward"  description="金币" value="61000"  />
          </item>	 
		
		
		
		
		<item id="13021" name="[单次]捕获天王" isGoto="1" type="limitingTimeAccumulatedTask" resetType="once"   taskCount="1"  progressBar="UI.TaskPanel.Bar.NewYearTaskBar"  isNew="1"
            description="儿童节到了，天宫送来援兵协助我们打僵尸，不过要考验我们的能力，快去天宫副本击败普通级的广目天王证明自己的实力吧！(该节日任务于6月1日24点消失)" formerData="2015-05-20 00:00:00"  latterData="2015-06-02 00:00:00"  >
           
            <taskGoal ids="10037" nums="1" />
			<activitytask type="6" >
			</activitytask>
                  
            <taskReward type="equipmentReward"  description="广目天王契约（模具）X1_广目天王契约碎片X5" ids="10600096_10500089" nums="1_5" binding="true" />

            <taskReward type="experienceReward" description="经验"  value="8888888" />
            <taskReward type="moneyReward"  description="金币" value="888888"  />
          </item>	 
		
		  
		
		<!--元宵任务 -->
		<item id="13020" name="[单次]鸿运当头" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1" isGoto="1"
			description="元宵节过了，但小伙伴们却什么都没得到，原来是牛魔王抢走了我们的礼物，去火焰山教训他1次，拿回属于我们的礼物吧！(该节日任务于3月9日凌晨0点消失)"
			formerData="2015-03-05 00:00:00" latterData="2015-03-09 00:00:00">

			<taskGoal ids="10010" nums="1" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_13" />
			</activitytask>

			<taskReward type="equipmentReward" description="新年红包X5_新年金红包X5"
				ids="11100007_11100006" nums="5_5" />

			<taskReward type="experienceReward" description="经验"
				value="8888888" />
			<taskReward type="moneyReward" description="金币" value="888888" />
		</item>


		<item id="10023" name="险中求胜" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="毒龙守卫拥有超强的摧毁能力，躲过他的攻击给他致命一击吧！">
		
			<taskGoal ids="10036" nums="2" />
			<everygototask type="3">
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_23" />
			</everygototask>
			<taskReward type="equipmentReward" description="幽冥宝珠（帝黄）X1_幸运宝石X4"
				ids="10500080_10500000" nums="1_4" />

			<taskReward type="experienceReward" description="经验"
				value="1800000" />
			<taskReward type="moneyReward" description="金币" value="800000" />
		</item>




		<item id="10022" name="斗智斗勇" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="原始秘境的章鱼死士们擅长自杀式攻击，运用自己的智慧躲过他们的致命一击吧！">
			
			<taskGoal ids="10035" nums="1" />
			<everygototask type="4">
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_22" />
			</everygototask>
			<taskReward type="equipmentReward" description="4级攻击宝石X1_幸运宝石X4"
				ids="11900013_10500000" nums="1_4" />

			<taskReward type="experienceReward" description="经验"
				value="1800000" />
			<taskReward type="moneyReward" description="金币" value="800000" />
		</item>



		<item id="10021" name="争分夺秒" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="厄运丛林里面的怪物们在自己的地盘拥有飞快的移动速度，在他们的首领接近你的时候消灭他们吧！">
			<taskGoal ids="10034" nums="1" />
			<everygototask type="5">
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_21" />
			</everygototask>
			<taskReward type="equipmentReward" description="4级人品宝石X1_幸运宝石X4"
				ids="11900023_10500000" nums="1_4" />

			<taskReward type="experienceReward" description="经验"
				value="1800000" />
			<taskReward type="moneyReward" description="金币" value="800000" />
		</item>



		<item id="10020" name="极寒挑战" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="冰魔领地里的彻骨冰魔利用邪恶的冰系魔法将星球的居民变为残暴的雪魔，击败他解放这个星球吧！">
			<taskGoal ids="10033" nums="1" />
			<everygototask type="6">
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_20" />
			</everygototask>
			<taskReward type="equipmentReward" description="4级魔法宝石X1_幸运宝石X4"
				ids="11900008_10500000" nums="1_4" />

			<taskReward type="experienceReward" description="经验"
				value="1800000" />
			<taskReward type="moneyReward" description="金币" value="800000" />
		</item>


		<item id="10019" name="速战速决" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="水晶壁垒星球上的晶石巨人，会用魔法在战斗中强壮自己的士兵，快速是唯一获胜手段，快快去击败他吧！">
			<taskGoal ids="10032" nums="1" />
			<everygototask type="7">
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_19" />
			</everygototask>
			<taskReward type="equipmentReward" description="4级防御宝石X1_幸运宝石X4"
				ids="11900018_10500000" nums="1_4" />

			<taskReward type="experienceReward" description="经验"
				value="1800000" />
			<taskReward type="moneyReward" description="金币" value="800000" />
		</item>



		<item id="10018" name="嗜血追击" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="嗜血星球上的嗜血妖王拥有极快的生命恢复能力，用最快的时间击败他1次，证明你的实力吧！">
			<taskGoal ids="10031" nums="1" />
			<everygototask type="8">
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_18" />
			</everygototask>
			<taskReward type="equipmentReward" description="4级生命宝石X1_幸运宝石X4"
				ids="11900003_10500000" nums="1_4" />

			<taskReward type="experienceReward" description="经验"
				value="1800000" />
			<taskReward type="moneyReward" description="金币" value="800000" />
		</item>


		<item id="10014" name="剿匪任务" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="传闻在元素禁地中的清风寨中有许多强盗来袭，三界侠士们快去清风寨剿匪吧，每日击败10次山贼头子即可获得丰富的奖励。">
			<taskGoal ids="10014" nums="10" />
			<everygototask type="10" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_2" />
			</everygototask>
			<taskReward type="equipmentReward" description="经验小虾米X1"
				ids="10500065" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="250000" />
		</item>
		<item id="10015" name="老玩家号召任务" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1"
			description="我们呼唤一起共同打僵尸的老朋友重新回来与朋友们相聚，重走西游路，为了曾经的感动，为了共同的梦想，踏上西游的征程！去决战之巅花果山，击杀那些年一起打过的六耳猕猴3次">
			<taskGoal ids="10012" nums="3" />
			<everygototask type="10" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_16" />
			</everygototask>

			<taskReward type="equipmentReward" description="经验小虾米X1"
				ids="10800006" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="3000000" />
			<taskReward type="moneyReward" description="金币" value="250000" />
		</item>

		<item id="10013" name="经验小虾米" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="听说元素禁地中的远古海域中的大目鱼妖头领偷吃了经验小虾米变得十分厉害，快去教训他10次，让他把经验小虾米吐出来！">
			<taskGoal ids="10029" nums="10" />
			<everygototask type="10" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_1" />
			</everygototask>
			<taskReward type="equipmentReward" description="经验小虾米X1"
				ids="12000004" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="250000" />
		</item>


		<item id="10011" name="前往僵尸星球" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1"
			description="一颗流星划过平静的星空，漆黑的宇宙中突然出现一个神秘的黑洞。进入黑洞就可以前往僵尸星球消灭僵尸魔王！快进入黑洞去幻影星球消灭100只幻影僵尸吧！">
			<taskGoal ids="10026" nums="100" />
			<everygototask type="10" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_17" />
			</everygototask>
			<taskReward type="equipmentReward" description="超进化仙果X1"
				ids="10500030" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="250000" />
		</item>
		<item id="10012" name="幻影魔王" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="僵尸星球的魔王们开始躁动不安，他们对西游世界发起了新一波的进攻，幻影魔王降临，快去幻影星球中和他大战3回合！">
			<taskGoal ids="10027" nums="3" />
			<everygototask type="10" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap3.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap3.xml" />
				<classname value="RouteMap3" />
				<gotoname value="levelMaptBtn_17" />
			</everygototask>
			<taskReward type="equipmentReward" description="幸运宝石X6"
				ids="10500000" nums="6" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="350000" />
		</item>
		<!-- 地狱之门 -->
		<item id="10009" name="地狱之门抓鬼" type="everyDayTask" resetType="everyDayReset"
			isNew="0" isGoto="1"
			description="月圆之日，地狱之门在元素禁地开启，无数僵尸恶鬼在城中作恶，奉师傅之命为民除害，快去元素禁地中的地狱之门消灭100只僵尸恶鬼即可获得奖励。">
			<taskGoal ids="10024" nums="100" />
			<everygototask type="10" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_3" />
			</everygototask>
			<taskReward type="equipmentReward" description="开孔灵符X1"
				ids="10500032" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="900000" />
			<taskReward type="moneyReward" description="金币" value="200000" />
		</item>
		<item id="10010" name="消灭千年僵尸王" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="0" description="听说元素禁地中的地狱之门有千年僵尸王出没，快去教训他一顿，保卫世界的和平！">
			<taskGoal ids="10025" nums="1" />
			<everygototask type="10" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_3" />
			</everygototask>
			<taskReward type="equipmentReward" description="幸运宝石X6"
				ids="10500000" nums="6" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="300000" />
		</item>

		<!-- 暑假任务 -->
		<item id="10006" name="阴曹地府" type="everyDayTask" resetType="everyDayReset"
			isNew="1"
			description="月圆之日，阴曹地府之门终于开启，无数野鬼恶鬼在城中作恶，奉师傅之命为名除害，快去元素禁地中的《地狱迷宫》消灭100只僵尸恶鬼即可获得奖励。">
			<taskGoal ids="10030" nums="100" />

			<taskReward type="equipmentReward" description="二级天火" ids="10500041"
				nums="2" />

			<taskReward type="experienceReward" description="经验"
				value="1500000" />
			<taskReward type="moneyReward" description="金币" value="500000" />
		</item>
		<item id="10007" name="夏日除虫" type="everyDayTask" resetType="everyDayReset"
			isNew="1" description="夏天到了，虫子出没，盘丝洞的蜘蛛精带领着一大波虫子僵尸向长安城发动了攻击，快去《盘丝洞》击败蜘蛛精，保卫长安。">
			<taskGoal ids="10019" nums="5" />

			<taskReward type="equipmentReward" description="幸运宝石X5"
				ids="10500008" nums="5" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="200000" />
		</item>
		<item id="10008" name="炎炎夏日" type="everyDayTask" resetType="everyDayReset"
			isNew="1" description="炎炎盛夏，得知是牛魔王又在火焰山发火，快前去《火焰山》，打败他1次，为夏日降温。">
			<taskGoal ids="10010" nums="1" />

			<taskReward type="equipmentReward" description="双倍经验药水X1_无敌药水X3"
				ids="11400000_10500004" nums="1_10" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="100000" />
		</item>
		<item id="10008" name="炎炎夏日" type="everyDayTask" resetType="everyDayReset"
			isNew="1" description="炎炎盛夏，得知是牛魔王又在火焰山发火，快前去《火焰山》，打败他1次，为夏日降温。">
			<taskGoal ids="10010" nums="1" />

			<taskReward type="equipmentReward" description="双倍经验药水X1_无敌药水X3"
				ids="11400000_10500004" nums="1_10" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="100000" />
		</item>
		<item id="10016" name="全民坐骑大放送" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="六耳猕猴去天宫偷盗天马，奉玉帝之命，去《花果山决战之巅》缉拿六耳猕猴归案！即可获得坐骑召唤卷轴抽取坐骑！">
			<taskGoal ids="10012" nums="1" />
			<everygototask type="10" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_16" />
			</everygototask>
			<taskReward type="equipmentReward" description="幸运宝石X3"
				ids="10500000" nums="3" />
			<taskReward type="zhHJZHRward" description="召唤卷轴" value="2" />
			<taskReward type="lSHSHRward" description="灵兽石" value="60" />
		</item>

		<item id="10017" name="中秋活动" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="1" description="中秋佳节，三界共庆，各处月饼飘香，各位暂停下修炼的脚步，前去《双叉岭》中击败一只僵尸即可获得奖励，">
			<taskGoal ids="10011" nums="1" />
			<everygototask type="9" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_1" />
			</everygototask>
			<taskReward type="equipmentReward" description="幸运蛋X3"
				ids="10800099" nums="3" />
			<taskReward type="zhHJZHRward" description="召唤卷轴" value="3" />

		</item>
		<item id="10000" name="消灭皮搋子僵尸" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="0" description="快去教训教训火云洞里的皮搋子僵尸!它们看到皮搋子以为是很厉害的武器，拿着皮搋子嚣张的游荡在火云洞中！">
			<!--该任务要完成的任务目标 ids：任务目标id （多个可以这样 10001_10002), nums：任务目标数量（20_5) -->
			<taskGoal ids="10001" nums="200" />
			<everygototask type="9" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_4" />
			</everygototask>
			<!--任务奖励 -->
			<!-- type: 任务类型， description：任务描述， ids：物品奖励id（可多个） nums：物品奖励数量 （可多个） -->
			<!-- 任务奖励现在有三个类型： equipmentReward： 物品奖励， experienceReward： 经验奖励， moneyReward：金钱奖励 -->
			<taskReward type="equipmentReward" description="皮料X5、针线X5"
				ids="10500009_10500001" nums="5_5" />


			<!-- value： 金钱奖励或经验奖励的值 -->
			<taskReward type="experienceReward" description="经验"
				value="100000" />
			<taskReward type="moneyReward" description="金币" value="10000" />



		</item>
		<item id="10001" name="三打白骨精" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="0" description="白虎岭的白骨精又扮成人形来迷惑师傅了。三打白骨精让她现出原形吧！">
			<taskGoal ids="10002" nums="3" />
			<everygototask type="9" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_5" />
			</everygototask>
			<taskReward type="equipmentReward" description="铁块X5" ids="10500004"
				nums="10" />
			<taskReward type="experienceReward" description="经验"
				value="100000" />
			<taskReward type="moneyReward" description="金币" value="10000" />


		</item>
		<item id="10002" name="大战金、银角大王" type="everyDayTask" resetType="everyDayReset"  isGoto="1"
			isNew="0" description="金角大王和银角大王对唐僧蠢蠢欲动，快去打消掉他们的念头吧！">
			<taskGoal ids="10003_10004" nums="1_1" />
			<everygototask type="9" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_6" />
			</everygototask>
			<taskReward type="equipmentReward" description="升级宝石X2"
				ids="10500008" nums="10" />
			<taskReward type="experienceReward" description="经验"
				value="100000" />
			<taskReward type="moneyReward" description="金币" value="10000" />
		</item>
		<item id="10003" name="狮驼岭、盘丝洞之难" type="everyDayTask" resetType="everyDayReset"  isGoto="1"
			isNew="0" description="狮驼岭的铁铲僵尸和盘丝洞里的长剑僵尸面向凶恶的冲向唐僧，快保护好师父！">
			<everygototask type="9" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_8" />
			</everygototask>
			<taskGoal ids="10005_10006" nums="200_200" />

			<taskReward type="equipmentReward" description="幸运宝石X1"
				ids="10500000" nums="2" />

			<taskReward type="experienceReward" description="经验"
				value="100000" />
			<taskReward type="moneyReward" description="金币" value="10000" />
		</item>
		<item id="10004" name="车迟国斗法" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="0" description="车迟国斗法，大战虎力大仙、鹿力大仙、羊力大仙。">
			<taskGoal ids="10007_10008_10009" nums="1_1_1" />
			<everygototask type="9" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_10" />
			</everygototask>


			<taskReward type="equipmentReward" description="升级宝石X2,幸运宝石X1"
				ids="10500008_10500000" nums="5_2" />

			<taskReward type="experienceReward" description="经验"
				value="100000" />
			<taskReward type="moneyReward" description="金币" value="10000" />

		</item>
		<item id="10005" name="火焰山灭火" type="everyDayTask" resetType="everyDayReset" isGoto="1"
			isNew="0" description="唐僧被困在火焰山中，牛魔王不肯借扇子来扇熄火焰山的大火。只好动手自己拿啦。">
			<taskGoal ids="10010" nums="3" />
			<everygototask type="9" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_13" />
			</everygototask>
			<taskReward type="equipmentReward" description="宠物精气药剂X1"
				ids="11000006" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="100000" />
			<taskReward type="moneyReward" description="金币" value="30000" />
		</item>

		<!-- taskCount:累积任务要完成的数量 -->
		<item id="12000" name="让牛魔王心服口服！" type="accumulatedTask"
			resetType="DieRevive" taskCount="5" progressBar="UI.TaskPanel.Bar.NuiBoWanJinDuBar"
			isNew="0" isGoto="1"
			description="牛魔王来势汹汹，气焰嚣张。每天教训牛魔王两次就可以打消一些牛魔王的气焰，想要彻底打消必须连续5天持续打消牛魔王气焰！(一旦接取任务后开始计时，5天内都完成任务即可获得任务奖励，中断则任务失败。)">
			<taskGoal ids="10010" nums="2" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_13" />
			</activitytask>
			<taskReward type="equipmentReward" description="精气药剂X1"
				ids="11000003" nums="1" />
			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="200000" />
		</item>
		<!--钥匙任务 -->
		<item id="12002" name="钥匙任务" type="accumulatedTask" resetType="DieRevive"
			taskCount="5" progressBar="UI.TaskPanel.Bar.LiuErMiHouBar" isNew="1" isGoto="1"
			description="花果山决战之巅的六耳猕猴偷走如来佛祖的开箱钥匙，每日教训六耳猕猴一次，连续教训六耳猕猴5天即可获得开启背包钥匙！(一旦接取任务后开始计时，5天内都完成任务即可获得任务奖励，中断则任务失败。)">
			<taskGoal ids="10012" nums="1" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_16" />
			</activitytask>
			<taskReward type="equipmentReward" description="背包钥匙" ids="11100002"
				nums="1" />
			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="300000" />
		</item>

		<!--群组任务 -->
		<item id="12001" name="群组签到" type="accumulatedTask" resetType="DieRevive"
			taskCount="7" progressBar="UI.TaskPanel.Bar.SignTaskBar" isNew="1"  isGoto="0"
			description="签到赢勋章！连续进入西游大战僵尸2群组签到七天便可获得《群组达人勋章》，更有超强属性的《群组荣誉勋章》等你来拿！（点击进入群组，在签到帖按格式签到，然后返回游戏点击完成任务即可完成每天签到，连续七天便可获得勋章）"
			formerData="2013-04-29 00:00:00" latterData="2016-05-02 00:00:00">

			<taskGoal ids="10023" nums="1" />


			<taskReward type="equipmentReward" description="群组达人勋章X1"
				ids="11600015" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="999999" />
			<taskReward type="moneyReward" description="金币" value="300000" />
		</item>

		<!--五一任务 -->
		<item id="13003" name="五一节日大放送" type="accumulatedTask" resetType="once"
			taskCount="3" progressBar="UI.TaskPanel.Bar.NewYearTaskBar" isNew="1" isGoto="1"
			description="五一劳动节，打僵尸，送道具大礼包！每天去花果山·决战之巅击败假的美猴王六耳猕猴五次！每天击杀完成，请记得点击完成任务。(该节日任务于5月3日凌晨0点消失)"
			formerData="2014-04-28 00:00:00" latterData="2014-05-03 00:00:00">

			<taskGoal ids="10012" nums="5" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_16" />
			</activitytask>

			<taskReward type="equipmentReward" description="宠物蛋（蛋壳龙）X1"
				ids="11000018_11000012_11000009_11000003" nums="1_1_1_1" />


			<taskReward type="experienceReward" description="经验"
				value="5000000" />
			<taskReward type="moneyReward" description="金币" value="500000" />
		</item>

		<!--六一任务 -->
		<item id="13004" name="儿童节任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1" isGoto="1"
			description="儿童节来了！可恶的清风寨山贼们抢走了孩子们的糖果和礼物！教训它们，把孩子们的礼物抢回来。(该节日任务于6月2日凌晨0点消失)"
			formerData="2014-05-27 00:00:00" latterData="2014-06-02 00:00:00">

			<taskGoal ids="10014" nums="10" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_2" />
			</activitytask>

			<taskReward type="equipmentReward" description="铸剑石（地炎宝剑）X1"
				ids="10500030_10800006" nums="5_1" />

			<taskReward type="experienceReward" description="经验"
				value="6666666" />
			<taskReward type="moneyReward" description="金币" value="666666" />
		</item>
		<!--七夕任务 -->
		<item id="13006" name="欢乐过七夕" type="limitingTimeAccumulatedTask" isGoto="1"
			resetType="once" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="0" description="七夕来了，该死的牛魔王又去找狐狸精约会了，帮助铁扇公主教训牛魔王3次即可获得宠物超进化仙果。"
			formerData="2013-08-2 00:00:00" latterData="2013-09-01 00:00:00">

			<taskGoal ids="10010" nums="3" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_13" />
			</activitytask>
			<taskReward type="equipmentReward" description="超进化仙果X1"
				ids="10500030" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="777777" />
			<taskReward type="moneyReward" description="金币" value="477777" />

		</item>
		<!--中秋任务 -->
		<item id="13008" name="中秋节任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1" isGoto="1"
			description="中秋佳节，花好月圆，庆祝中秋的月饼被六耳猕猴抢走了，速速前去花果山决战之巅教训六耳猕猴三次，让他再也不敢来捣蛋。（该节日任务于9月22日凌晨0点消失）"
			formerData="2013-09-13 00:00:00" latterData="2013-09-22 00:00:00">
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_16" />
			</activitytask>
			<taskGoal ids="10012" nums="3" />
			<taskReward type="equipmentReward" description="碎石锤X1_开孔灵符X1_大圣令X1"
				ids="10500032_10500031_10500023" nums="1_1_1" />

			<taskReward type="experienceReward" description="经验"
				value="1000000" />
			<taskReward type="moneyReward" description="金币" value="500000" />


		</item>
		<!--国庆任务 -->

		<item id="13009" name="[循环]国庆大丰收！" type="limitingTimeAccumulatedTask"
			resetType="DieRevive" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1" isGoto="1"
			description="金秋十月，此刻的长安城内一派丰收乐景，又正逢一年一度的国庆大典召开在即，牛魔王率领的手下的大军正在向长安城发起突袭，牛魔王、六耳猕猴和山贼头子正在密谋进攻长安，快去教训他们，让国庆大典正常举行。（该节日任务于10月10日凌晨0点消失，任务在消失前可以多次完成）"
			formerData="2013-09-27 00:00:00" latterData="2013-10-10 00:00:00">
			<taskGoal ids="10010_10012_10014" nums="1_1_1" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_13" />
			</activitytask>
			<taskReward type="equipmentReward" description="幸运宝石X1"
				ids="10500000" nums="5" />

			<taskReward type="experienceReward" description="经验"
				value="400000" />
			<taskReward type="moneyReward" description="金币" value="300000" />

		</item>
		<item id="13005" name="儿童节任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1"
			description="新年来了！但是庆祝新年用的新衣服和鞭炮都被可恶的双叉岭僵尸抢走了！连续教训它们2天，让它们再也不敢来抢。(该节日任务于1月7日凌晨0点消失)"
			formerData="2013-05-31 00:00:00" latterData="2013-06-02 00:00:00">

			<taskGoal ids="10011" nums="1" />


			<taskReward type="equipmentReward" description="新年时装X1"
				ids="10900000" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="666666" />
			<taskReward type="moneyReward" description="金币" value="66666" />
		</item>
		<item id="13007" name="开学第一课" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1" isGoto="1"
			description="小伙伴们又开学了，牛魔王偷走了小伙伴们的暑假作业，快去火焰山打败牛魔王五次即可抢回暑假作业（该任务与9月22日凌晨0点消失）"
			formerData="2013-09-02 00:00:00" latterData="2013-09-22 00:00:00">

			<taskGoal ids="10010" nums="5" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_13" />
			</activitytask>

			<taskReward type="equipmentReward" description="背包钥匙X3_仓库钥匙X3"
				ids="11100002_11100003" nums="3_3" />

			<taskReward type="experienceReward" description="经验"
				value="666666" />
			<taskReward type="moneyReward" description="金币" value="266666" />
		</item>
		<!--圣诞任务 -->

		<item id="13010" name="[循环]寻找圣诞老人的礼物！" type="limitingTimeAccumulatedTask"
			resetType="DieRevive" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1" isGoto="1"
			description="你挂在床头的红袜子始终没有收到圣诞老人的礼物，你找到土地老头，他说圣诞礼物被假扮圣诞老人的山贼头子骗走了，每天打败山贼头子3次，抢回大家的圣诞礼物。（该节日任务于2014年01月04日凌晨0点消失，任务在消失前可以多次完成）"
			formerData="2013-12-19 00:00:00" latterData="2014-01-04 00:00:00">
			<taskGoal ids="10014" nums="3" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_2" />
			</activitytask>
			<taskReward type="equipmentReward" description="幸运蛋X1"
				ids="10800099" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="400000" />
			<taskReward type="moneyReward" description="金币" value="300000" />

		</item>
		<!--元旦任务 -->

		<item id="13000" name="新年节日任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="2" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1"
			description="新年来了！但是庆祝新年用的新衣服和鞭炮都被可恶的双叉岭僵尸抢走了！连续教训它们2天，让它们再也不敢来抢。(该节日任务于1月7日凌晨0点消失)"
			formerData="2012-12-29 00:00:00" latterData="2013-01-02 00:00:00">

			<taskGoal ids="10010" nums="1" />


			<taskReward type="equipmentReward" description="新年时装X1"
				ids="10900003" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="888888" />
			<taskReward type="moneyReward" description="金币" value="88888" />
		</item>



		<item id="13001" name="节日补偿任务" type="limitingTimeAccumulatedTask"
			resetType="once" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1"
			description="新年来了！但是庆祝新年用的新衣服和鞭炮都被可恶的双叉岭僵尸抢走了！去教训它们，让它们再也不敢来抢。(该节日任务于1月15日凌晨0点消失)"
			formerData="2012-01-04 00:00:00" latterData="2013-01-15 00:00:00">

			<taskGoal ids="10011" nums="1" />


			<taskReward type="equipmentReward" description="新年时装X1"
				ids="10900003" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="888888" />
			<taskReward type="moneyReward" description="金币" value="88888" />
		</item>




		<!--圣诞任务 -->

		<item id="13011" name="[循环]寻找圣诞老人的礼物！" type="limitingTimeAccumulatedTask"
			resetType="DieRevive" taskCount="1" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1" isGoto="1" 
			description="你挂在床头的红袜子始终没有收到圣诞老人的礼物，你找到土地老头，他说圣诞礼物被假扮圣诞老人的山贼头子骗走了，每天打败山贼头子3次，抢回大家的圣诞礼物。（该节日任务于2015年01月04日凌晨0点消失，任务在消失前可以多次完成）"
			formerData="2014-12-14 00:00:00" latterData="2015-01-04 00:00:00">
			<taskGoal ids="10014" nums="3" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_2" />
			</activitytask>
			<taskReward type="equipmentReward" description="幸运蛋X1"
				ids="10800099" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="400000" />
			<taskReward type="moneyReward" description="金币" value="300000" />

		</item>






		<item id="11000" name="元旦任务" type="limitingTimeTask" resetType="once"
			isNew="1" description="新年来了！但是庆祝新年用的新衣服和鞭炮都被可恶的双叉岭僵尸抢走了！连续教训它们3天，让它们再也不敢来抢。"
			formerData="2012-12-29 00:00:00" latterData="2013-01-07 00:00:00">
			<taskGoal ids="10011" nums="3" />

			<taskReward type="equipmentReward" description="新年时装X1"
				ids="10900003" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="100000" />
			<taskReward type="moneyReward" description="金币" value="30000" />
		</item>


		<item id="11002" name="[单次]真假美猴王" type="limitingTimeTask"
			resetType="once" isNew="0" isGoto="1"
			description="去花果山·决战之巅击败假的美猴王六耳猕猴！只有它才会掉落大圣翎，从它身上获得大圣翎做出大圣套装来让它明白谁才是齐天大圣孙悟空！（这个任务是单次任务，完成之后不再出现）"
			formerData="2010-12-29 00:00:00" latterData="2999-01-07 00:00:00">

			<taskGoal ids="10012" nums="1" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_16" />
			</activitytask>

			<taskReward type="equipmentReward" description="大圣翎X1"
				ids="10500023" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="500000" />
			<taskReward type="moneyReward" description="金币" value="100000" />
		</item>
		<item id="11003" name="[单次]剿灭清风寨山贼" type="limitingTimeTask"
			resetType="once" isNew="0" isGoto="1"
			description="在元素禁地的探险中，唐僧误入山贼窝，被清风寨山贼抓走了，快去剿灭山贼头子，救出师父吧（这个任务是单次任务，完成之后不再出现）"
			formerData="2010-12-29 00:00:00" latterData="2999-01-07 00:00:00">

			<taskGoal ids="10014" nums="1" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_2" />
			</activitytask>

			<taskReward type="equipmentReward" description="宠物训练卡X1"
				ids="11000009" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="500000" />
			<taskReward type="moneyReward" description="金币" value="200000" />
		</item>
		<item id="11004" name="[单次]重走西游路" type="limitingTimeTask"
			resetType="once" isNew="1" isGoto="1"
			description="重走西游征程，击杀所有西游征程关卡BOSS，获得通关奖励！（这个任务是单次任务，完成之后不再出现）"
			formerData="2010-12-29 00:00:00" latterData="2999-01-07 00:00:00">

			<taskGoal
				ids="10013_10015_10016_10017_10002_10004_10003_10018_10019_10009_10008_10007_10010_10020_10021_10012"
				nums="1_1_1_1_1_1_1_1_1_1_1_1_1_1_1_1" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_1" />
			</activitytask>

			<taskReward type="equipmentReward" description="洗髓丹X3"
				ids="11000010" nums="3" />

			<taskReward type="experienceReward" description="经验"
				value="5000000" />
			<taskReward type="moneyReward" description="金币" value="1000000" />
		</item>

		<item id="11005" name="[单次]降魔之人" type="limitingTimeTask"
			resetType="once" isNew="1" isGoto="1"
			description="传说中获得降魔之剑的人将获得强大无比的力量，此把魔剑被六耳猕猴夺走了，快去花果山决战之巅教训他3次，夺回魔剑！（这个任务是单次任务，完成之后不再出现）"
			formerData="2010-12-29 00:00:00" latterData="2999-01-07 00:00:00">

			<taskGoal ids="10012" nums="3" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
				<classname value="RouteMap1" />
				<gotoname value="levelMaptBtn_16" />
			</activitytask>

			<taskReward type="equipmentReward" description="魔剑蛋X1_超进化仙果X1"
				ids="10800007_10500030" nums="1_1" />

			<taskReward type="experienceReward" description="经验"
				value="500000" />
			<taskReward type="moneyReward" description="金币" value="300000" />
		</item>

		<item id="11006" name="[单次]巨魔岛" type="limitingTimeTask"
			resetType="once" isNew="1" isGoto="1"
			description="在元素禁地这片未知的大陆上居住着一群食人巨魔，他们只吃活人，前去巨魔岛教训食人巨魔3次，拯救岛屿的居民！"
			formerData="2010-12-29 00:00:00" latterData="2999-01-07 00:00:00">

			<taskGoal ids="10028" nums="3" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_4" />
			</activitytask>

			<taskReward type="equipmentReward" description="圣灵精华X1"
				ids="10500035" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="5000000" />
			<taskReward type="moneyReward" description="金币" value="999999" />
		</item>

		<item id="13002" name="[循环]教训年兽！" type="limitingTimeAccumulatedTask"
			resetType="DieRevive" taskCount="4" progressBar="UI.TaskPanel.Bar.NewYearTaskBar"
			isNew="1"
			description="新年来了！双叉岭的寅将军扮成年兽又骚动起来了，去教训教训它！连续4天打败它就可以获得新年奖励哦！(该节日任务于3月2日凌晨0点消失，该任务在消失前可以多次完成。)"
			formerData="2013-02-01 00:00:00" latterData="2013-03-02 00:00:00">
			<taskGoal ids="10013" nums="1" />

			<taskReward type="equipmentReward" description="新年时装X1"
				ids="10900003" nums="1" />

			<taskReward type="experienceReward" description="经验"
				value="888888" />
			<taskReward type="moneyReward" description="金币" value="888888" />
		</item>


		<item id="11007" name="[单次]妖将新玩法" type="limitingTimeTask" isGoto="1"
			resetType="once" isNew="1" description="妖将新玩法开启，巨魔岛新掉落妖将内丹，前去巨魔岛教训食人巨魔5次，他将送你新妖将道具！"
			formerData="2010-12-29 00:00:00" latterData="2999-01-07 00:00:00">

			<taskGoal ids="10028" nums="5" />
			<activitytask type="1" >
				<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
				<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
				<classname value="RouteMap2" />
				<gotoname value="levelMaptBtn_4" />
			</activitytask>

			<taskReward type="equipmentReward" description="圣灵精华X1"
				ids="10500064_10500065_10500066_10500067" nums="1_1_1_1" />

			<taskReward type="experienceReward" description="经验"
				value="5000000" />
			<taskReward type="moneyReward" description="金币" value="999999" />
		</item>

	</Task>



</Task>