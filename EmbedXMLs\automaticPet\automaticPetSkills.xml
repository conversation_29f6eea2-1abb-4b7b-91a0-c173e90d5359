<?xml version="1.0" encoding="utf-8" ?>
<data>
<!--hurtMulti="1"普攻伤害增加200%-->
	<!--小虾技能-->
    <skill skillId="smallShrimpAttack" name="普通攻击"   description="虾兵使用强有力的钳子对敌人发动攻击。" 
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	    <levelData level="1">
			<data att="skillHurtAdd"  value="100" />
			<data att="skillHurtMulti" value="0.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack" />
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="200" />
			<data att="skillHurtMulti" value="0.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="0.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="0.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="0.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="0.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="0.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="0.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="0.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_8" />
		</levelData>
		<levelData level="10">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_9" />
		</levelData>

	
	</skill>

    <!--乌龟技能-->
    <skill skillId="tortoiseAttack" name="普通攻击" description="发动基础攻击，造成100%并附加100点伤害。" 
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	    <levelData level="1">
			<data att="skillHurtAdd"  value="100" />
			<data att="skillHurtMulti" value="0.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack" />
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="200" />
			<data att="skillHurtMulti" value="0.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="0.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="0.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="0.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="0.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="0.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="0.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="0.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_8" />
		</levelData>
		<levelData level="10">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_9" />
		</levelData>


		
		
	</skill>
	
	<!--鲛人技能-->
	<skill skillId="jiaoRenAttack" name="普通攻击" description="发动基础攻击，造成110%并附加200点伤害。" 
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
		<levelData level="1">
			<data att="skillHurtAdd"  value="200" />
			<data att="skillHurtMulti" value="0.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack" />
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="0.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="0.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="0.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="0.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="0.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="0.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="0.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="0.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_8" />
		</levelData>		
		<levelData level="10">
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillHurtMulti" value="1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_9" />
		</levelData>		
		
	</skill>
    <skill skillId="jiaoRenSkill1" name="飞矛突刺"  description="鲛人将军挥舞手中的飞矛，迅速冲向敌人，对敌人造成250%并附加500点攻击伤害，攻击特效：击退敌人。"   
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
		<levelData level="1">
			<data att="skillHurtMulti" value="2.5" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill" />
		</levelData>	 
		<levelData level="2">
			<data att="skillHurtMulti" value="2.7" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="2.9" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="3.1" />
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="3.3" />
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="3.5" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="3.7" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="3.9" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="4.3" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_JiaoRenSkill9" />
		</levelData>	 
	 
	
	</skill>
	
	<!--龙王技能-->
	<skill skillId="dragonKingAttack" name="普通攻击" description="发动基础攻击，造成120%并附加200点伤害。" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	   <levelData level="1">

			<data att="skillHurtAdd"  value="200" />
			<data att="skillHurtMulti" value="0.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
		    <data att="iconClassName" value="PetSkill_Attack" />			
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="0.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="0.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="0.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="0.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="0.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="0.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="0.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_8" />
		</levelData>		
		<levelData level="10">
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillHurtMulti" value="1.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_9" />
		</levelData>		
		
	</skill>
	
    <skill skillId="dragonKingSkill1" name="恶龙突袭" description="龙王挥舞手中的宝剑，迅速刺向敌人，对敌人造成300%并附加600点攻击伤害，攻击特效：击退敌人。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
        <levelData level="1">
			<data att="skillHurtMulti" value="2" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1" />
		</levelData>	
		<levelData level="2">
			<data att="skillHurtMulti" value="2.7" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="2.9" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="3.1" />
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="3.3" />
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="3.5" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="3.7" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="3.9" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="4.3" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill1_9" />
		</levelData>	 
	 		
		
		
	</skill>
	
	<skill skillId="dragonKingSkill2" name="龙卷雨击"   description="龙王施展法术攻击敌人，呼风唤雨，对敌人造成200%并附加500点攻击伤害。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="3" priorityForRun="0">
        <levelData level="1">
			<data att="skillHurtMulti" value="1" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2" />
		</levelData>

		<levelData level="2">
			<data att="skillHurtMulti" value="1.2" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="1.4" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="1.6" />
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="1.8" />
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="2" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="2.2" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="2.4" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="2.6" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="2.8" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DragonKingSkill2_9" />
		</levelData>	 
	 
		
			
	</skill>
	

	
	<!--增长1技能-->
	<skill skillId="zengZhang3Attack" name="普通攻击" description="发动基础攻击，造成120%并附加200点伤害。" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	   <levelData level="1">

			<data att="skillHurtAdd"  value="200" />
			<data att="skillHurtMulti" value="0.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
		    <data att="iconClassName" value="PetSkill_Attack" />			
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="0.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="0.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="0.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="0.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="0.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="0.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="0.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_8" />
		</levelData>		
		<levelData level="10">
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillHurtMulti" value="1.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack_9" />
		</levelData>		
		
	</skill>
	
    <skill skillId="zengZhang3Skill1" name="飞天转移" description="增长天王的天赋技能，可飞天位移到怪物前方，该技能无法升级。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetActiveSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="1" priorityForRun="0">
        <levelData level="1">
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill1" />
		</levelData>	
	</skill>
	
	<skill skillId="zengZhang3Skill2" name="天王剑舞"   description="天王以流星般的速度挥剑攻击，对敌人造成150%附加300点伤害。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
        <levelData level="1">
			<data att="skillHurtMulti" value="1.5" />
			<data att="skillHurtAdd"  value="300" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2" />
		</levelData>

		<levelData level="2">
			<data att="skillHurtMulti" value="1.6" />
			<data att="skillHurtAdd"  value="400" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="1.7" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="1.8" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="1.9" />
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="2.1" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="2.5" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="2.7" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="2.9" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="3.1" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill2_9" />
		</levelData>	 
	 
		
			
	</skill>
	<skill skillId="zengZhang3Skill3" name="剑荡八荒"   description="上古神技，剑荡八荒，毁天灭地，对敌人造成200%附加500点攻击伤害，并且晕眩敌人！"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="2">
   <levelData level="1">
			<data att="skillHurtMulti" value="3" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3" />
		</levelData>	
		<levelData level="2">
			<data att="skillHurtMulti" value="3.7" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="3.9" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="4.3" />
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="4.5" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="4.7" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="4.9" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="5.1" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="5.5" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZengZhangSkill3_9" />
		</levelData>	 
		
			
	</skill>
	
		<!--广目1技能-->
	<skill skillId="Guangmu1Attack" name="普通攻击" description="发动基础攻击，造成120%并附加200点伤害。" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	   <levelData level="1">

			<data att="skillHurtAdd"  value="200" />
			<data att="skillHurtMulti" value="0.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
		    <data att="iconClassName" value="PetSkill_Attack1" />			
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="0.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="0.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="0.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="0.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="0.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="0.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="0.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_8" />
		</levelData>		
		<levelData level="10">
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillHurtMulti" value="1.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack1_9" />
		</levelData>		
		
	</skill>
	
    <skill skillId="Guangmu1Skill1" name="飞天转移" description="广目天王的天赋技能，可飞天位移到怪物前方，该技能无法升级。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetActiveSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="1" priorityForRun="0">
        <levelData level="1">
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill1" />
		</levelData>	
	</skill>
	
	<skill skillId="Guangmu1Skill2" name="珠光破"   description="广目天王丢出手中宝珠，对接触到的敌人造成150%附加300点伤害，并爆炸伤害周围的敌人。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
        <levelData level="1">
			<data att="skillHurtMulti" value="1.5" />
			<data att="skillHurtAdd"  value="300" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2" />
		</levelData>

		<levelData level="2">
			<data att="skillHurtMulti" value="1.6" />
			<data att="skillHurtAdd"  value="400" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="1.7" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="1.8" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="1.9" />
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="2.1" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="2.5" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="2.7" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="2.9" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="3.1" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="2000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill2_9" />
		</levelData>	 
	 
		
			
	</skill>
	<skill skillId="Guangmu1Skill3" name="风雷击"   description="广目天王丢出手中金蛇，金蛇召唤乌云跟随雷击敌人，对敌人造成200%附加500点攻击伤害！"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="2">
   <levelData level="1">
			<data att="skillHurtMulti" value="3" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="6000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3" />
		</levelData>	
		<levelData level="2">
			<data att="skillHurtMulti" value="3.7" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="3.9" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="4.3" />
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="4.5" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="4.7" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="4.9" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="5.1" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="5.5" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_guangmuSkill3_9" />
		</levelData>	 
	</skill>
	
		
	<!--多闻技能-->
	<skill skillId="Duowen3Attack" name="普通攻击" description="发动基础攻击，造成220%并附加200点伤害。" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	   <levelData level="1">

			<data att="skillHurtAdd"  value="200" />
			<data att="skillHurtMulti" value="1.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
		    <data att="iconClassName" value="PetSkill_Attack2" />			
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="1.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="1.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="1.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="1.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="1.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="1.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="1.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_8" />
		</levelData>		
		<levelData level="10">
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillHurtMulti" value="2.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack2_9" />
		</levelData>		
		
	</skill>
	
    <skill skillId="Duowen3Skill1" name="飞天转移" description="多闻天王的天赋技能，可飞天位移到怪物前方，该技能无法升级。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetActiveSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="1" priorityForRun="0">
        <levelData level="1">
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill1" />
		</levelData>	
	</skill>
	
	<skill skillId="Duowen3Skill2" name="伞照四方"   description="多闻天王丢出手中宝伞在头顶飞舞，对周围敌人造成连续伤害。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
        <levelData level="1">
			<data att="skillHurtMulti" value="1.2" />
			<data att="skillHurtAdd"  value="300" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2" />
		</levelData>

		<levelData level="2">
			<data att="skillHurtMulti" value="1.5" />
			<data att="skillHurtAdd"  value="400" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="1.7" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="2.0" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="2.5" />
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="3.1" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="3.5" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="4.5" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="5.1" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill2_9" />
		</levelData>	 
	 
		
			
	</skill>
	<skill skillId="Duowen3Skill3" name="致命冲击"   description="多闻天王快速翻滚向前冲击，对敌人造成大量伤害，并且击退敌人！"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="2">
   <levelData level="1">
			<data att="skillHurtMulti" value="3" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3" />
		</levelData>	
		<levelData level="2">
			<data att="skillHurtMulti" value="3.7" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="3.9" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="4.3" />
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="4.5" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="4.7" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="5.5" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="6.1" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="6.5" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_DuowenSkill3_9" />
		</levelData>	 
		
			
	</skill>
	
	
	<!--哪吒1技能-->
	<skill skillId="Nezha3Attack" name="普通攻击" description="发动基础攻击，造成120%并附加200点伤害。" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	   <levelData level="1">

			<data att="skillHurtAdd"  value="200" />
			<data att="skillHurtMulti" value="1.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
		    <data att="iconClassName" value="PetSkill_Attack5" />			
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="1.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="1.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="1.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="1.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="1.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="1.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="2.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="2.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_8" />
		</levelData>		
		<levelData level="10">
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillHurtMulti" value="2.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Attack5_9" />
		</levelData>		
		
	</skill>
	
    <skill skillId="Nezha3Skill1" name="环环相扣" description="哪吒抛出乾坤圈打击并晕眩敌人。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
        <levelData level="1">
			<data att="skillHurtMulti" value="2.5" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2" />
		</levelData>

		<levelData level="2">
			<data att="skillHurtMulti" value="2.6" />
			<data att="skillHurtAdd"  value="800" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="2.7" />
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="2.8" />
			<data att="skillHurtAdd"  value="1200" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="2.9" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="3.1" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="3.5" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="3.7" />
			<data att="skillHurtAdd"  value="2000" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="3.9" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="20" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="2500" />
			<data att="skillCostMp" value="22" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill2_9" />
		</levelData>	
	</skill>
	
	<skill skillId="Nezha3Skill2" name="混天禁锢"   description="哪吒抛出混天绫对敌人造成450%附加500点攻击伤害，并且禁锢敌人！"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
        <levelData level="1">
			<data att="skillHurtMulti" value="3.5" />
			<data att="skillHurtAdd"  value="300" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="15000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3" />
		</levelData>

		<levelData level="2">
			<data att="skillHurtMulti" value="3.6" />
			<data att="skillHurtAdd"  value="400" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="15000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="3.7" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="15000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="3.8" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="15000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="3.9" />
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="15000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="15000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="4.5" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="14500" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="4.7" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="13000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="4.9" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="20" />
			<data att="skillCdTime" value="13500" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="5.1" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="22" />
			<data att="skillCdTime" value="12000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NezhaSkill3_9" />
		</levelData>	 
	 
		
			
	</skill>
	<skill skillId="Nezha3Skill3" name="暴怒"   description="哪吒短时间内收到大量伤害后会激发暴怒形态，提升200点暴击并无视怪物攻击，暴怒持续时间随妖将等级提升。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="2">
   <levelData level="1">
			<data att="skillHurtMulti" value="2.5" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="19000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu" />
		</levelData>	
		<levelData level="2">
			<data att="skillHurtMulti" value="2.9" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="18000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="3.1" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="17000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="3.3" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="16000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="3.5" />
			<data att="skillHurtAdd"  value="1200" />
			<data att="skillCostMp" value="20" />
			<data att="skillCdTime" value="15000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="3.7" />
			<data att="skillHurtAdd"  value="1400" />
			<data att="skillCostMp" value="22" />
			<data att="skillCdTime" value="140000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="3.9" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="24" />
			<data att="skillCdTime" value="13000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="4.2" />
			<data att="skillHurtAdd"  value="1600" />
			<data att="skillCostMp" value="26" />
			<data att="skillCdTime" value="12000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="4.5" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="28" />
			<data att="skillCdTime" value="11000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="4.8" />
			<data att="skillHurtAdd"  value="1800" />
			<data att="skillCostMp" value="30" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Baonu9" />
		</levelData>	 
		
			
	</skill>
	
	
		<!--风伯技能-->
	<skill skillId="fengboAttack" name="普通攻击" description="发动基础攻击，造成140%并附加300点伤害。" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	   <levelData level="1">

			<data att="skillHurtAdd"  value="300" />
			<data att="skillHurtMulti" value="0.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
		    <data att="iconClassName" value="PetSkill_Fengbo3" />			
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="0.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="0.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="0.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="0.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="0.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="1.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillHurtMulti" value="1.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_8" />
		</levelData>		
		<levelData level="10">
			<data att="skillHurtAdd"  value="1200" />
			<data att="skillHurtMulti" value="1.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo3_9" />
		</levelData>		
		
	</skill>
	
    <skill skillId="fengboSkill1" name="飞天转移" description="风伯的天赋技能，可飞天位移到怪物前方，该技能无法升级。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetActiveSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="1" priorityForRun="0">
        <levelData level="1">
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo4" />
		</levelData>	
	</skill>
	
	<skill skillId="fengboSkill2" name="致命旋风"   description="风伯向前方吹出致命旋风，对敌人造成550%附加300点伤害，并减速敌人。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
        <levelData level="1">
			<data att="skillHurtMulti" value="4.5" />
			<data att="skillHurtAdd"  value="300" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1" />
		</levelData>

		<levelData level="2">
			<data att="skillHurtMulti" value="4.9" />
			<data att="skillHurtAdd"  value="400" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="5.3" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="5.6" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="5.9" />
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="6.3" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="6.7" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="7.1" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="7.5" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="8.1" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="5000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo1_9" />
		</levelData>	 
	 
		
			
	</skill>
	<skill skillId="fengboSkill3" name="无极之风"   description="风伯向西周吹出无极之风，对敌人造成500%附加500点攻击伤害，并且击飞敌人！"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="2">
   <levelData level="1">
			<data att="skillHurtMulti" value="4" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2" />
		</levelData>	
		<levelData level="2">
			<data att="skillHurtMulti" value="4.7" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="4.9" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="5.1" />
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="5.3" />
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="5.5" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="5.7" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="5.9" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="6.1" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="6.5" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Fengbo2_9" />
		</levelData>	 
		
	</skill>
	
		<!--雨师技能-->
	<skill skillId="yushiAttack" name="普通攻击" description="发动基础攻击，造成320%并附加400点伤害。" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" >
	   <levelData level="1">

			<data att="skillHurtAdd"  value="400" />
			<data att="skillHurtMulti" value="2.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
		    <data att="iconClassName" value="PetSkill_Yushi3" />			
		</levelData>
		<levelData level="2">
			<data att="skillHurtAdd"  value="500" />
			<data att="skillHurtMulti" value="2.3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_1" />
		</levelData>
		<levelData level="3">
			<data att="skillHurtAdd"  value="600" />
			<data att="skillHurtMulti" value="2.4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_2" />
		</levelData>
		<levelData level="4">
			<data att="skillHurtAdd"  value="700" />
			<data att="skillHurtMulti" value="2.5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_3" />
		</levelData>
		<levelData level="5">
			<data att="skillHurtAdd"  value="800" />
			<data att="skillHurtMulti" value="2.6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_4" />
		</levelData>
		<levelData level="6">
			<data att="skillHurtAdd"  value="900" />
			<data att="skillHurtMulti" value="2.7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_5" />
		</levelData>
		<levelData level="7">
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillHurtMulti" value="2.8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_6" />
		</levelData>
		<levelData level="8">
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillHurtMulti" value="2.9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_7" />
		</levelData>
		<levelData level="9">
			<data att="skillHurtAdd"  value="1200" />
			<data att="skillHurtMulti" value="3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_8" />
		</levelData>		
		<levelData level="10">
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillHurtMulti" value="3.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi3_9" />
		</levelData>		
		
	</skill>
	
    <skill skillId="yushiSkill1" name="飞天转移" description="雨师的天赋技能，可飞天位移到怪物前方，该技能无法升级。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetActiveSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="1" priorityForRun="0">
        <levelData level="1">
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi4" />
		</levelData>	
	</skill>
	
	<skill skillId="yushiSkill2" name="雨幕"   description="雨师带来持续降雨，对周围敌人造成连续伤害，并降低敌人的命中与暴击。"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="1">
        <levelData level="1">
			<data att="skillHurtMulti" value="2.2" />
			<data att="skillHurtAdd"  value="500" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1" />
		</levelData>

		<levelData level="2">
			<data att="skillHurtMulti" value="2.5" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="2.7" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="3.0" />
			<data att="skillHurtAdd"  value="800" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="3.5" />
			<data att="skillHurtAdd"  value="1000" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="4.1" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="4.5" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="5.1" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="5.5" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="6.1" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="10000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi1_9" />
		</levelData>	 
	 
		
			
	</skill>
	<skill skillId="yushiSkill3" name="水龙"   description="雨师将地面积水化作水龙在地面盘旋，打击触碰到的敌人！"  
	className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" isAbleRunInHurt="1" isInvincibleInRun="1" priorityForRunInHurt="0" priorityForRun="2">
   <levelData level="1">
			<data att="skillHurtMulti" value="4" />
			<data att="skillHurtAdd"  value="600" />
			<data att="skillCostMp" value="10" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2" />
		</levelData>	
		<levelData level="2">
			<data att="skillHurtMulti" value="4.7" />
			<data att="skillHurtAdd"  value="700" />
			<data att="skillCostMp" value="11" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_1" />
		</levelData>	 
		<levelData level="3">
			<data att="skillHurtMulti" value="4.9" />
			<data att="skillHurtAdd"  value="900" />
			<data att="skillCostMp" value="12" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_2" />
		</levelData>	 
		<levelData level="4">
			<data att="skillHurtMulti" value="5.1" />
			<data att="skillHurtAdd"  value="1100" />
			<data att="skillCostMp" value="13" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_3" />
		</levelData>	 
		<levelData level="5">
			<data att="skillHurtMulti" value="5.3" />
			<data att="skillHurtAdd"  value="1300" />
			<data att="skillCostMp" value="14" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_4" />
		</levelData>	 
		<levelData level="6">
			<data att="skillHurtMulti" value="5.5" />
			<data att="skillHurtAdd"  value="1500" />
			<data att="skillCostMp" value="15" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_5" />
		</levelData>	 
		<levelData level="7">
			<data att="skillHurtMulti" value="5.7" />
			<data att="skillHurtAdd"  value="1700" />
			<data att="skillCostMp" value="16" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_6" />
		</levelData>	 
		<levelData level="8">
			<data att="skillHurtMulti" value="6.5" />
			<data att="skillHurtAdd"  value="1900" />
			<data att="skillCostMp" value="17" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_7" />
		</levelData>	 
		<levelData level="9">
			<data att="skillHurtMulti" value="7.1" />
			<data att="skillHurtAdd"  value="2100" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_8" />
		</levelData>	 
		<levelData level="10">
			<data att="skillHurtMulti" value="7.5" />
			<data att="skillHurtAdd"  value="2300" />
			<data att="skillCostMp" value="18" />
			<data att="skillCdTime" value="8000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Yushi2_9" />
		</levelData>	 
		
			
	</skill>
	
	<!--被动技能-->
	<skill skillId="pS1" name="必杀"   description="必杀，增加妖将自身暴击率。" className="YJFY.AutomaticPet.PassiveSkillVO.AddCriticalSkill">
	    <levelData level="1">
			<data att="addCriticalPercent"  value="0.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha" />
			<data att="description2" value="增加自身暴击率10%。" />
			
		</levelData>
	    <levelData level="2">
			<data att="addCriticalPercent"  value="0.12" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha1" />
			<data att="description2" value="增加自身暴击率12%。" />			
		</levelData>
	    <levelData level="3">
			<data att="addCriticalPercent"  value="0.14" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha2" />
			<data att="description2" value="增加自身暴击率14%。" />			
		</levelData>
	    <levelData level="4">
			<data att="addCriticalPercent"  value="0.16" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha3" />
			<data att="description2" value="增加自身暴击率16%。" />			
		</levelData>
	    <levelData level="5">
			<data att="addCriticalPercent"  value="0.18" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha4" />
		    <data att="description2" value="增加自身暴击率18%。" />		
		</levelData>
	    <levelData level="6">
			<data att="addCriticalPercent"  value="0.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha5" />
			<data att="description2" value="增加自身暴击率20%。" />		
		</levelData>
	    <levelData level="7">
			<data att="addCriticalPercent"  value="0.22" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha6" />
			<data att="description2" value="增加自身暴击率22%。" />		
		</levelData>
	    <levelData level="8">
			<data att="addCriticalPercent"  value="0.24" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha7" />
			<data att="description2" value="增加自身暴击率24%。" />			
		</levelData>
	    <levelData level="9">
			<data att="addCriticalPercent"  value="0.26" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha8" />
			<data att="description2" value="增加自身暴击率26%。" />			
		</levelData>
	    <levelData level="10">
			<data att="addCriticalPercent"  value="0.28" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_BiSha9" />
			<data att="description2" value="增加自身暴击率28%。" />			
		</levelData>
		
	
	</skill>
	<skill skillId="pS2" name="幸运"   description="幸运，增加妖将自身防爆率。" className="YJFY.AutomaticPet.PassiveSkillVO.AddDeCriticalSkill">
	    <levelData level="1">
			<data att="addDeCriticalPercent"  value="0.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun" />
			<data att="description2" value="增加自身防爆率10%。" />				
		</levelData>
	    <levelData level="2">
			<data att="addDeCriticalPercent"  value="0.12" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun1" />
			<data att="description2" value="增加自身防爆率12%。" />			
		</levelData>
	    <levelData level="3">
			<data att="addDeCriticalPercent"  value="0.14" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun2" />
			<data att="description2" value="增加自身防爆率14%。" />			
		</levelData>
	    <levelData level="4">
			<data att="addDeCriticalPercent"  value="0.16" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun3" />
			<data att="description2" value="增加自身防爆率16%。" />			
		</levelData>
	    <levelData level="5">
			<data att="addDeCriticalPercent"  value="0.18" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun4" />
		    <data att="description2" value="增加自身防爆率18%。" />		
		</levelData>
	    <levelData level="6">
			<data att="addDeCriticalPercent"  value="0.2" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun5" />
			<data att="description2" value="增加自身防爆率20%。" />		
		</levelData>
	    <levelData level="7">
			<data att="addDeCriticalPercent"  value="0.22" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun6" />
			<data att="description2" value="增加自身防爆率22%。" />		
		</levelData>
	    <levelData level="8">
			<data att="addDeCriticalPercent"  value="0.24" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun7" />
			<data att="description2" value="增加自身防爆率24%。" />			
		</levelData>
	    <levelData level="9">
			<data att="addDeCriticalPercent"  value="0.26" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun8" />
			<data att="description2" value="增加自身防爆率26%。" />			
		</levelData>
	    <levelData level="10">
			<data att="addDeCriticalPercent"  value="0.28" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_XingYun9" />
			<data att="description2" value="增加自身防爆率28%。" />			
		</levelData>		
	
	</skill>
	<skill skillId="pS3" name="强力"   description="强力，根据妖将自身等级增加攻击力。" className="YJFY.AutomaticPet.PassiveSkillVO.AddAttackByLevelSkill">
	    <levelData level="1">
			<data att="addAttackNumByLevel"  value="5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×5。" />					
		</levelData>
	    <levelData level="2">
			<data att="addAttackNumByLevel"  value="7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi1" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×7。" />					
		</levelData>
	    <levelData level="3">
			<data att="addAttackNumByLevel"  value="9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi2" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×9。" />					
		</levelData>
	    <levelData level="4">
			<data att="addAttackNumByLevel"  value="11" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi3" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×11。" />					
		</levelData>
	    <levelData level="5">
			<data att="addAttackNumByLevel"  value="13" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi4" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×13。" />					
		</levelData>
	    <levelData level="6">
			<data att="addAttackNumByLevel"  value="15" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi5" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×15。" />					
		</levelData>
	    <levelData level="7">
			<data att="addAttackNumByLevel"  value="17" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi6" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×17。" />					
		</levelData>
	    <levelData level="8">
			<data att="addAttackNumByLevel"  value="19" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi7" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×19。" />					
		</levelData>
	    <levelData level="9">
			<data att="addAttackNumByLevel"  value="21" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi8" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×21。" />					
		</levelData>
	    <levelData level="10">
			<data att="addAttackNumByLevel"  value="23" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangLi9" />
			<data att="description2" value="根据自身等级增加攻击力=妖将等级×23。" />					
		</levelData>
	
	</skill>
	<skill skillId="pS4" name="防御"   description="防御，根据妖将自身等级增加防御力。" className="YJFY.AutomaticPet.PassiveSkillVO.AddDefenceByLevelSkill">
	    <levelData level="1">
			<data att="addDefenceNumByLevel"  value="3" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×3。" />				
		</levelData>
	    <levelData level="2">
			<data att="addDefenceNumByLevel"  value="4" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu1" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×4。" />				
		</levelData>
	    <levelData level="3">
			<data att="addDefenceNumByLevel"  value="5" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu2" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×5。" />				
		</levelData>
	    <levelData level="4">
			<data att="addDefenceNumByLevel"  value="6" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu3" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×6。" />				
		</levelData>
	    <levelData level="5">
			<data att="addDefenceNumByLevel"  value="7" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu4" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×7。" />				
		</levelData>
	    <levelData level="6">
			<data att="addDefenceNumByLevel"  value="8" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu5" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×8。" />				
		</levelData>
	    <levelData level="7">
			<data att="addDefenceNumByLevel"  value="9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu6" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×9。" />				
		</levelData>
	    <levelData level="8">
			<data att="addDefenceNumByLevel"  value="10" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu7" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×10。" />				
		</levelData>
	    <levelData level="9">
			<data att="addDefenceNumByLevel"  value="11" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu8" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×11。" />				
		</levelData>
	    <levelData level="10">
			<data att="addDefenceNumByLevel"  value="12" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FangYu9" />
			<data att="description2" value="根据自身等级增加防御力=妖将等级×12。" />				
		</levelData>
	
	</skill>
	<skill skillId="pS5" name="强壮"   description="强壮，根据妖将自身等级增加生命。" className="YJFY.AutomaticPet.PassiveSkillVO.AddBloodByLevelSkill">
	    <levelData level="1">
			<data att="addBloodNumByLevel"  value="20" />
			<data att="addBloodExtraNum"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×20+100。" />				
		</levelData>
	    <levelData level="2">
			<data att="addBloodNumByLevel"  value="30" />
			<data att="addBloodExtraNum"  value="200" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang1" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×30+200。" />				
		</levelData>
	    <levelData level="3">
			<data att="addBloodNumByLevel"  value="40" />
			<data att="addBloodExtraNum"  value="300" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang2" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×40+300。" />				
		</levelData>
	    <levelData level="4">
			<data att="addBloodNumByLevel"  value="50" />
			<data att="addBloodExtraNum"  value="400" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang3" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×50+400。" />				
		</levelData>
	    <levelData level="5">
			<data att="addBloodNumByLevel"  value="60" />
			<data att="addBloodExtraNum"  value="500" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang4" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×60+500。" />				
		</levelData>
	    <levelData level="6">
			<data att="addBloodNumByLevel"  value="70" />
			<data att="addBloodExtraNum"  value="600" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang5" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×70+600。" />				
		</levelData>
	    <levelData level="7">
			<data att="addBloodNumByLevel"  value="80" />
			<data att="addBloodExtraNum"  value="700" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang6" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×80+700。" />				
		</levelData>
	    <levelData level="8">
			<data att="addBloodNumByLevel"  value="90" />
			<data att="addBloodExtraNum"  value="800" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang7" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×90+800。" />				
		</levelData>
	    <levelData level="9">
			<data att="addBloodNumByLevel"  value="100" />
			<data att="addBloodExtraNum"  value="900" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang8" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×100+900。" />				
		</levelData>
	    <levelData level="10">
			<data att="addBloodNumByLevel"  value="110" />
			<data att="addBloodExtraNum"  value="1000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_QiangZhuang9" />
		    <data att="description2" value="根据自身等级增加生命=妖将等级×110+1000。" />				
		</levelData>
	
	</skill>
	<skill skillId="pS6" name="冥思"   description="冥思，根据妖将自身等级增加魔法。" className="YJFY.AutomaticPet.PassiveSkillVO.AddMagicByLevelSkill">
	    <levelData level="1">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="10" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+10。" />				
		</levelData>
	    <levelData level="2">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="12" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi1" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+12。" />				
		</levelData>
	    <levelData level="3">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="14" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi2" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+14。" />				
		</levelData>
	    <levelData level="4">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="16" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi3" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+16。" />				
		</levelData>
	    <levelData level="5">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="18" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi4" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+18。" />				
		</levelData>
	    <levelData level="6">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="20" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi5" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+20。" />				
		</levelData>
	    <levelData level="7">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="22" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi6" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+22。" />				
		</levelData>
	    <levelData level="8">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="24" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi7" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+24。" />				
		</levelData>
	    <levelData level="9">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="26" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi8" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+26。" />				
		</levelData>
	    <levelData level="10">
			<data att="addMagicNumByLevel"  value="1" />
			<data att="addMagicExtraNum"  value="28" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_MingSi9" />
		    <data att="description2" value="根据自身等级增加魔法=妖将等级×1+28。" />				
		</levelData>
	
	</skill>
	<skill skillId="pS7" name="自愈"   description="自愈，每秒恢复自身百分比生命。" className="YJFY.AutomaticPet.PassiveSkillVO.AddRegHpSkill">
	    <levelData level="1">
			<data att="addRegHpPercent"  value="0.01" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu" />
			<data att="description2" value="每秒恢复自身1%生命" />	
		</levelData>
	    <levelData level="2">
			<data att="addRegHpPercent"  value="0.02" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu1" />
			<data att="description2" value="每秒恢复自身2%生命" />	
		</levelData>
	    <levelData level="3">
			<data att="addRegHpPercent"  value="0.03" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu2" />
			<data att="description2" value="每秒恢复自身3%生命" />	
		</levelData>
	    <levelData level="4">
			<data att="addRegHpPercent"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu3" />
			<data att="description2" value="每秒恢复自身4%生命" />	
		</levelData>
	    <levelData level="5">
			<data att="addRegHpPercent"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu4" />
			<data att="description2" value="每秒恢复自身5%生命" />	
		</levelData>
	    <levelData level="6">
			<data att="addRegHpPercent"  value="0.06" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu5" />
			<data att="description2" value="每秒恢复自身6%生命" />	
		</levelData>
	    <levelData level="7">
			<data att="addRegHpPercent"  value="0.07" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu6" />
			<data att="description2" value="每秒恢复自身7%生命" />	
		</levelData>
	    <levelData level="8">
			<data att="addRegHpPercent"  value="0.08" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu7" />
			<data att="description2" value="每秒恢复自身8%生命" />	
		</levelData>
	    <levelData level="9">
			<data att="addRegHpPercent"  value="0.09" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu8" />
			<data att="description2" value="每秒恢复自身9%生命" />	
		</levelData>
	    <levelData level="10">
			<data att="addRegHpPercent"  value="0.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZiYu9" />
			<data att="description2" value="每秒恢复自身10%生命" />	
		</levelData>

	
	</skill>
	<skill skillId="pS8" name="凝气"   description="凝气，每秒恢复百分比魔法。" className="YJFY.AutomaticPet.PassiveSkillVO.AddRegMpSkill">
	    <levelData level="1">
			<data att="addRegMpPercent"  value="0.01" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi" />
			<data att="description2" value="每秒恢复自身1%魔法" />				
		</levelData>
	    <levelData level="2">
			<data att="addRegMpPercent"  value="0.02" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi1" />
			<data att="description2" value="每秒恢复自身2%魔法" />				
		</levelData>
	    <levelData level="3">
			<data att="addRegMpPercent"  value="0.03" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi2" />
			<data att="description2" value="每秒恢复自身3%魔法" />				
		</levelData>
	    <levelData level="4">
			<data att="addRegMpPercent"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi3" />
			<data att="description2" value="每秒恢复自身4%魔法" />				
		</levelData>
	    <levelData level="5">
			<data att="addRegMpPercent"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi4" />
			<data att="description2" value="每秒恢复自身5%魔法" />				
		</levelData>
	    <levelData level="6">
			<data att="addRegMpPercent"  value="0.06" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi5" />
			<data att="description2" value="每秒恢复自身6%魔法" />				
		</levelData>
	    <levelData level="7">
			<data att="addRegMpPercent"  value="0.07" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi6" />
			<data att="description2" value="每秒恢复自身7%魔法" />				
		</levelData>
	    <levelData level="8">
			<data att="addRegMpPercent"  value="0.08" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi7" />
			<data att="description2" value="每秒恢复自身8%魔法" />				
		</levelData>
	    <levelData level="9">
			<data att="addRegMpPercent"  value="0.09" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi8" />
			<data att="description2" value="每秒恢复自身9%魔法" />				
		</levelData>
	    <levelData level="10">
			<data att="addRegMpPercent"  value="0.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_NingQi9" />
			<data att="description2" value="每秒恢复自身10%魔法" />				
		</levelData>
	
	</skill>
	<skill skillId="pS9" name="法力高深"   description="法力高深，减少妖将技能冷却。" className="YJFY.AutomaticPet.PassiveSkillVO.DecCdTimeSkill">
	    <levelData level="1">
			<data att="decCDTimeNum"  value="-1000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FaLiGaoShen" />
			<data att="description2" value="所有技能冷却减少1秒" />	
		</levelData>
	    <levelData level="2">
			<data att="decCDTimeNum"  value="-2000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FaLiGaoShen1" />
			<data att="description2" value="所有技能冷却减少2秒" />	
		</levelData>
	    <levelData level="3">
			<data att="decCDTimeNum"  value="-3000" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_FaLiGaoShen2" />
			<data att="description2" value="所有技能冷却减少3秒" />	
		</levelData>

	
	</skill>
	<skill skillId="pS10" name="钢筋铁骨"   description="钢筋铁骨，大量提升妖将生命。" className="YJFY.AutomaticPet.PassiveSkillVO.AddBloodSkill">
	    <levelData level="1">
			<data att="addHpPercent"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu" />
			<data att="description2" value="提升妖将当前生命的5%" />	
		</levelData>
	    <levelData level="2">
			<data att="addHpPercent"  value="0.07" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu1" />
			<data att="description2" value="提升妖将当前生命的7%" />	
		</levelData>
	    <levelData level="3">
			<data att="addHpPercent"  value="0.09" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu2" />
			<data att="description2" value="提升妖将当前生命的9%" />	
		</levelData>
	    <levelData level="4">
			<data att="addHpPercent"  value="0.11" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu3" />
			<data att="description2" value="提升妖将当前生命的11%" />	
		</levelData>
	    <levelData level="5">
			<data att="addHpPercent"  value="0.13" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu4" />
			<data att="description2" value="提升妖将当前生命的13%" />	
		</levelData>
	    <levelData level="6">
			<data att="addHpPercent"  value="0.15" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu5" />
			<data att="description2" value="提升妖将当前生命的15%" />	
		</levelData>
	    <levelData level="7">
			<data att="addHpPercent"  value="0.17" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu6" />
			<data att="description2" value="提升妖将当前生命的17%" />	
		</levelData>
	    <levelData level="8">
			<data att="addHpPercent"  value="0.19" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu7" />
			<data att="description2" value="提升妖将当前生命的19%" />	
		</levelData>
	    <levelData level="9">
			<data att="addHpPercent"  value="0.21" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu8" />
			<data att="description2" value="提升妖将当前生命的21%" />	
		</levelData>
	    <levelData level="10">
			<data att="addHpPercent"  value="0.23" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_GangJinTieGu9" />
			<data att="description2" value="提升妖将当前生命的23%" />	
		</levelData>
	
	</skill>
	<skill skillId="pS11" name="铜墙铁壁"   description="铜墙铁壁，大量提升妖将防御。" className="YJFY.AutomaticPet.PassiveSkillVO.AddDefenceSkill">
	    <levelData level="1">
			<data att="addDefencePercent"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi" />
			<data att="description2" value="提升妖将当前防御的5%" />				
		</levelData>
	    <levelData level="2">
			<data att="addDefencePercent"  value="0.07" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi1" />
			<data att="description2" value="提升妖将当前防御的7%" />				
		</levelData>
	    <levelData level="3">
			<data att="addDefencePercent"  value="0.09" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi2" />
			<data att="description2" value="提升妖将当前防御的9%" />				
		</levelData>
	    <levelData level="4">
			<data att="addDefencePercent"  value="0.11" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi3" />
			<data att="description2" value="提升妖将当前防御的11%" />				
		</levelData>
	    <levelData level="5">
			<data att="addDefencePercent"  value="0.13" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi4" />
			<data att="description2" value="提升妖将当前防御的13%" />				
		</levelData>
	    <levelData level="6">
			<data att="addDefencePercent"  value="0.15" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi5" />
			<data att="description2" value="提升妖将当前防御的15%" />				
		</levelData>
	    <levelData level="7">
			<data att="addDefencePercent"  value="0.17" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi6" />
			<data att="description2" value="提升妖将当前防御的17%" />				
		</levelData>
	    <levelData level="8">
			<data att="addDefencePercent"  value="0.19" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi7" />
			<data att="description2" value="提升妖将当前防御的19%" />				
		</levelData>
	    <levelData level="9">
			<data att="addDefencePercent"  value="0.21" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi8" />
			<data att="description2" value="提升妖将当前防御的21%" />				
		</levelData>
	    <levelData level="10">
			<data att="addDefencePercent"  value="0.23" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_TongQiangTieBi9" />
			<data att="description2" value="提升妖将当前防御的23%" />				
		</levelData>
	
	</skill>
	<skill skillId="pS12" name="无坚不摧"   description="无坚不摧，大量提升妖将攻击力。" className="YJFY.AutomaticPet.PassiveSkillVO.AddAttackSkill">
	    <levelData level="1">
			<data att="addAttackPercent"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui" />
			<data att="description2" value="提升妖将当前攻击的5%" />					
		</levelData>
	    <levelData level="2">
			<data att="addAttackPercent"  value="0.07" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui1" />
			<data att="description2" value="提升妖将当前攻击的7%" />					
		</levelData>
	    <levelData level="3">
			<data att="addAttackPercent"  value="0.09" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui2" />
			<data att="description2" value="提升妖将当前攻击的9%" />					
		</levelData>
	    <levelData level="4">
			<data att="addAttackPercent"  value="0.11" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui3" />
			<data att="description2" value="提升妖将当前攻击的11%" />					
		</levelData>
	    <levelData level="5">
			<data att="addAttackPercent"  value="0.13" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui4" />
			<data att="description2" value="提升妖将当前攻击的13%" />					
		</levelData>
	    <levelData level="6">
			<data att="addAttackPercent"  value="0.15" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui5" />
			<data att="description2" value="提升妖将当前攻击的15%" />					
		</levelData>
	    <levelData level="7">
			<data att="addAttackPercent"  value="0.17" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui6" />
			<data att="description2" value="提升妖将当前攻击的17%" />					
		</levelData>
	    <levelData level="8">
			<data att="addAttackPercent"  value="0.19" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui7" />
			<data att="description2" value="提升妖将当前攻击的19%" />					
		</levelData>
	    <levelData level="9">
			<data att="addAttackPercent"  value="0.21" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui8" />
			<data att="description2" value="提升妖将当前攻击的21%" />					
		</levelData>
	    <levelData level="10">
			<data att="addAttackPercent"  value="0.23" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_WuJianBuCui9" />
			<data att="description2" value="提升妖将当前攻击的23%" />					
		</levelData>
	
	</skill>
	
<!-- 	辅助技能 -->
	<skill skillId="AuxiliarySkill1" name="震撼"   description="人物的技能攻击将有一定几率晕眩敌人。" className="YJFY.AutomaticPet.AssistSkillVO.AuxiliaryZhengHanSkill">
	    <levelData level="1">
			<data att="addPlayerVertigo"  value="0.03" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan" />
			<data att="description2" value="人物技能有12%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="2">
			<data att="addPlayerVertigo"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan1" />
			<data att="description2" value="人物技能有14%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="3">
			<data att="addPlayerVertigo"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan2" />
			<data att="description2" value="人物技能有16%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="4">
			<data att="addPlayerVertigo"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan3" />
			<data att="description2" value="人物技能有18%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="5">
			<data att="addPlayerVertigo"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan4" />
			<data att="description2" value="人物技能有20%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="6">
			<data att="addPlayerVertigo"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan5" />
			<data att="description2" value="人物技能有22%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="7">
			<data att="addPlayerVertigo"  value="0.06" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan6" />
			<data att="description2" value="人物技能有24%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="8">
			<data att="addPlayerVertigo"  value="0.06" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan7" />
			<data att="description2" value="人物技能有26%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="9">
			<data att="addPlayerVertigo"  value="0.07" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan8" />
			<data att="description2" value="人物技能有28%的几率晕眩敌人" />					
		</levelData>
	    <levelData level="10">
			<data att="addPlayerVertigo"  value="0.08" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_ZhengHan9" />
			<data att="description2" value="人物技能有30%的几率晕眩敌人" />					
		</levelData>
	</skill>
	<skill skillId="AuxiliarySkill2" name="护佑"   description="在角色血量较低时，妖将发动护佑技能，为人物施加无敌护盾。" className="YJFY.AutomaticPet.AssistSkillVO.AuxiliaryBaoYouSkill">
	    <levelData level="1">
			<data att="addPlayerInvincible"  value="9" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续9秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="2">
			<data att="addPlayerInvincible"  value="10" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun1" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续10秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="3">
			<data att="addPlayerInvincible"  value="11" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun2" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续11秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="4">
			<data att="addPlayerInvincible"  value="12" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun3" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续12秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="5">
			<data att="addPlayerInvincible"  value="13" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun4" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续13秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="6">
			<data att="addPlayerInvincible"  value="14" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun5" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续14秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="7">
			<data att="addPlayerInvincible"  value="15" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun6" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续15秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="8">
			<data att="addPlayerInvincible"  value="16" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun7" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续16秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="9">
			<data att="addPlayerInvincible"  value="17" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun8" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续17秒的无敌护盾，cd时间60秒" />					
		</levelData>
	    <levelData level="10">
			<data att="addPlayerInvincible"  value="18" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shengdun9" />
			<data att="description2" value="当人物血量低于百分之25时将获得持续18秒的无敌护盾，cd时间60秒"/>					
		</levelData>
	</skill>
	
		<skill skillId="AuxiliarySkill3" name="击破"   description="人物的技能攻击将有一定几率击碎怪物无敌护盾。" className="YJFY.AutomaticPet.AssistSkillVO.AuxiliaryJiSuiDunPaiSkill">
	    <levelData level="1">
			<data att="addPlayerCrushed"  value="0.03" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo" />
			<data att="description2" value="人物技能有12%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="2">
			<data att="addPlayerCrushed"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo1" />
			<data att="description2" value="人物技能有14%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="3">
			<data att="addPlayerCrushed"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo2" />
			<data att="description2" value="人物技能有16%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="4">
			<data att="addPlayerCrushed"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo3" />
			<data att="description2" value="人物技能有18%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="5">
			<data att="addPlayerCrushed"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo4" />
			<data att="description2" value="人物技能有20%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="6">
			<data att="addPlayerCrushed"  value="0.06" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo5" />
			<data att="description2" value="人物技能有22%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="7">
			<data att="addPlayerCrushed"  value="0.07" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo6" />
			<data att="description2" value="人物技能有24%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="8">
			<data att="addPlayerCrushed"  value="0.08" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo7" />
			<data att="description2" value="人物技能有26%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="9">
			<data att="addPlayerCrushed"  value="0.09" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo8" />
			<data att="description2" value="人物技能有28%的击碎无敌护盾" />					
		</levelData>
	    <levelData level="10">
			<data att="addPlayerCrushed"  value="0.10" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Jipo9" />
			<data att="description2" value="人物技能有30%的击碎无敌护盾" />					
		</levelData>
	</skill>
	<skill skillId="AuxiliarySkill4" name="嗜血"   description="人物降到一定血量时将获得吸血能力。" className="YJFY.AutomaticPet.AssistSkillVO.AuxiliaryVampireSkill">
	    <levelData level="1">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="2" />    <!-- 持续时间 -->  
			<data att="addPlayerVampireBloodLimit"  value="0.04" />   <!--  血量下线--> 
			<data att="addPlayerVampirePercent"  value="0.01" />   <!--  百分比--> 
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue" />
			<data att="description2" value="当人物血量降低到4%的时候获得持续2秒的吸血效果，吸血比例为造成伤害的百分之1%" />					
		</levelData>
	    <levelData level="2">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="4" />
			<data att="addPlayerVampireBloodLimit"  value="0.06" />
			<data att="addPlayerVampirePercent"  value="0.02" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue1" />
			<data att="description2" value="当人物血量降低到6%的时候获得持续4秒的吸血效果，吸血比例为造成伤害的百分之2%" />					
		</levelData>
	    <levelData level="3">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="6" />
			<data att="addPlayerVampireBloodLimit"  value="0.08" />
			<data att="addPlayerVampirePercent"  value="0.03" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue2" />
			<data att="description2" value="当人物血量降低到8%的时候获得持续6秒的吸血效果，吸血比例为造成伤害的百分之3%" />					
		</levelData>
	    <levelData level="4">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="8" />
			<data att="addPlayerVampireBloodLimit"  value="0.1" />
			<data att="addPlayerVampirePercent"  value="0.04" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue3" />
			<data att="description2" value="当人物血量降低到10%的时候获得持续8秒的吸血效果，吸血比例为造成伤害的百分之4%" />					
		</levelData>
	    <levelData level="5">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="10" />
			<data att="addPlayerVampireBloodLimit"  value="0.12" />
			<data att="addPlayerVampirePercent"  value="0.05" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue4" />
			<data att="description2" value="当人物血量降低到12%的时候获得持续10秒的吸血效果，吸血比例为造成伤害的百分之5%" />					
		</levelData>
	    <levelData level="6">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="12" />
			<data att="addPlayerVampireBloodLimit"  value="0.14" />
			<data att="addPlayerVampirePercent"  value="0.06" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue5" />
			<data att="description2" value="当人物血量降低到14%的时候获得持续12秒的吸血效果，吸血比例为造成伤害的百分之6%" />					
		</levelData>
	    <levelData level="7">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="14" />
			<data att="addPlayerVampireBloodLimit"  value="0.18" />
			<data att="addPlayerVampirePercent"  value="0.07" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue6" />
			<data att="description2" value="当人物血量降低到18%的时候获得持续14秒的吸血效果，吸血比例为造成伤害的百分之7%" />					
		</levelData>
	    <levelData level="8">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="16" />
			<data att="addPlayerVampireBloodLimit"  value="0.22" />
			<data att="addPlayerVampirePercent"  value="0.08" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue7" />
			<data att="description2" value="当人物血量降低到22%的时候获得持续16秒的吸血效果，吸血比例为造成伤害的百分之8%" />					
		</levelData>
	    <levelData level="9">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="18" />
			<data att="addPlayerVampireBloodLimit"  value="0.26" />
			<data att="addPlayerVampirePercent"  value="0.09" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue8" />
			<data att="description2" value="当人物血量降低到26%的时候获得持续18秒的吸血效果，吸血比例为造成伤害的百分之9%" />					
		</levelData>
	    <levelData level="10">
			<data att="addPlayerVampireCD"  value="60" />
			<data att="addPlayerVampireDuration"  value="20" />
			<data att="addPlayerVampireBloodLimit"  value="0.3" />
			<data att="addPlayerVampirePercent"  value="0.1" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Shixue9" />
			<data att="description2" value="当人物血量降低到30%的时候获得持续20秒的吸血效果，吸血比例为造成伤害的百分之10%" />					
		</levelData>
	</skill>

	<skill skillId="AuxiliarySkill5" name="重生"   description="人物阵亡时有概率原地复活。" className="YJFY.AutomaticPet.AssistSkillVO.AuxiliaryRebornSkill">
	    <levelData level="1">
			<data att="addPlayerRebornOdds"  value="0.25" />     <!-- 复活概率 --> 
			<data att="addPlayerRebornPercent"  value="0.1" />  <!--  血量--> 
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng" />
			<data att="description2" value="当人物死亡时有25%的概率复活，复活后将恢复人物10%的生命值" />					
		</levelData>
	    <levelData level="2">
			<data att="addPlayerRebornOdds"  value="0.30" />
			<data att="addPlayerRebornPercent"  value="0.2" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng1" />
			<data att="description2" value="当人物死亡时有30%的概率复活，复活后将恢复人物20%的生命值" />					
		</levelData>
	    <levelData level="3">
			<data att="addPlayerRebornOdds"  value="0.35" />
			<data att="addPlayerRebornPercent"  value="0.3" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng2" />
			<data att="description2" value="当人物死亡时有35%的概率复活，复活后将恢复人物30%的生命值" />					
		</levelData>
	    <levelData level="4">
			<data att="addPlayerRebornOdds"  value="0.40" />
			<data att="addPlayerRebornPercent"  value="0.4" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng3" />
			<data att="description2" value="当人物死亡时有40%的概率复活，复活后将恢复人物40%的生命值" />					
		</levelData>
	    <levelData level="5">
			<data att="addPlayerRebornOdds"  value="0.45" />
			<data att="addPlayerRebornPercent"  value="0.5" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng4" />
			<data att="description2" value="当人物死亡时有45%的概率复活，复活后将恢复人物50%的生命值" />					
		</levelData>
	    <levelData level="6">
			<data att="addPlayerRebornOdds"  value="0.5" />
			<data att="addPlayerRebornPercent"  value="0.6" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng5" />
			<data att="description2" value="当人物死亡时有50%的概率复活，复活后将恢复人物60%的生命值" />					
		</levelData>
	    <levelData level="7">
			<data att="addPlayerRebornOdds"  value="0.55" />
			<data att="addPlayerRebornPercent"  value="0.7" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng6" />
			<data att="description2" value="当人物死亡时有55%的概率复活，复活后将恢复人物70%的生命值" />					
		</levelData>
	    <levelData level="8">
			<data att="addPlayerRebornOdds"  value="0.6" />
			<data att="addPlayerRebornPercent"  value="0.8" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng7" />
			<data att="description2" value="当人物死亡时有60%的概率复活，复活后将恢复人物80%的生命值" />					
		</levelData>
	    <levelData level="9">
			<data att="addPlayerRebornOdds"  value="0.65" />
			<data att="addPlayerRebornPercent"  value="0.9" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng8" />
			<data att="description2" value="当人物死亡时有65%的概率复活，复活后将恢复人物90%的生命值" />					
		</levelData>
	    <levelData level="10">
			<data att="addPlayerRebornOdds"  value="0.7" />
			<data att="addPlayerRebornPercent"  value="1" />
			<data att="addPlayerRebornCD"  value="100" />
			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" />
			<data att="iconClassName" value="PetSkill_Chongsheng9" />
			<data att="description2" value="当人物死亡时有70%的概率复活，复活后将恢复人物100%的生命值" />					
		</levelData>
	</skill>
<!-- 	<skill skillId="AuxiliarySkill3" name="暴怒"   description="哪吒短时间内收到大量伤害后会激发暴怒形态，暴怒形态下哪吒攻击力提升1.5倍，并无视怪物攻击。" className="YJFY.AutomaticPet.AssistSkillVO.AuxiliaryZhengHanSkill"> -->
<!-- 	    <levelData level="1"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.03" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续8秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="2"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.04" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu1" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续9秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="3"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.04" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu2" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续10秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="4"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.04" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu3" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续11秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="5"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.05" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu4" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续12秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="6"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.05" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu5" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续13秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="7"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.06" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu6" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续14秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="8"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.06" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu7" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续15秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="9"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.07" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu8" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续16秒" />					 -->
<!-- 		</levelData> -->
<!-- 	    <levelData level="10"> -->
<!-- 			<data att="addPlayerVertigo"  value="0.08" /> -->
<!-- 			<data att="iconSwfPath" value="UISprite2/AutomaticPetSkill.swf" /> -->
<!-- 			<data att="iconClassName" value="PetSkill_Baonu9" /> -->
<!-- 			<data att="description2" value="哪吒暴怒时间持续17秒" />					 -->
<!-- 		</levelData> -->
<!-- 	</skill> -->
</data>



