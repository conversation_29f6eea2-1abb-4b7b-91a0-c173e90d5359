<?xml version="1.0" encoding="utf-8" ?>
<data>
	<taskGoal id="taskGoalTest1" name="测试任务目标" gameEventStr="player1_50_player2_50" />






	<taskGoal id="taskGoal1" name="双叉岭僵尸" gameEventStr="enemy1" />
	<taskGoal id="taskGoal2" name="黑风洞僵尸" gameEventStr="boss1" />
	<taskGoal id="taskGoal3" name="任务目标1" gameEventStr="enemy2" />
	<taskGoal id="taskGoal4" name="任务目标1" gameEventStr="boss2" />
	<taskGoal id="taskGoal5" name="任务目标1" gameEventStr="enemy3" />
	<taskGoal id="taskGoal6" name="任务目标1" gameEventStr="boss3" />
	<taskGoal id="taskGoal7" name="任务目标1" gameEventStr="enemy4" />
	<taskGoal id="taskGoal8" name="任务目标1" gameEventStr="boss4" />
	<taskGoal id="taskGoal9" name="任务目标1" gameEventStr="enemy5" />
	<taskGoal id="taskGoal10" name="任务目标1" gameEventStr="boss5" />
	<taskGoal id="taskGoal11" name="任务目标1" gameEventStr="enemy6" />
	<taskGoal id="taskGoal12" name="任务目标1" gameEventStr="boss6" />
	<taskGoal id="taskGoal13" name="任务目标1" gameEventStr="enemy7" />
	<taskGoal id="taskGoal14" name="任务目标1" gameEventStr="boss7" />
	<taskGoal id="taskGoal15" name="任务目标1" gameEventStr="enemy8" />
	<taskGoal id="taskGoal16" name="任务目标1" gameEventStr="boss8" />
	<taskGoal id="taskGoal17" name="任务目标1" gameEventStr="enemy9" />
	<taskGoal id="taskGoal18" name="任务目标1" gameEventStr="boss9" />
	<taskGoal id="taskGoal19" name="任务目标1" gameEventStr="enemy10" />
	<taskGoal id="taskGoal20" name="任务目标1" gameEventStr="boss10" />
	<taskGoal id="taskGoal21" name="任务目标1" gameEventStr="enemy11" />
	<taskGoal id="taskGoal22" name="任务目标1" gameEventStr="boss11" />
	<taskGoal id="taskGoal23" name="任务目标1" gameEventStr="enemy12" />
	<taskGoal id="taskGoal24" name="任务目标1" gameEventStr="boss12" />
	<taskGoal id="taskGoal25" name="任务目标1" gameEventStr="enemy13" />
	<taskGoal id="taskGoal26" name="任务目标1" gameEventStr="boss13" />
	<taskGoal id="taskGoal27" name="任务目标1" gameEventStr="enemy14" />
	<taskGoal id="taskGoal28" name="任务目标1" gameEventStr="boss14" />
	<taskGoal id="taskGoal29" name="任务目标1" gameEventStr="enemy15" />
	<taskGoal id="taskGoal30" name="任务目标1" gameEventStr="boss15" />
	<taskGoal id="taskGoal31" name="任务目标1" gameEventStr="enemy16" />
	<taskGoal id="taskGoal32" name="任务目标1" gameEventStr="boss16" />
	<taskGoal id="taskGoal33" name="任务目标1" gameEventStr="enemy17" />
	<taskGoal id="taskGoal34" name="任务目标1" gameEventStr="boss17" />
	<taskGoal id="taskGoal35" name="任务目标1" gameEventStr="enemy18" />
	<taskGoal id="taskGoal36" name="任务目标1" gameEventStr="boss18" />
	<taskGoal id="taskGoal37" name="任务目标1" gameEventStr="enemy19" />
	<taskGoal id="taskGoal38" name="任务目标1" gameEventStr="boss19" />


	<!--铁匠功能 -->
	<taskGoal id="taskGoal39" name="打造装备" gameEventStr="EquipmentMake" />
	<taskGoal id="taskGoal40" name="升级装备" gameEventStr="EquipmentUpgrade" />
	<taskGoal id="taskGoal41" name="升级装备到一定等级" gameEventStr="EquipmentUpgradeTo6" />
	<taskGoal id="taskGoal42" name="开孔装备" gameEventStr="openHole" />
	<taskGoal id="taskGoal50" name="升级装备到一定等级" gameEventStr="EquipmentUpgradeTo9" />

	<!--宠物功能 -->
	<taskGoal id="taskGoal43" name="普通孵化" gameEventStr="HatchPet_normal" />
	<taskGoal id="taskGoal44" name="精致孵化" gameEventStr="HatchPet_super" />
	<taskGoal id="taskGoal45" name="宠物幻化" gameEventStr="mirage" />
	<taskGoal id="taskGoal46" name="宠物超进化" gameEventStr="petAdvance" />
	<!--其他功能 -->
	<taskGoal id="taskGoal47" name="签到" gameEventStr="sign" />
	<taskGoal id="taskGoal48" name="在线礼包" gameEventStr="getOnLineGiftBag_3" />
	<taskGoal id="taskGoal49" name="在线礼包" gameEventStr="getOnLineGiftBag_4" />
	<!--状态性任务 -->
	<taskGoal id="taskGoal50" name="人物等级" gameEventStr="player1_60_player2_60" />
	<taskGoal id="taskGoal51" name="PK胜场300" gameEventStr="PKOneWinNum_300" />
	<taskGoal id="taskGoal52" name="人物攻击" gameEventStr="playerAttack_1500" />
	<taskGoal id="taskGoal53" name="装备加9数量" gameEventStr="haveOneEq_Level9_1" />
	<taskGoal id="taskGoal54" name="徒弟等级" gameEventStr="tuDiOne_5" />


	<!--活跃任务 -->
	<taskGoal id="activeTaskGoal1" name="完成1次日常任务" gameEventStr="completeEveryDayTask" />
	<taskGoal id="activeTaskGoal2" name="完成1次农场种植" gameEventStr="plantOneLand" />
	<taskGoal id="activeTaskGoal3" name="完成1次世界BOSS" gameEventStr="fightWorldBoss" />
	<taskGoal id="activeTaskGoal4" name="完成1次PK" gameEventStr="pk" />
	<taskGoal id="activeTaskGoal5" name="完成1次任意关卡" gameEventStr="passLevelOfLevelMode1" />
	<taskGoal id="activeTaskGoal6" name="点券购买1次任意物品" gameEventStr="ticketPointBuyQuipment" />
	<taskGoal id="activeTaskGoal7" name="完成1次天宫BOSS" gameEventStr="completeDieBoss" />


</data>