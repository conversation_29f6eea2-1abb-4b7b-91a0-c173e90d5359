<?xml version="1.0" encoding="utf-8" ?>
<!--移动速度以秒为单位-->
<Animal id="tangSeng1"  animalType="YJFY.Entity.TangMonkEntity.TangMonkEntity"  bodyWidth="60" bodyHeight="80"> 
	 
	  
	  
	  
	  
	  
	  <idle defId="idle_tangSeng1" />
	  <walk defId="walk_tangSeng1" />
	  <run defId="run_tangSeng1" />
	  <attack defId="attack_tangSeng1" />
	  <hurt defId="hurt_tangSeng1" soundId="beAttackSound1">
		  <attackSourceData entityId="" skillId="" />
	  </hurt>
	  
	  <die defId="die_tangSeng1" >
		  <attackSourceData entityId="" skillId="" />
	  </die>
	 
	  
	  
	 
	  <animationDefinitions>
		   <animationDefinition  id="idle_tangSeng1" rows="1" cols="1" walkable="false" overlap="false"  frameInterval="3"  
		    defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
             <show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" showClass="TangSengStand" x_offset="0" y_offset="0" />
	     </animationDefinition>
		 <animationDefinition  id="walk_tangSeng1" rows="1" cols="1" walkable="false" overlap="false"  frameInterval="3"  
		    defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
             <show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" showClass="TangSengStand" x_offset="0" y_offset="0" />
	     </animationDefinition>
		 <animationDefinition  id="run_tangSeng1" rows="1" cols="1" walkable="false" overlap="false"  frameInterval="3" 
		    defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
             <show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" showClass="TangSengStand" x_offset="0" y_offset="0" />
	     </animationDefinition>
		 <animationDefinition  id="hurt_tangSeng1" rows="1" cols="1" walkable="false" overlap="false"  frameInterval="10"  
		    defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
             <show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" showClass="TangSengStand" x_offset="0" y_offset="0" />
	     </animationDefinition>
		 <animationDefinition  id="attack_tangSeng1" rows="1" cols="1" walkable="false" overlap="false"  frameInterval="3"
		    defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
            <show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" showClass="TangSengStand" x_offset="0" y_offset="0" />
	     </animationDefinition>
		 <animationDefinition  id="die_tangSeng1" rows="1" cols="1" walkable="false" overlap="false"  frameInterval="3"
		    defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
            <show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" showClass="TangSengStand" x_offset="0" y_offset="0" />
	     </animationDefinition>
	  </animationDefinitions>
	  
	  
	  
</Animal>