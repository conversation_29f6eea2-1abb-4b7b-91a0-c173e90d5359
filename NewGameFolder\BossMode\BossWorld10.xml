<?xml version="1.0" encoding="utf-8" ?>
<data id="boss10" swfPath="NewGameFolder/BossMode/Scene1.swf"
	className="BossModeMap1" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound5.swf" className="SoundCC" />	

    <startShow swfPath="NewGameFolder/BossMode/Boss10.swf" className="CBossStoryShow" />
    <bossHead swfPath="NewGameFolder/BossMode/Boss10.swf" className="CBossHead" />
    <!--抽奖配置-->
	<lottery>
		<!-- 只能12个-->
		<!-- 龙王契约碎片 -->
		<item id="10500063" num="2" proWeight="3" />
		<!-- 鲛人将军契约碎片 -->
		<item id="10500062" num="2" proWeight="10" />
		<!-- 妖将契约（虾兵） -->
        <item id="12000000" num="2" proWeight="20" />
        <!-- 妖将契约（龟丞相 ）-->
        <item id="12000001" num="2" proWeight="20" />
        <!-- 圣灵精华 -->
        <item id="10500035" num="2" proWeight="1" />
        <!-- 白虎石 -->
        <item id="10500036" num="2" proWeight="5" />
        <!-- 青龙石 -->
        <item id="10500037" num="2" proWeight="5" />
        <!-- 玄武石 -->
        <item id="10500038" num="2" proWeight="5" />
        <!-- 朱雀石 -->
        <item id="10500039" num="2" proWeight="5" />
       <!-- 进阶丹 -->
        <item id="10500064" num="1" proWeight="5" />
        <!-- 内丹-->
        <item id="10500065" num="3" proWeight="3" />
        <!-- 神秘圣诞宠物蛋-->
        <item id="10500067" num="1" proWeight="1" />
	</lottery>
	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
	
   <boss type="YJFY.BossMode.Boss1.Boss1">
	<!--敌人数据 -->
	<!--技能伤害hurtMulti=X倍数*attack-->
   <bossData hpSegment="10000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_BossTeleport" hurtMulti="0" costMp="20" cdTime="5000" priorityForRun="0" priorityForRunInHurt="1"  
	      isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="Skill_BossSkill1" hurtMulti="0" costMp="20" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
	   isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="Skill_BossDaZhao" hurtMulti="1" costMp="30" cdTime="3000" priorityForRun="2" priorityForRunInHurt="0"  
	  isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO"/> <!--cdTime 毫秒-->
		
		</bossData>
		
    <!--BOSS属性-->		
	<enemyData>
		<data att="totalHp" value="300000" />
		<data att="attack" value="2200" />
		<data att="expOfDieThisEnemy" value="1200000" />
		<data att="defence" value="200" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="1" />
		<data att="criticalMuti" value="1" />
		<data att="deCriticalRate" value="0.8" />
		<data att="hitRate" value="0.05" />
		
		
		<data att="totalMp" value="200" />
		
     <!--回血回魔-->	
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="5" />
	</enemyData>
	
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="80" bodyHeight="120" walkSpeed="60"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="-20" y="-10" z="-1" xRange="200" yRange="20" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
			
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" x="0"
			y="-15" z="-1" xRange="350" yRange="30" zRange="100" bodyDefId="skill1Animation"  hurtDuration="1000"  
			skillAttackEffectDefId="bossSkill1AttackEffect" randomPlaceXRange="10" randomPlaceYRange="-60" attackInterval="400"
			skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^" >
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
		</skill>
		<skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao" x="-200"
			y="-100" z="-1" xRange="400" yRange="200" zRange="100" bodyDefId="skill3Animation"  hurtDuration="5000" 
			bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^" effectAddtoTargetId="yunQuan"
			everyEntityAddShowIsFrontOfBody="0">
			<shakeView swfPath="NewGameFolder/BossMode/Boss10.swf" className="ShakeView" />
		    <animationDefinition id="yunQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/BossMode/Boss10.swf" 
				showClass="CBossRun" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="CBossBeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="CBossDead" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill3Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="CBossSkill3Show" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="DisappearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="AppearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="CBossChallengeBossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
			<animationDefinition id="yunQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss>
</data>
