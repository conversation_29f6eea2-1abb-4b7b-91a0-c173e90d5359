<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet2" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet2Idle" />
		<walk defId="pet2Walk" />
		<run defId="pet2Run" />
		<attack defId="pet2Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet2Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet2Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
		<skill id="Skill_Pet2Skill" className="YJFY.Skill.PetSkills.Skill_Pet2Skill" x="0"
			y="-30" z="-1" xRange="200" yRange="60" zRange="100" bodyDefId="pet2SkillBodyShow"
			skillShowDefId="pet2SkillFire" skillShowIsFrontOfBody="1">
			<frameLabels releaseFrameLabel="down" releaseFrameLabel2="releaseSkillFrame2" skillAttackReachFrameLabel="skillAttackReach" skillEndStartFrameLabel="skillEndStart" 
			    skillEndEndFrameLabel="skillShow^stop^" bodyEndStartFrameLabel="" />
			
				
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet2Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet2All.swf"
					showClass="PetStand2_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet2Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet2All.swf"
					showClass="PetWalk2_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet2SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet2All.swf"
					showClass="PetSkill2Attack_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet2SkillFrontShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet2All.swf"
					showClass="PetSkill2Effect_Word_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			<animationDefinition id="pet2SkillFire" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet2All.swf"
					showClass="PetSkill2Effect_Fire" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet2Idle" eqClassName="Pet_ShiTouRen_1"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetStand2_1"
				x_offset="0" y_offset="0" />
			<show defId="pet2Walk" eqClassName="Pet_ShiTouRen_1"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetWalk2_1"
				x_offset="0" y_offset="0" />
           <show defId="pet2SkillBodyShow" eqClassName="Pet_ShiTouRen_1"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetSkill2Attack_1"
				x_offset="0" y_offset="0" />
			<show defId="pet2SkillFrontShow" eqClassName="Pet_ShiTouRen_1"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetSkill2Effect_Word_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet2Idle" eqClassName="Pet_ShiTouRen_2"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetStand2_2"
				x_offset="0" y_offset="0" />
			<show defId="pet2Walk" eqClassName="Pet_ShiTouRen_2"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetWalk2_2"
				x_offset="0" y_offset="0" />
           <show defId="pet2SkillBodyShow" eqClassName="Pet_ShiTouRen_2"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetSkill2Attack_2"
				x_offset="0" y_offset="0" />
			<show defId="pet2SkillFrontShow" eqClassName="Pet_ShiTouRen_2"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetSkill2Effect_Word_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet2Idle" eqClassName="Pet_ShiTouRen_3"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetStand2_3"
				x_offset="0" y_offset="0" />
			<show defId="pet2Walk" eqClassName="Pet_ShiTouRen_3"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetWalk2_3"
				x_offset="0" y_offset="0" />
           <show defId="pet2SkillBodyShow" eqClassName="Pet_ShiTouRen_3"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetSkill2Attack_3"
				x_offset="0" y_offset="0" />
			<show defId="pet2SkillFrontShow" eqClassName="Pet_ShiTouRen_3"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetSkill2Effect_Word_3"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
