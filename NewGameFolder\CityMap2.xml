<?xml version="1.0" encoding="utf-8" ?>
<data swfPath="NewGameFolder/CityMap2.swf" className="CityMap2" x ="0" y="0" z="0" xRange="1920" yRange="400" zRange="100">
	<mainGameProjectLayerData  sx="0" sy="" sz="100"   name="middleMap">
		<npc name="itemDeliverNpcBtn"   />
		<npc name="linggefudibtn"  />
		<npc name="allPkNpcBtn"   />
		<!-- <npc name="petChallengeNpcBtn"  /> -->
		<npc name="bossChallengeNpcBtn"  />
		<npc name="worldBossBtn"  />
		<npc name="nextAreaChooseBtn2" isNextAreaDoor="1" />
		<npc name="levelChooseBtn2" isNextAreaDoor="1"/>
		<npc name="xiangMoFuBenBtn"  />
		<npc name="EndlessNpcBtn"  />
	</mainGameProjectLayerData>
	<!--背景-->
    <projectLayer sx="0" sy="2245" sz="500" name="backMap" />
	<projectLayer sx="0" sy="-220" sz="60"  name="frontMap" />
	<backgroundMusic id="cityMapMusic2" name="cityMapMusic2"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound2.swf" className="SoundTT" />
				
				
	<playerInitData player1InitPositionX="1700" player1InitPositionY="300" player1InitPositionZ="0" 
	player2InitPositionX="1650" player2InitPositionY="250" player2InitPositionZ="0" />
	
	
	
	 
  <sharedAnimationDefinitions>
        
  </sharedAnimationDefinitions>
</data>