<?xml version="1.0" encoding="utf-8" ?>
<data>
     <taskDescription id="taskDescription1" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="欢迎来到西游大战僵尸2的神秘世界，师傅被双叉岭的僵尸抓走啦，快去西游征途中的双叉岭消灭20只双叉岭僵尸完成拯救师傅的任务。" />
		 <completeConditionDescription value="欢迎来到西游大战僵尸2的神秘世界，师傅被双叉岭的僵尸抓走啦，快去西游征途中的双叉岭消灭20只双叉岭僵尸完成拯救师傅的任务。" />
		 <enemyName value="双叉岭僵尸" />
		 <mapName value="双叉岭" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level1/level.xml" />
	 </taskDescription>
     <taskDescription id="taskDescription2" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="双叉岭是寅将军的老巢，快去双叉岭中打败寅将军2次试试身手吧。" />
		 <completeConditionDescription value="双叉岭是寅将军的老巢，快去双叉岭中打败寅将军2次试试身手吧。" />
		 <enemyName value="寅将军" />
		 <mapName value="双叉岭" />

         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level1/level.xml"  />
	 </taskDescription>
     <taskDescription id="taskDescription3" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭30只黑风洞僵尸" />
		 <completeConditionDescription value="消灭30只黑风洞僵尸" />
		 <enemyName value="黑风洞僵尸" />
		 <mapName value="黑风洞" />

         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level2/level.xml"  />
	 </taskDescription>
	 
     <taskDescription id="taskDescription4" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="黑风洞是黑熊精的洞府，快去黑风洞中打败黑熊精2次证明自己的实力。" />
		 <completeConditionDescription value="黑风洞是黑熊精的洞府，快去黑风洞中打败黑熊精2次证明自己的实力。" />
		 <enemyName value="黑熊精" />
		 <mapName value="黑风洞" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level2/level.xml"  />
	 </taskDescription>
	 
     <taskDescription id="taskDescription5" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭40只通天河僵尸" />
		 <completeConditionDescription value="消灭40只通天河僵尸" />
		 <enemyName value="通天河僵尸。" />
		 <mapName value="通天河" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level3/level.xml"  />
	 </taskDescription>
	 
     <taskDescription id="taskDescription6" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="通天河中居住的千年魔鱼精，击败魔鱼精3次让他把师傅放了。" />
		 <completeConditionDescription value="通天河中居住的千年魔鱼精，击败魔鱼精3次让他把师傅放了。" />
		 <enemyName value="魔鱼精" />
		 <mapName value="通天河" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level3/level.xml"  />
	 </taskDescription>
	 
     <taskDescription id="taskDescription7" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭40只火云洞僵尸。" />
		 <completeConditionDescription value="消灭40只火云洞僵尸。" />
		 <enemyName value="火云洞僵尸" />
		 <mapName value="火云洞" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level4/level.xml" />
	 </taskDescription>
	 
     <taskDescription id="taskDescription8" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="住在火云洞里的红孩儿，自称“圣婴大王”，击败红孩儿3次让他改过自新。" />
		 <completeConditionDescription value="住在火云洞里的红孩儿，自称“圣婴大王”，击败红孩儿3次让他改过自新。" />
		 <enemyName value="火云洞僵尸" />
		 <mapName value="火云洞" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level4/level.xml"  />
	 </taskDescription>
     <taskDescription id="taskDescription9" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭40只白虎岭僵尸" />
		 <completeConditionDescription value="消灭40只白虎岭僵尸" />
		 <enemyName value="白虎岭僵尸" />
		 <mapName value="白虎岭" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level5/level.xml"  />
	 </taskDescription>
	 
	 
     <taskDescription id="taskDescription10" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="在白虎岭中的白骨精作恶多端，教训他3次让她悔过自身。" />
		 <completeConditionDescription value="在白虎岭中的白骨精作恶多端，教训他3次让她悔过自身。" />
		 <enemyName value="白骨精" />
		 <mapName value="白虎岭" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level5/level.xml"  />
 
 
	 </taskDescription>
	 
     <taskDescription id="taskDescription11" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭50只小银僵尸" />
		 <completeConditionDescription value="消灭50只小银僵尸" />
		 <enemyName value="小银僵尸" />
		 <mapName value="平顶山——银角" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level6/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription12" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="银角大王原是太上老君的炼丹童子，私下凡间占山为王，击败银角大王5次让他再也不敢私下凡间作恶。" />
		 <completeConditionDescription value="银角大王原是太上老君的炼丹童子，私下凡间占山为王，击败银角大王5次让他再也不敢私下凡间作恶。" />
		 <enemyName value="银角大王" />
		 <mapName value="平顶山——银角" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level6/level.xml"  />
 
 
	 </taskDescription>
	 
     <taskDescription id="taskDescription13" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭50只小金僵尸" />
		 <completeConditionDescription value="消灭50只小金僵尸" />
		 <enemyName value="小金僵尸" />
		 <mapName value="平顶山——金角" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level7/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription14" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="金角大王原是银角大王的哥哥，生性顽劣，击败金角大王5次让他回到太上老君的身边。" />
		 <completeConditionDescription value="金角大王原是银角大王的哥哥，生性顽劣，击败金角大王5次让他回到太上老君的身边。" />
		 <enemyName value="金角大王" />
		 <mapName value="平顶山——金角" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level7/level.xml"  />
 
 
	 </taskDescription>
	 
     <taskDescription id="taskDescription15" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭50只狮驼岭僵尸" />
		 <completeConditionDescription value="消灭50只狮驼岭僵尸" />
		 <enemyName value="狮驼岭僵尸" />
		 <mapName value="狮驼岭" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level8/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription16" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="狮驼岭的大大王老是下山危害百姓，让人不得安宁，去狮驼岭打败狮怪5次，让他消停消停。" />
		 <completeConditionDescription value="狮驼岭的大大王老是下山危害百姓，让人不得安宁，去狮驼岭打败狮怪5次，让他消停消停。" />
		 <enemyName value="狮怪" />
		 <mapName value="狮驼岭" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level8/level.xml"  />
 
 
	 </taskDescription>
	 
     <taskDescription id="taskDescription17" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭50只盘丝洞僵尸" />
		 <completeConditionDescription value="消灭50只盘丝洞僵尸" />
		 <enemyName value="盘丝洞僵尸" />
		 <mapName value="盘丝洞" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level9/level.xml" />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription18" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="盘丝洞的蜘蛛精经常使用法术迷惑过路行人，快去降了这个害人的妖精，击败蜘蛛精5次" />
		 <completeConditionDescription value="盘丝洞的蜘蛛精经常使用法术迷惑过路行人，快去降了这个害人的妖精，击败蜘蛛精5次" />
		 <enemyName value="蜘蛛精" />
		 <mapName value="盘丝洞" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level9/level.xml" />
 
 
	 </taskDescription>
	 
     <taskDescription id="taskDescription19" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭60只羊力僵尸" />
		 <completeConditionDescription value="消灭60只羊力僵尸" />
		 <enemyName value="羊力僵尸" />
		 <mapName value="车迟国——羊力" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level10/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription20" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="车迟国三国师之一，同两个师兄弟虎力大仙、鹿力大仙在车迟国为非作歹，快去与其斗法击败羊力大仙6次，替天行道！" />
		 <completeConditionDescription value="车迟国三国师之一，同两个师兄弟虎力大仙、鹿力大仙在车迟国为非作歹，快去与其斗法击败羊力大仙6次，替天行道！" />
		 <enemyName value="羊力大仙" />
		 <mapName value="车迟国——羊力" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level10/level.xml"  />
 
 
	 </taskDescription>
	 
     <taskDescription id="taskDescription21" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭70只鹿力僵尸" />
		 <completeConditionDescription value="消灭70只鹿力僵尸" />
		 <enemyName value="鹿力僵尸" />
		 <mapName value="车迟国——鹿力" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level11/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription22" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="西游记里的妖怪乃是一只白鹿修炼成精，车迟国三国师之一，打倒鹿力大仙3次，让他明白谁什么是真神！" />
		 <completeConditionDescription value="西游记里的妖怪乃是一只白鹿修炼成精，车迟国三国师之一，打倒鹿力大仙3次，让他明白谁什么是真神！" />
		 <enemyName value="鹿力大仙" />
		 <mapName value="车迟国——鹿力" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level11/level.xml" />
 
 
	 </taskDescription>
	 
     <taskDescription id="taskDescription23" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭80只虎力僵尸" />
		 <completeConditionDescription value="消灭80只虎力僵尸" />
		 <enemyName value="虎力僵尸" />
		 <mapName value="车迟国——虎力" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level12/level.xml" />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription24" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="车迟国虎力大仙，实力在三个国师之首，击败虎力大仙8次就可以通过车迟国。" />
		 <completeConditionDescription value="车迟国虎力大仙，实力在三个国师之首，击败虎力大仙8次就可以通过车迟国。" />
		 <enemyName value="虎力大仙" />
		 <mapName value="车迟国——虎力" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level12/level.xml" />
 
 
	 </taskDescription>
	 
     <taskDescription id="taskDescription25" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭100只火焰山僵尸" />
		 <completeConditionDescription value="消灭100只火焰山僵尸" />
		 <enemyName value="火焰山僵尸" />
		 <mapName value="火焰山" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level13/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription26" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="火焰山的牛魔王实力非凡，战胜他是高手的象征，快去战胜牛魔王20次，证明自己的实力！" />
		 <completeConditionDescription value="火焰山的牛魔王实力非凡，战胜他是高手的象征，快去战胜牛魔王20次，证明自己的实力！" />
		 <enemyName value="牛魔王" />
		 <mapName value="火焰山" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level13/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription27" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭150只花果山幽暗密林" />
		 <completeConditionDescription value="消灭150只花果山幽暗密林" />
		 <enemyName value="幽暗密林僵尸" />
		 <mapName value="花果山幽暗密林" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level14/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription28" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="花果山幽暗密林中的猿长老身经百战，花果山劫难之一，想要通过幽暗密林需要击败猿长老20次" />
		 <completeConditionDescription value="花果山幽暗密林中的猿长老身经百战，花果山劫难之一，想要通过幽暗密林需要击败猿长老20次" />
		 <enemyName value="猿长老" />
		 <mapName value="花果山幽暗密林" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level14/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription29" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭200只死亡密道僵尸" />
		 <completeConditionDescription value="消灭200只死亡密道僵尸" />
		 <enemyName value="死亡密道僵尸" />
		 <mapName value="花果山死亡密道" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level15/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription30" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="花果山死亡密道中暴怒猿将的装备齐全，很难找到防守破绽，并且力大无穷，花果山劫难之二，想要通过死亡密道需要击败暴怒猿将20次." />
		 <completeConditionDescription value="花果山死亡密道中暴怒猿将的装备齐全，很难找到防守破绽，并且力大无穷，花果山劫难之二，想要通过死亡密道需要击败暴怒猿将20次." />
		 <enemyName value="暴怒猿将" />
		 <mapName value="花果山死亡密道" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level15/level.xml"  />
 
 
	 </taskDescription>
	 <taskDescription id="taskDescription31" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭1000只决战之颠僵尸。" />
		 <completeConditionDescription value="消灭1000只决战之颠僵尸。" />
		 <enemyName value="决战之颠僵尸" />
		 <mapName value="花果山决战之颠" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level16/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription32" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="花果山决战之颠中六耳猕猴有着和齐天大圣一样的本事，上天入地、七十二变无所不能。花果山劫难之三，想要通过决战之颠需要击败六耳猕猴50次." />
		 <completeConditionDescription value="花果山决战之颠中六耳猕猴有着和齐天大圣一样的本事，上天入地、七十二变无所不能。花果山劫难之三，想要通过决战之颠需要击败六耳猕猴50次." />
		 <enemyName value="六耳猕猴" />
		 <mapName value="花果山决战之颠" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level16/level.xml"  />
 
 
	 </taskDescription>
	 <taskDescription id="taskDescription33" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭1200只大目鱼妖。" />
		 <completeConditionDescription value="消灭1200只大目鱼妖。" />
		 <enemyName value="大目鱼妖" />
		 <mapName value="远古海域" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level17/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription34" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="远古海域在长安城郊的最左方的元素禁地中，不过现在远古海域被一个妖怪封锁了，想要过去只有打败它。去远古海域，打败大目鱼妖头领60次，才能安全通过远古海域。" />
		 <completeConditionDescription value="远古海域在长安城郊的最左方的元素禁地中，不过现在远古海域被一个妖怪封锁了，想要过去只有打败它。去远古海域，打败大目鱼妖头领60次，才能安全通过远古海域。" />
		 <enemyName value="大目鱼妖头领" />
		 <mapName value="远古海域" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level17/level.xml"  />
 
 
	 </taskDescription>
	 <taskDescription id="taskDescription35" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭1400只清风寨山贼。" />
		 <completeConditionDescription value="消灭1400只清风寨山贼。" />
		 <enemyName value="清风寨山贼" />
		 <mapName value="清风寨" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level18/level.xml" />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription36" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="救救我们吧！前面山寨里的山贼经常到我们这里抢劫，我们已经死了好多人了，救救我们吧！进入清风寨，击杀山贼头子70次，解救被掠夺的村子。" />
		 <completeConditionDescription value="救救我们吧！前面山寨里的山贼经常到我们这里抢劫，我们已经死了好多人了，救救我们吧！进入清风寨，击杀山贼头子70次，解救被掠夺的村子。" />
		 <enemyName value="山贼头子" />
		 <mapName value="清风寨" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level18/level.xml"  />
 
 
	 </taskDescription>
	 <taskDescription id="taskDescription37" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="消灭1500只僵尸恶鬼。" />
		 <completeConditionDescription value="消灭1500只僵尸恶鬼。" />
		 <enemyName value="僵尸恶鬼" />
		 <mapName value="地狱之门" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level19/level.xml"  />
 
 
	 </taskDescription>
     <taskDescription id="taskDescription38" className="UI.MainLineTask.TaskDescription.TaskDescription1" type="taskDescription1">
		 <taskName value="千辛万苦，历尽磨难。终于来到地狱之门。只要打败地狱之门中的千年僵尸50次，就可以镇压住地狱之门的暴动。" />
		 <completeConditionDescription value="千辛万苦，历尽磨难。终于来到地狱之门。只要打败地狱之门中的千年僵尸100次，就可以镇压住地狱之门的暴动。" />
		 <enemyName value="千年僵尸王" />
		 <mapName value="地狱之门" />
         <dropOut levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level19/level.xml"  />
 
 
	 </taskDescription>
	 
	 <!--新手上路-->
	 <taskDescription id="taskDescription39" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="第一次打造装备" />
		 <completeConditionDescription value="先去杂货店处后买或怪物掉率装备模具，再到铁匠处成功【打造】1件装备" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription40" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="第一次强化武器" />
		 <completeConditionDescription value="到铁匠处使用【强化功能】，成功强化任意1件装备即可完成任务" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription41" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="第一次孵化宠物" />
		 <completeConditionDescription value="在宠物处进入宠物【普通孵化功能】，放入宠物蛋，完成1次普通孵化即可完成任务" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>
	 <taskDescription id="taskDescription42" className="UI.MainLineTask.TaskDescription.TaskDescription3" type="taskDescription3">
		 <taskName value="第一次签到" />
		 <completeConditionDescription value="完成1次签到" />
	 </taskDescription>
	 
      <!--渐入佳境--> 
	 <taskDescription id="taskDescription43" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="打造能手" />
		 <completeConditionDescription value="先去杂货店处后买或怪物掉率装备模具，再到铁匠处成功【打造】5次装备" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription44" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="强化能手" />
		 <completeConditionDescription value="到铁匠处使用【强化功能】，成功将一件装备强化到+6。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription45" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="孵化能手" />
		 <completeConditionDescription value="在宠物处进入宠物【普通孵化功能】，放入宠物蛋，完成5次普通孵化即可完成任务" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>
	 <taskDescription id="taskDescription46" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="第一次幻化" />
		 <completeConditionDescription value="在宠物处进入宠物【幻化功能】，放入主宠和副宠成功幻化1次即可完成任务。" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>
	 <taskDescription id="taskDescription47" className="UI.MainLineTask.TaskDescription.TaskDescription3" type="taskDescription3">
		 <taskName value="在线达人" />
		 <completeConditionDescription value="领取一次35分钟【在线礼包】" />
	 </taskDescription>
	 
      <!--略有小成--> 
	 <taskDescription id="taskDescription48" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="打造高手" />
		 <completeConditionDescription value="先去杂货店处后买或怪物掉率装备模具，再到铁匠处成功【打造】10次装备。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription49" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="强化高手" />
		 <completeConditionDescription value="到铁匠处使用【强化功能】，将任意一件装备成功强化到+9。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription50" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="孵化高手" />
		 <completeConditionDescription value="在宠物处进入宠物【精致孵化功能】，放入宠物蛋，完成5次精致孵化即可完成任务。" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>
	 <taskDescription id="taskDescription51" className="UI.MainLineTask.TaskDescription.TaskDescription3" type="taskDescription3">
		 <taskName value="在线王者" />
		 <completeConditionDescription value="领取一次60分钟【在线礼包】" />	
      </taskDescription>		 
		 	 
	 <taskDescription id="taskDescription52" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="幻化能手" />
		 <completeConditionDescription value="在宠物处进入【宠物幻化功能】，放入主宠和副宠成功幻化3次即可完成任务。" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
		
	 </taskDescription>
	 <taskDescription id="taskDescription53" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="第一次装备开孔" />
		 <completeConditionDescription value="到铁匠处使用【开孔功能】，放入开孔灵符和装备，完成1次装备开孔。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
		
	 </taskDescription>
	 <taskDescription id="taskDescription54" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="第一次超进化" />
		 <completeConditionDescription value="在宠物处进入宠物【超进化功能】，放入可以超进化的宠物，完成1次超进化" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>	
      <!--出类拔萃--> 
	 <taskDescription id="taskDescription55" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="打造大师" />
		 <completeConditionDescription value="先去杂货店处后买或怪物掉率装备模具，再到铁匠处成功【打造】20次。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription56" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="强化大师" />
		 <completeConditionDescription value="到铁匠处使用【强化功能】，成功【强化】装备20次。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription57" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="孵化大师" />
		 <completeConditionDescription value="在宠物处进入宠物【精致孵化功能】，放入宠物蛋，完成5次精致孵化即可完成任务" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>
	 
	 <taskDescription id="taskDescription58" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="幻化达人" />
		 <completeConditionDescription value="在宠物处进入宠物【幻化功能】，成功幻化5次宠物。" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>	 
	 
	 <taskDescription id="taskDescription59" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="超进化达人" />
		 <completeConditionDescription value="在宠物处进入宠物【超进化功能】，放入可以超进化的宠物，完成3次超进化" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>	
		 
	 <taskDescription id="taskDescription60" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="开孔达人" />
		 <completeConditionDescription value="到铁匠处使用【开孔功能】，放入开孔灵符和装备，成功完成5次装备开孔。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
		
	 </taskDescription>		 

      <!--登峰造极--> 
	 <taskDescription id="taskDescription61" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="打造大神" />
		 <completeConditionDescription value="先去杂货店处后买或怪物掉率装备模具，再到铁匠处成功【打造】20次。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription62" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="强化大神" />
		 <completeConditionDescription value="到铁匠处使用【强化功能】，成功强化50次装备。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 <taskDescription id="taskDescription63" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="孵化大神" />
		 <completeConditionDescription value="在宠物处进入宠物【精致孵化功能】，放入宠物蛋，完成15次精致孵化即可完成任务" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超计划" />
	 </taskDescription>
	 
	 <taskDescription id="taskDescription64" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="幻化高手" />
		 <completeConditionDescription value="在宠物处进入宠物【幻化功能】，成功幻化10次宠物。" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>	 
	 
	 <taskDescription id="taskDescription65" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="超进化高手" />
		 <completeConditionDescription value="在宠物处进入宠物【超进化功能】，放入可以超进化的宠物，完成5次超进化" />
		 <npcName value="宠物" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="普通孵化、精致孵化、幻化、超进化" />
	 </taskDescription>
	 
   <taskDescription id="taskDescription66" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="开孔高手" />
		 <completeConditionDescription value="到铁匠处使用【开孔功能】，放入开孔灵符和装备，成功完成12次装备开孔。" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
		
	 </taskDescription>		 
 
   <taskDescription id="taskDescription67" className="UI.MainLineTask.TaskDescription.TaskDescription3" type="taskDescription3">
		 <taskName value="60级僵尸达人" />
		 <completeConditionDescription value="人物成功升级至60级。" />
		
	 </taskDescription>		 
 
   <taskDescription id="taskDescription68" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="PK胜利300场" />
		 <completeConditionDescription value="到PK竞技人处进入【单人PK功能】，成功获得300场胜利。" />
		 <npcName value="PK竞技人" />
		 <mapName value="长安城郊" />
		 <npcFunctionDescription value="单人PK、双人PK" />
		
	 </taskDescription>		 
 
   <taskDescription id="taskDescription69" className="UI.MainLineTask.TaskDescription.TaskDescription3" type="taskDescription3">
	 <taskName value="人物攻击力超过1500" />
		 <completeConditionDescription value="人物攻击力成功超过1500点。" />
	 </taskDescription>		 
 
   <taskDescription id="taskDescription70" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="大圣装达人" />
		 <completeConditionDescription value="拥有1件以上大圣+9装备" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
		
	 </taskDescription>		 
 
   <taskDescription id="taskDescription71" className="UI.MainLineTask.TaskDescription.TaskDescription3" type="taskDescription3">
		 <taskName value="精通师徒玩法" />
		 <completeConditionDescription value="徒弟成功升级至5级。" />
		
	 </taskDescription>		 
 
	 	 
	<!--
	 
	 <taskDescription id="taskDescription2" className="UI.MainLineTask.TaskDescription.TaskDescription2" type="taskDescription2">
		 <taskName value="强化十次武器" />
		 <completeConditionDescription value="强化十次武器" />
		 <npcName value="铁匠" />
		 <mapName value="长安城" />
		 <npcFunctionDescription value="打造、强化、打孔、镶嵌、摘除" />
	 </taskDescription>
	 
	 <taskDescription id="taskDescription3" className="UI.MainLineTask.TaskDescription.TaskDescription3" type="taskDescription3">
		 <taskName value="完成一次签到" />
		 <completeConditionDescription value="完成一次签到" />
	 </taskDescription>
-->

</data>