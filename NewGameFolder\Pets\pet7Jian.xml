<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet7" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet7Idle" />
		<walk defId="pet7Walk" />
		<run defId="pet7Run" />
		<attack defId="pet7Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet7Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet7Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	   <skill id="Skill_Pet7Skill" className="YJFY.Skill.PetSkills.Skill_Pet7Skill" x="0"
			y="-150" z="-1" xRange="500" yRange="300" zRange="100" bodyDefId="pet7SkillBodyShow"
			 disappearBodyId="pet7SkillBodyDisAppear" releaseSkillFrameLabel="releaseSkill^stop^"
			 skillAttackReachFrameLabel="skillAttackReach" replaceFrameLabel="replace" skillEndFrameLabel="skillEnd^stop^">
			
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet7Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet7All.swf"
					showClass="PetStand7_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet7Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet7All.swf"
					showClass="PetWalk7_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet7SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet7All.swf"
					showClass="PetSkill7AttackAnimation_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet7SkillBodyDisAppear" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet7All.swf"
					showClass="PetSkill7LinkDisapppear_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet7Idle" eqClassName="Pet_Jian_1"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetStand7_1"
				x_offset="0" y_offset="0" />
			<show defId="pet7Walk" eqClassName="Pet_Jian_1"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetWalk7_1"
				x_offset="0" y_offset="0" />
           <show defId="pet7SkillBodyShow" eqClassName="Pet_Jian_1"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetSkill7AttackAnimation_1"
				x_offset="0" y_offset="0" />
			<show defId="pet7SkillBodyDisAppear" eqClassName="Pet_Jian_1"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetSkill7LinkDisapppear_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet7Idle" eqClassName="Pet_Jian_2"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetStand7_2"
				x_offset="0" y_offset="0" />
			<show defId="pet7Walk" eqClassName="Pet_Jian_2"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetWalk7_2"
				x_offset="0" y_offset="0" />
           <show defId="pet7SkillBodyShow" eqClassName="Pet_Jian_2"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetSkill7AttackAnimation_2"
				x_offset="0" y_offset="0" />
			<show defId="pet7SkillBodyDisAppear" eqClassName="Pet_Jian_2"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetSkill7LinkDisapppear_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet7Idle" eqClassName="Pet_Jian_3"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetStand7_3"
				x_offset="0" y_offset="0" />
			<show defId="pet7Walk" eqClassName="Pet_Jian_3"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetWalk7_3"
				x_offset="0" y_offset="0" />
           <show defId="pet7SkillBodyShow" eqClassName="Pet_Jian_3"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetSkill7AttackAnimation_3"
				x_offset="0" y_offset="0" />
			<show defId="pet7SkillBodyDisAppear" eqClassName="Pet_Jian_3"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetSkill7LinkDisapppear_3"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
