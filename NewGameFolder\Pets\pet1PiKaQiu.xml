<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet1" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet1Idle" />
		<walk defId="pet1Walk" />
		<run defId="pet1Run" />
		<attack defId="pet1Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet1Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet1Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
		<skill id="Skill_pet1Skill" className="YJFY.Skill.PetSkills.Skill_Pet1Skill" x="-480"
			y="-280" z="-1" xRange="960" yRange="560" zRange="100" bodyDefId="pet1SkillBodyShow" bodyAttackReachFrameLabel="skillAttackReach" 
			bodySkillEndFrameLabel="skillEnd^stop^"  everyEntityAddShowDefId="pet1ShanDian"  everyEntityAddShowIsFrontOfBody="0"  hurtDuration="5000" createRandomShowInterval="100" randomEntityShowId="pet1ShanDian" addEffectOnRandomEntitysId="pet1ShanDianReachEffect" 
			addGroundEffectOneRandomEntityId="pet1ShanDianGroundEffect" effectAddtoTargetId="pet1EnemyBeAttackShow" randomEntityReachGroundFrameLabel="reachGround">
			<animationDefinition id="pet1ShanDian" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_FlashAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet1ShanDianReachEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_FlashBeAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet1EnemyBeAttackShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_FlashLoop" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet1ShanDianGroundEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_FlashGroundEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<flashView swfPath="NewGameFolder/PetSource/Pet1All.swf" className="PetSkill1FlashView" />
		</skill>
		
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet1Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetStand1_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet1Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetWalk1_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet1SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Attack_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet1SkillFrontShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_Word_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!--不换装-->
		    <!--技能攻击效果-->
			<animationDefinition id="pet1ShanDian" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_FlashAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet1ShanDianReachEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_FlashBeAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet1EnemyBeAttackShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_FlashLoop" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet1ShanDianGroundEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet1All.swf"
					showClass="PetSkill1Effect_FlashGroundEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet1Idle" eqClassName="Pet_XiaoPiKaQiu_1"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetStand1_1"
				x_offset="0" y_offset="0" />
			<show defId="pet1Walk" eqClassName="Pet_XiaoPiKaQiu_1"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetWalk1_1"
				x_offset="0" y_offset="0" />
           <show defId="pet1SkillBodyShow" eqClassName="Pet_XiaoPiKaQiu_1"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetSkill1Attack_1"
				x_offset="0" y_offset="0" />
			<show defId="pet1SkillFrontShow" eqClassName="Pet_XiaoPiKaQiu_1"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetSkill1Effect_Word_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet1Idle" eqClassName="Pet_XiaoPiKaQiu_2"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetStand1_2"
				x_offset="0" y_offset="0" />
			<show defId="pet1Walk" eqClassName="Pet_XiaoPiKaQiu_2"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetWalk1_2"
				x_offset="0" y_offset="0" />
           <show defId="pet1SkillBodyShow" eqClassName="Pet_XiaoPiKaQiu_2"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetSkill1Attack_2"
				x_offset="0" y_offset="0" />
			<show defId="pet1SkillFrontShow" eqClassName="Pet_XiaoPiKaQiu_2"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetSkill1Effect_Word_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet1Idle" eqClassName="Pet_XiaoPiKaQiu_3"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetStand1_3"
				x_offset="0" y_offset="0" />
			<show defId="pet1Walk" eqClassName="Pet_XiaoPiKaQiu_3"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetWalk1_3"
				x_offset="0" y_offset="0" />
           <show defId="pet1SkillBodyShow" eqClassName="Pet_XiaoPiKaQiu_3"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetSkill1Attack_3"
				x_offset="0" y_offset="0" />
			<show defId="pet1SkillFrontShow" eqClassName="Pet_XiaoPiKaQiu_3"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetSkill1Effect_Word_3"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
