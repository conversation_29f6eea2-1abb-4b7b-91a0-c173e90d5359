<?xml version="1.0" encoding="utf-8" ?>
<mounts>


	 <!--maxLevel1：最大星级；maxLevel2：最大强化等级
         baseAttack：基础攻击；proAttack1：强化成长；proAttack2：星级成长
	     -->
   <mount id="mount1"
          name="赤炎火龙驹" 
		  logicPartMountXMLPath="NewGameFolder/Mounts/mount1.xml"
		  showSwfPath="Resources/Mount/Mount1.swf" 
		  showClassName="ShowOfMount"
		  maxLevel1="3"
		  maxLevel2="15"
		  baseAttack="105"
		  proAttack1="15"
		  proAttack2="5"
		  baseDefence="45"
		  proDefence1="6"
		  proDefence2="3"
		  baseHp="45"
		  proHp1="6"
		  proHp2="3"
		  >
	   <initSkillsData >
		 <passiveSkill id="pS1" level="1"  />
	   </initSkillsData>


 <!--星级所需碎片数量-->
	   <level1Datas>
		   <level1Data level="0" >
			   <data att="needPowerChipNum" value="10" />
		   </level1Data>
		   <level1Data level="1" >
			   <data att="needPowerChipNum" value="30" />
		   </level1Data>
		   <level1Data level="2" >
			   <data att="needPowerChipNum" value="70" />
		   </level1Data>
		   <level1Data level="3" >
			   <data att="needPowerChipNum" value="0" />
		   </level1Data>

	   </level1Datas>

 <!--强化所需经验数量-->	   
	   <level2Datas>
		   <level2Data level="0">
			   <data att="needStrengthenNum" value="1000" />
		   </level2Data>
		   <level2Data level="1">
			   <data att="needStrengthenNum" value="2000" />
		   </level2Data>
		   <level2Data level="2">
			   <data att="needStrengthenNum" value="3000" />
		   </level2Data>
		   <level2Data level="3">
			   <data att="needStrengthenNum" value="4000" />
		   </level2Data>
		   <level2Data level="4">
			   <data att="needStrengthenNum" value="5000" />
		   </level2Data>
		   <level2Data level="5">
			   <data att="needStrengthenNum" value="6000" />
		   </level2Data>
		   <level2Data level="6">
			   <data att="needStrengthenNum" value="7000" />
		   </level2Data>
		   <level2Data level="7">
			   <data att="needStrengthenNum" value="8000" />
		   </level2Data>
		   <level2Data level="8">
			   <data att="needStrengthenNum" value="9000" />
		   </level2Data>
		   <level2Data level="9">
			   <data att="needStrengthenNum" value="10000" />
		   </level2Data>
		   <level2Data level="10">
			   <data att="needStrengthenNum" value="11000" />
		   </level2Data>
		   <level2Data level="11">
			   <data att="needStrengthenNum" value="12000" />
		   </level2Data>
		   <level2Data level="12">
			   <data att="needStrengthenNum" value="13000" />
		   </level2Data>
		   <level2Data level="13">
			   <data att="needStrengthenNum" value="14000" />
		   </level2Data>
		   <level2Data level="14">
			   <data att="needStrengthenNum" value="15000" />
		   </level2Data>
		   <level2Data level="15">
			   <data att="needStrengthenNum" value="16000" />
		   </level2Data>

		   
	   </level2Datas>
   </mount>





   
   <mount id="mount2" 
          name="寒冰巨熊" 
		  logicPartMountXMLPath="NewGameFolder/Mounts/mount2.xml"
		  showSwfPath="Resources/Mount/Mount2.swf" 
		  showClassName="ShowOfMount"
		  maxLevel1="4"
		  maxLevel2="20"
		  baseAttack="100"
		  proAttack1="12"
		  proAttack2="5"
		  baseDefence="95"
		  proDefence1="10"
		  proDefence2="5"
		  baseHp="95"
		  proHp1="10"
		  proHp2="5"
		  >
	   <initSkillsData >
		 <passiveSkill id="pS2" level="1"  />
	   </initSkillsData>
	   <level1Datas>
		   <level1Data level="0" >
			   <data att="needPowerChipNum" value="30" />
		   </level1Data>
		   <level1Data level="1" >
			   <data att="needPowerChipNum" value="50" />
		   </level1Data>
		   <level1Data level="2" >
			   <data att="needPowerChipNum" value="70" />
		   </level1Data>
		   <level1Data level="3" >
			   <data att="needPowerChipNum" value="90" />
		   </level1Data>
		   <level1Data level="4" >
			   <data att="needPowerChipNum" value="0" />
		   </level1Data>
	   </level1Datas>
 <!--强化所需经验数量-->	   
	   <level2Datas>
		   <level2Data level="0">
			   <data att="needStrengthenNum" value="1000" />
		   </level2Data>
		   <level2Data level="1">
			   <data att="needStrengthenNum" value="2000" />
		   </level2Data>
		   <level2Data level="2">
			   <data att="needStrengthenNum" value="3000" />
		   </level2Data>
		   <level2Data level="3">
			   <data att="needStrengthenNum" value="4000" />
		   </level2Data>
		   <level2Data level="4">
			   <data att="needStrengthenNum" value="5000" />
		   </level2Data>
		   <level2Data level="5">
			   <data att="needStrengthenNum" value="6000" />
		   </level2Data>
		   <level2Data level="6">
			   <data att="needStrengthenNum" value="7000" />
		   </level2Data>
		   <level2Data level="7">
			   <data att="needStrengthenNum" value="8000" />
		   </level2Data>
		   <level2Data level="8">
			   <data att="needStrengthenNum" value="9000" />
		   </level2Data>
		   <level2Data level="9">
			   <data att="needStrengthenNum" value="10000" />
		   </level2Data>
		   <level2Data level="10">
			   <data att="needStrengthenNum" value="11000" />
		   </level2Data>
		   <level2Data level="11">
			   <data att="needStrengthenNum" value="12000" />
		   </level2Data>
		   <level2Data level="12">
			   <data att="needStrengthenNum" value="13000" />
		   </level2Data>
		   <level2Data level="13">
			   <data att="needStrengthenNum" value="14000" />
		   </level2Data>
		   <level2Data level="14">
			   <data att="needStrengthenNum" value="15000" />
		   </level2Data>
		   <level2Data level="15">
			   <data att="needStrengthenNum" value="16000" />
		   </level2Data>
		   <level2Data level="16">
			   <data att="needStrengthenNum" value="17000" />
		   </level2Data>
		   <level2Data level="17">
			   <data att="needStrengthenNum" value="18000" />
		   </level2Data>
		   <level2Data level="18">
			   <data att="needStrengthenNum" value="19000" />
		   </level2Data>
		   <level2Data level="19">
			   <data att="needStrengthenNum" value="20000" />
		   </level2Data>
		   <level2Data level="20">
			   <data att="needStrengthenNum" value="21000" />
		   </level2Data>
		   
	   </level2Datas>
	   
	   </mount>   
   <mount id="mount3" 
          name="毁灭战熊" 
		  logicPartMountXMLPath="NewGameFolder/Mounts/mount3.xml"
		  showSwfPath="Resources/Mount/Mount3.swf" 
		  showClassName="ShowOfMount"
		  maxLevel1="5"
		  maxLevel2="25"
		  baseAttack="120"
		  proAttack1="20"
		  proAttack2="8"
		  baseDefence="110"
		  proDefence1="15"
		  proDefence2="8"
		  baseHp="150"
		  proHp1="15"
		  proHp2="8"
		  >
	   <initSkillsData >
		 <passiveSkill id="pS3" level="1"  />
	   </initSkillsData>
	   <level1Datas>
		   <level1Data level="0" >
			   <data att="needPowerChipNum" value="50" />
		   </level1Data>
		   <level1Data level="1" >
			   <data att="needPowerChipNum" value="70" />
		   </level1Data>
		   <level1Data level="2" >
			   <data att="needPowerChipNum" value="90" />
		   </level1Data>
		   <level1Data level="3" >
			   <data att="needPowerChipNum" value="110" />
		   </level1Data>
		   <level1Data level="4" >
			   <data att="needPowerChipNum" value="130" />
		   </level1Data>
		   <level1Data level="5" >
			   <data att="needPowerChipNum" value="0" />
		   </level1Data>
	   </level1Datas>
 <!--强化所需经验数量-->	   
	   <level2Datas>
		   <level2Data level="0">
			   <data att="needStrengthenNum" value="1000" />
		   </level2Data>
		   <level2Data level="1">
			   <data att="needStrengthenNum" value="2000" />
		   </level2Data>
		   <level2Data level="2">
			   <data att="needStrengthenNum" value="3000" />
		   </level2Data>
		   <level2Data level="3">
			   <data att="needStrengthenNum" value="4000" />
		   </level2Data>
		   <level2Data level="4">
			   <data att="needStrengthenNum" value="5000" />
		   </level2Data>
		   <level2Data level="5">
			   <data att="needStrengthenNum" value="6000" />
		   </level2Data>
		   <level2Data level="6">
			   <data att="needStrengthenNum" value="7000" />
		   </level2Data>
		   <level2Data level="7">
			   <data att="needStrengthenNum" value="8000" />
		   </level2Data>
		   <level2Data level="8">
			   <data att="needStrengthenNum" value="9000" />
		   </level2Data>
		   <level2Data level="9">
			   <data att="needStrengthenNum" value="10000" />
		   </level2Data>
		   <level2Data level="10">
			   <data att="needStrengthenNum" value="11000" />
		   </level2Data>
		   <level2Data level="11">
			   <data att="needStrengthenNum" value="12000" />
		   </level2Data>
		   <level2Data level="12">
			   <data att="needStrengthenNum" value="13000" />
		   </level2Data>
		   <level2Data level="13">
			   <data att="needStrengthenNum" value="14000" />
		   </level2Data>
		   <level2Data level="14">
			   <data att="needStrengthenNum" value="15000" />
		   </level2Data>
		   <level2Data level="15">
			   <data att="needStrengthenNum" value="16000" />
		   </level2Data>
		   <level2Data level="16">
			   <data att="needStrengthenNum" value="17000" />
		   </level2Data>
		   <level2Data level="17">
			   <data att="needStrengthenNum" value="18000" />
		   </level2Data>
		   <level2Data level="18">
			   <data att="needStrengthenNum" value="19000" />
		   </level2Data>
		   <level2Data level="19">
			   <data att="needStrengthenNum" value="20000" />
		   </level2Data>
		   <level2Data level="20">
			   <data att="needStrengthenNum" value="21000" />
		   </level2Data>
		   <level2Data level="21">
			   <data att="needStrengthenNum" value="22000" />
		   </level2Data>
		   <level2Data level="22">
			   <data att="needStrengthenNum" value="23000" />
		   </level2Data>
		   <level2Data level="23">
			   <data att="needStrengthenNum" value="24000" />
		   </level2Data>
		   <level2Data level="24">
			   <data att="needStrengthenNum" value="25000" />
		   </level2Data>
		   <level2Data level="25">
			   <data att="needStrengthenNum" value="26000" />
		   </level2Data>
		   
	   </level2Datas>
   </mount>
     <mount id="mount4" 
          name="广寒玉兔" 
		  logicPartMountXMLPath="NewGameFolder/Mounts/mount4.xml"
		  showSwfPath="Resources/Mount/Mount4.swf" 
		  showClassName="ShowOfMount"
		  maxLevel1="5"
		  maxLevel2="30"
		  baseAttack="100"
		  proAttack1="10"
		  proAttack2="5"
		  baseDefence="120"
		  proDefence1="15"
		  proDefence2="8"
		  baseHp="200"
		  proHp1="50"
		  proHp2="20"
		  >
	   <initSkillsData >
		 <passiveSkill id="pS4" level="1"  />
	   </initSkillsData>
	   <level1Datas>
		   <level1Data level="0" >
			   <data att="needPowerChipNum" value="20" />
		   </level1Data>
		   <level1Data level="1" >
			   <data att="needPowerChipNum" value="90" />
		   </level1Data>
		   <level1Data level="2" >
			   <data att="needPowerChipNum" value="130" />
		   </level1Data>
		   <level1Data level="3" >
			   <data att="needPowerChipNum" value="170" />
		   </level1Data>
		   <level1Data level="4" >
			   <data att="needPowerChipNum" value="220" />
		   </level1Data>
		   <level1Data level="5" >
			   <data att="needPowerChipNum" value="0" />
		   </level1Data>
	   </level1Datas>
 <!--强化所需经验数量-->	   
	   <level2Datas>
		   <level2Data level="0">
			   <data att="needStrengthenNum" value="1000" />
		   </level2Data>
		   <level2Data level="1">
			   <data att="needStrengthenNum" value="2000" />
		   </level2Data>
		   <level2Data level="2">
			   <data att="needStrengthenNum" value="3000" />
		   </level2Data>
		   <level2Data level="3">
			   <data att="needStrengthenNum" value="4000" />
		   </level2Data>
		   <level2Data level="4">
			   <data att="needStrengthenNum" value="5000" />
		   </level2Data>
		   <level2Data level="5">
			   <data att="needStrengthenNum" value="6000" />
		   </level2Data>
		   <level2Data level="6">
			   <data att="needStrengthenNum" value="7000" />
		   </level2Data>
		   <level2Data level="7">
			   <data att="needStrengthenNum" value="8000" />
		   </level2Data>
		   <level2Data level="8">
			   <data att="needStrengthenNum" value="9000" />
		   </level2Data>
		   <level2Data level="9">
			   <data att="needStrengthenNum" value="10000" />
		   </level2Data>
		   <level2Data level="10">
			   <data att="needStrengthenNum" value="11000" />
		   </level2Data>
		   <level2Data level="11">
			   <data att="needStrengthenNum" value="12000" />
		   </level2Data>
		   <level2Data level="12">
			   <data att="needStrengthenNum" value="13000" />
		   </level2Data>
		   <level2Data level="13">
			   <data att="needStrengthenNum" value="14000" />
		   </level2Data>
		   <level2Data level="14">
			   <data att="needStrengthenNum" value="15000" />
		   </level2Data>
		   <level2Data level="15">
			   <data att="needStrengthenNum" value="16000" />
		   </level2Data>
		   <level2Data level="16">
			   <data att="needStrengthenNum" value="17000" />
		   </level2Data>
		   <level2Data level="17">
			   <data att="needStrengthenNum" value="18000" />
		   </level2Data>
		   <level2Data level="18">
			   <data att="needStrengthenNum" value="19000" />
		   </level2Data>
		   <level2Data level="19">
			   <data att="needStrengthenNum" value="20000" />
		   </level2Data>
		   <level2Data level="20">
			   <data att="needStrengthenNum" value="21000" />
		   </level2Data>
		   <level2Data level="21">
			   <data att="needStrengthenNum" value="22000" />
		   </level2Data>
		   <level2Data level="22">
			   <data att="needStrengthenNum" value="23000" />
		   </level2Data>
		   <level2Data level="23">
			   <data att="needStrengthenNum" value="24000" />
		   </level2Data>
		   <level2Data level="24">
			   <data att="needStrengthenNum" value="25000" />
		   </level2Data>
		   <level2Data level="25">
			   <data att="needStrengthenNum" value="26000" />
		   </level2Data>
		   <level2Data level="26">
			   <data att="needStrengthenNum" value="27000" />
		   </level2Data>
		   <level2Data level="27">
			   <data att="needStrengthenNum" value="28000" />
		   </level2Data>
		   <level2Data level="28">
			   <data att="needStrengthenNum" value="29000" />
		   </level2Data>
		   <level2Data level="29">
			   <data att="needStrengthenNum" value="30000" />
		   </level2Data>
		   <level2Data level="30">
			   <data att="needStrengthenNum" value="31000" />
		   </level2Data>
		   
	   </level2Datas>
   </mount> 
   
    <!--maxLevel1：最大星级；maxLevel2：最大强化等级
         baseAttack：基础攻击；proAttack1：强化成长；proAttack2：星级成长
	     -->
    <mount id="mount5" 
          name="吉祥花虎" 
		  logicPartMountXMLPath="NewGameFolder/Mounts/mount5.xml"
		  showSwfPath="Resources/Mount/Mount5.swf" 
		  showClassName="ShowOfMount"
		  maxLevel1="5"
		  maxLevel2="35"
		  baseAttack="150"
		  proAttack1="10"
		  proAttack2="10"
		  baseDefence="100"
		  proDefence1="10"
		  proDefence2="4"
		  baseHp="200"
		  proHp1="10"
		  proHp2="6"
		  >
	   <initSkillsData >
		 <passiveSkill id="pS5" level="1"  />
	   </initSkillsData>
	   <level1Datas>
		   <level1Data level="0" >
			   <data att="needPowerChipNum" value="10" />
		   </level1Data>
		   <level1Data level="1" >
			   <data att="needPowerChipNum" value="120" />
		   </level1Data>
		   <level1Data level="2" >
			   <data att="needPowerChipNum" value="160" />
		   </level1Data>
		   <level1Data level="3" >
			   <data att="needPowerChipNum" value="220" />
		   </level1Data>
		   <level1Data level="4" >
			   <data att="needPowerChipNum" value="300" />
		   </level1Data>
		   <level1Data level="5" >
			   <data att="needPowerChipNum" value="0" />
		   </level1Data>
	   </level1Datas>
 <!--强化所需经验数量-->	   
	   <level2Datas>
		   <level2Data level="0">
			   <data att="needStrengthenNum" value="1000" />
		   </level2Data>
		   <level2Data level="1">
			   <data att="needStrengthenNum" value="2000" />
		   </level2Data>
		   <level2Data level="2">
			   <data att="needStrengthenNum" value="3000" />
		   </level2Data>
		   <level2Data level="3">
			   <data att="needStrengthenNum" value="4000" />
		   </level2Data>
		   <level2Data level="4">
			   <data att="needStrengthenNum" value="5000" />
		   </level2Data>
		   <level2Data level="5">
			   <data att="needStrengthenNum" value="6000" />
		   </level2Data>
		   <level2Data level="6">
			   <data att="needStrengthenNum" value="7000" />
		   </level2Data>
		   <level2Data level="7">
			   <data att="needStrengthenNum" value="8000" />
		   </level2Data>
		   <level2Data level="8">
			   <data att="needStrengthenNum" value="9000" />
		   </level2Data>
		   <level2Data level="9">
			   <data att="needStrengthenNum" value="10000" />
		   </level2Data>
		   <level2Data level="10">
			   <data att="needStrengthenNum" value="11000" />
		   </level2Data>
		   <level2Data level="11">
			   <data att="needStrengthenNum" value="12000" />
		   </level2Data>
		   <level2Data level="12">
			   <data att="needStrengthenNum" value="13000" />
		   </level2Data>
		   <level2Data level="13">
			   <data att="needStrengthenNum" value="14000" />
		   </level2Data>
		   <level2Data level="14">
			   <data att="needStrengthenNum" value="15000" />
		   </level2Data>
		   <level2Data level="15">
			   <data att="needStrengthenNum" value="16000" />
		   </level2Data>
		   <level2Data level="16">
			   <data att="needStrengthenNum" value="17000" />
		   </level2Data>
		   <level2Data level="17">
			   <data att="needStrengthenNum" value="18000" />
		   </level2Data>
		   <level2Data level="18">
			   <data att="needStrengthenNum" value="19000" />
		   </level2Data>
		   <level2Data level="19">
			   <data att="needStrengthenNum" value="20000" />
		   </level2Data>
		   <level2Data level="20">
			   <data att="needStrengthenNum" value="21000" />
		   </level2Data>
		   <level2Data level="21">
			   <data att="needStrengthenNum" value="22000" />
		   </level2Data>
		   <level2Data level="22">
			   <data att="needStrengthenNum" value="23000" />
		   </level2Data>
		   <level2Data level="23">
			   <data att="needStrengthenNum" value="24000" />
		   </level2Data>
		   <level2Data level="24">
			   <data att="needStrengthenNum" value="25000" />
		   </level2Data>
		   <level2Data level="25">
			   <data att="needStrengthenNum" value="26000" />
		   </level2Data>
		   <level2Data level="26">
			   <data att="needStrengthenNum" value="27000" />
		   </level2Data>
		   <level2Data level="27">
			   <data att="needStrengthenNum" value="28000" />
		   </level2Data>
		   <level2Data level="28">
			   <data att="needStrengthenNum" value="29000" />
		   </level2Data>
		   <level2Data level="29">
			   <data att="needStrengthenNum" value="30000" />
		   </level2Data>
		   <level2Data level="30">
			   <data att="needStrengthenNum" value="31000" />
		   </level2Data>
		   <level2Data level="31">
			   <data att="needStrengthenNum" value="32000" />
		   </level2Data>
		   <level2Data level="32">
			   <data att="needStrengthenNum" value="33000" />
		   </level2Data>
		   <level2Data level="33">
			   <data att="needStrengthenNum" value="34000" />
		   </level2Data>
		   <level2Data level="34">
			   <data att="needStrengthenNum" value="35000" />
		   </level2Data>
		   <level2Data level="35">
			   <data att="needStrengthenNum" value="36000" />
		   </level2Data> 
		   
		   
		   
		   
	   </level2Datas>
   </mount> 
   <!--maxLevel1：最大星级；maxLevel2：最大强化等级
         baseAttack：基础攻击；proAttack1：强化成长；proAttack2：星级成长
	     -->
    <mount id="mount6" 
          name="普贤圣象" 
		  logicPartMountXMLPath="NewGameFolder/Mounts/mount6.xml"
		  showSwfPath="Resources/Mount/Mount6.swf" 
		  showClassName="ShowOfMount"
		  maxLevel1="5"
		  maxLevel2="35"
		  baseAttack="100"
		  proAttack1="7"
		  proAttack2="7"
		  baseDefence="100"
		  proDefence1="10"
		  proDefence2="6"
		  baseHp="150"
		  proHp1="8"
		  proHp2="8"
		  >
	   <initSkillsData >
		 <passiveSkill id="pS6" level="1"  />
	   </initSkillsData>
	   <level1Datas>
		   <level1Data level="0" >
			   <data att="needPowerChipNum" value="40" />
		   </level1Data>
		   <level1Data level="1" >
			   <data att="needPowerChipNum" value="120" />
		   </level1Data>
		   <level1Data level="2" >
			   <data att="needPowerChipNum" value="160" />
		   </level1Data>
		   <level1Data level="3" >
			   <data att="needPowerChipNum" value="220" />
		   </level1Data>
		   <level1Data level="4" >
			   <data att="needPowerChipNum" value="300" />
		   </level1Data>
		   <level1Data level="5" >
			   <data att="needPowerChipNum" value="0" />
		   </level1Data>
	   </level1Datas>
 <!--强化所需经验数量-->	   
	   <level2Datas>
		   <level2Data level="0">
			   <data att="needStrengthenNum" value="1000" />
		   </level2Data>
		   <level2Data level="1">
			   <data att="needStrengthenNum" value="2000" />
		   </level2Data>
		   <level2Data level="2">
			   <data att="needStrengthenNum" value="3000" />
		   </level2Data>
		   <level2Data level="3">
			   <data att="needStrengthenNum" value="4000" />
		   </level2Data>
		   <level2Data level="4">
			   <data att="needStrengthenNum" value="5000" />
		   </level2Data>
		   <level2Data level="5">
			   <data att="needStrengthenNum" value="6000" />
		   </level2Data>
		   <level2Data level="6">
			   <data att="needStrengthenNum" value="7000" />
		   </level2Data>
		   <level2Data level="7">
			   <data att="needStrengthenNum" value="8000" />
		   </level2Data>
		   <level2Data level="8">
			   <data att="needStrengthenNum" value="9000" />
		   </level2Data>
		   <level2Data level="9">
			   <data att="needStrengthenNum" value="10000" />
		   </level2Data>
		   <level2Data level="10">
			   <data att="needStrengthenNum" value="11000" />
		   </level2Data>
		   <level2Data level="11">
			   <data att="needStrengthenNum" value="12000" />
		   </level2Data>
		   <level2Data level="12">
			   <data att="needStrengthenNum" value="13000" />
		   </level2Data>
		   <level2Data level="13">
			   <data att="needStrengthenNum" value="14000" />
		   </level2Data>
		   <level2Data level="14">
			   <data att="needStrengthenNum" value="15000" />
		   </level2Data>
		   <level2Data level="15">
			   <data att="needStrengthenNum" value="16000" />
		   </level2Data>
		   <level2Data level="16">
			   <data att="needStrengthenNum" value="17000" />
		   </level2Data>
		   <level2Data level="17">
			   <data att="needStrengthenNum" value="18000" />
		   </level2Data>
		   <level2Data level="18">
			   <data att="needStrengthenNum" value="19000" />
		   </level2Data>
		   <level2Data level="19">
			   <data att="needStrengthenNum" value="20000" />
		   </level2Data>
		   <level2Data level="20">
			   <data att="needStrengthenNum" value="21000" />
		   </level2Data>
		   <level2Data level="21">
			   <data att="needStrengthenNum" value="22000" />
		   </level2Data>
		   <level2Data level="22">
			   <data att="needStrengthenNum" value="23000" />
		   </level2Data>
		   <level2Data level="23">
			   <data att="needStrengthenNum" value="24000" />
		   </level2Data>
		   <level2Data level="24">
			   <data att="needStrengthenNum" value="25000" />
		   </level2Data>
		   <level2Data level="25">
			   <data att="needStrengthenNum" value="26000" />
		   </level2Data>
		   <level2Data level="26">
			   <data att="needStrengthenNum" value="27000" />
		   </level2Data>
		   <level2Data level="27">
			   <data att="needStrengthenNum" value="28000" />
		   </level2Data>
		   <level2Data level="28">
			   <data att="needStrengthenNum" value="29000" />
		   </level2Data>
		   <level2Data level="29">
			   <data att="needStrengthenNum" value="30000" />
		   </level2Data>
		   <level2Data level="30">
			   <data att="needStrengthenNum" value="31000" />
		   </level2Data>
		   <level2Data level="31">
			   <data att="needStrengthenNum" value="32000" />
		   </level2Data>
		   <level2Data level="32">
			   <data att="needStrengthenNum" value="33000" />
		   </level2Data>
		   <level2Data level="33">
			   <data att="needStrengthenNum" value="34000" />
		   </level2Data>
		   <level2Data level="34">
			   <data att="needStrengthenNum" value="35000" />
		   </level2Data>
		   <level2Data level="35">
			   <data att="needStrengthenNum" value="36000" />
		   </level2Data> 
		   
		   
		   
		   
	   </level2Datas>
   </mount> 
   
     <!--maxLevel1：最大星级；maxLevel2：最大强化等级
         baseAttack：基础攻击；proAttack1：强化成长；proAttack2：星级成长
	     -->
   
    <mount id="mount7" 
          name="文殊青狮" 
		  logicPartMountXMLPath="NewGameFolder/Mounts/mount7.xml"
		  showSwfPath="Resources/Mount/Mount7.swf" 
		  showClassName="ShowOfMount"
		  maxLevel1="5"
		  maxLevel2="35"
		  baseAttack="120"
		  proAttack1="8"
		  proAttack2="8"
		  baseDefence="100"
		  proDefence1="7"
		  proDefence2="5"
		  baseHp="100"
		  proHp1="8"
		  proHp2="8"
		  >
	   <initSkillsData >
		 <passiveSkill id="pS7" level="1"  />
	   </initSkillsData>
	   <level1Datas>
		   <level1Data level="0" >
			   <data att="needPowerChipNum" value="40" />
		   </level1Data>
		   <level1Data level="1" >
			   <data att="needPowerChipNum" value="120" />
		   </level1Data>
		   <level1Data level="2" >
			   <data att="needPowerChipNum" value="160" />
		   </level1Data>
		   <level1Data level="3" >
			   <data att="needPowerChipNum" value="220" />
		   </level1Data>
		   <level1Data level="4" >
			   <data att="needPowerChipNum" value="300" />
		   </level1Data>
		   <level1Data level="5" >
			   <data att="needPowerChipNum" value="0" />
		   </level1Data>
	   </level1Datas>
 <!--强化所需经验数量-->	   
	   <level2Datas>
		   <level2Data level="0">
			   <data att="needStrengthenNum" value="1000" />
		   </level2Data>
		   <level2Data level="1">
			   <data att="needStrengthenNum" value="2000" />
		   </level2Data>
		   <level2Data level="2">
			   <data att="needStrengthenNum" value="3000" />
		   </level2Data>
		   <level2Data level="3">
			   <data att="needStrengthenNum" value="4000" />
		   </level2Data>
		   <level2Data level="4">
			   <data att="needStrengthenNum" value="5000" />
		   </level2Data>
		   <level2Data level="5">
			   <data att="needStrengthenNum" value="6000" />
		   </level2Data>
		   <level2Data level="6">
			   <data att="needStrengthenNum" value="7000" />
		   </level2Data>
		   <level2Data level="7">
			   <data att="needStrengthenNum" value="8000" />
		   </level2Data>
		   <level2Data level="8">
			   <data att="needStrengthenNum" value="9000" />
		   </level2Data>
		   <level2Data level="9">
			   <data att="needStrengthenNum" value="10000" />
		   </level2Data>
		   <level2Data level="10">
			   <data att="needStrengthenNum" value="11000" />
		   </level2Data>
		   <level2Data level="11">
			   <data att="needStrengthenNum" value="12000" />
		   </level2Data>
		   <level2Data level="12">
			   <data att="needStrengthenNum" value="13000" />
		   </level2Data>
		   <level2Data level="13">
			   <data att="needStrengthenNum" value="14000" />
		   </level2Data>
		   <level2Data level="14">
			   <data att="needStrengthenNum" value="15000" />
		   </level2Data>
		   <level2Data level="15">
			   <data att="needStrengthenNum" value="16000" />
		   </level2Data>
		   <level2Data level="16">
			   <data att="needStrengthenNum" value="17000" />
		   </level2Data>
		   <level2Data level="17">
			   <data att="needStrengthenNum" value="18000" />
		   </level2Data>
		   <level2Data level="18">
			   <data att="needStrengthenNum" value="19000" />
		   </level2Data>
		   <level2Data level="19">
			   <data att="needStrengthenNum" value="20000" />
		   </level2Data>
		   <level2Data level="20">
			   <data att="needStrengthenNum" value="21000" />
		   </level2Data>
		   <level2Data level="21">
			   <data att="needStrengthenNum" value="22000" />
		   </level2Data>
		   <level2Data level="22">
			   <data att="needStrengthenNum" value="23000" />
		   </level2Data>
		   <level2Data level="23">
			   <data att="needStrengthenNum" value="24000" />
		   </level2Data>
		   <level2Data level="24">
			   <data att="needStrengthenNum" value="25000" />
		   </level2Data>
		   <level2Data level="25">
			   <data att="needStrengthenNum" value="26000" />
		   </level2Data>
		   <level2Data level="26">
			   <data att="needStrengthenNum" value="27000" />
		   </level2Data>
		   <level2Data level="27">
			   <data att="needStrengthenNum" value="28000" />
		   </level2Data>
		   <level2Data level="28">
			   <data att="needStrengthenNum" value="29000" />
		   </level2Data>
		   <level2Data level="29">
			   <data att="needStrengthenNum" value="30000" />
		   </level2Data>
		   <level2Data level="30">
			   <data att="needStrengthenNum" value="31000" />
		   </level2Data>
		   <level2Data level="31">
			   <data att="needStrengthenNum" value="32000" />
		   </level2Data>
		   <level2Data level="32">
			   <data att="needStrengthenNum" value="33000" />
		   </level2Data>
		   <level2Data level="33">
			   <data att="needStrengthenNum" value="34000" />
		   </level2Data>
		   <level2Data level="34">
			   <data att="needStrengthenNum" value="35000" />
		   </level2Data>
		   <level2Data level="35">
			   <data att="needStrengthenNum" value="36000" />
		   </level2Data> 
		   
		   
		   
		   
	   </level2Datas>
   </mount> 
        <!--maxLevel1：最大星级；maxLevel2：最大强化等级
         baseAttack：基础攻击；proAttack1：强化成长；proAttack2：星级成长
	     -->
    <mount id="mount8" 
          name="火麒麟" 
		  logicPartMountXMLPath="NewGameFolder/Mounts/mount8.xml"
		  showSwfPath="Resources/Mount/Mount8.swf" 
		  showClassName="ShowOfMount"
		  maxLevel1="5"
		  maxLevel2="35"
		  baseAttack="120"
		  proAttack1="8"
		  proAttack2="8"
		  baseDefence="100"
		  proDefence1="7"
		  proDefence2="5"
		  baseHp="100"
		  proHp1="8"
		  proHp2="8"
		  >
	   <initSkillsData >
		 <passiveSkill id="pS8" level="1"  />
	   </initSkillsData>
	   <level1Datas>
		   <level1Data level="0" >
			   <data att="needPowerChipNum" value="40" />
		   </level1Data>
		   <level1Data level="1" >
			   <data att="needPowerChipNum" value="120" />
		   </level1Data>
		   <level1Data level="2" >
			   <data att="needPowerChipNum" value="160" />
		   </level1Data>
		   <level1Data level="3" >
			   <data att="needPowerChipNum" value="220" />
		   </level1Data>
		   <level1Data level="4" >
			   <data att="needPowerChipNum" value="300" />
		   </level1Data>
		   <level1Data level="5" >
			   <data att="needPowerChipNum" value="0" />
		   </level1Data>
	   </level1Datas>
 <!--强化所需经验数量-->	   
	   <level2Datas>
		   <level2Data level="0">
			   <data att="needStrengthenNum" value="1000" />
		   </level2Data>
		   <level2Data level="1">
			   <data att="needStrengthenNum" value="2000" />
		   </level2Data>
		   <level2Data level="2">
			   <data att="needStrengthenNum" value="3000" />
		   </level2Data>
		   <level2Data level="3">
			   <data att="needStrengthenNum" value="4000" />
		   </level2Data>
		   <level2Data level="4">
			   <data att="needStrengthenNum" value="5000" />
		   </level2Data>
		   <level2Data level="5">
			   <data att="needStrengthenNum" value="6000" />
		   </level2Data>
		   <level2Data level="6">
			   <data att="needStrengthenNum" value="7000" />
		   </level2Data>
		   <level2Data level="7">
			   <data att="needStrengthenNum" value="8000" />
		   </level2Data>
		   <level2Data level="8">
			   <data att="needStrengthenNum" value="9000" />
		   </level2Data>
		   <level2Data level="9">
			   <data att="needStrengthenNum" value="10000" />
		   </level2Data>
		   <level2Data level="10">
			   <data att="needStrengthenNum" value="11000" />
		   </level2Data>
		   <level2Data level="11">
			   <data att="needStrengthenNum" value="12000" />
		   </level2Data>
		   <level2Data level="12">
			   <data att="needStrengthenNum" value="13000" />
		   </level2Data>
		   <level2Data level="13">
			   <data att="needStrengthenNum" value="14000" />
		   </level2Data>
		   <level2Data level="14">
			   <data att="needStrengthenNum" value="15000" />
		   </level2Data>
		   <level2Data level="15">
			   <data att="needStrengthenNum" value="16000" />
		   </level2Data>
		   <level2Data level="16">
			   <data att="needStrengthenNum" value="17000" />
		   </level2Data>
		   <level2Data level="17">
			   <data att="needStrengthenNum" value="18000" />
		   </level2Data>
		   <level2Data level="18">
			   <data att="needStrengthenNum" value="19000" />
		   </level2Data>
		   <level2Data level="19">
			   <data att="needStrengthenNum" value="20000" />
		   </level2Data>
		   <level2Data level="20">
			   <data att="needStrengthenNum" value="21000" />
		   </level2Data>
		   <level2Data level="21">
			   <data att="needStrengthenNum" value="22000" />
		   </level2Data>
		   <level2Data level="22">
			   <data att="needStrengthenNum" value="23000" />
		   </level2Data>
		   <level2Data level="23">
			   <data att="needStrengthenNum" value="24000" />
		   </level2Data>
		   <level2Data level="24">
			   <data att="needStrengthenNum" value="25000" />
		   </level2Data>
		   <level2Data level="25">
			   <data att="needStrengthenNum" value="26000" />
		   </level2Data>
		   <level2Data level="26">
			   <data att="needStrengthenNum" value="27000" />
		   </level2Data>
		   <level2Data level="27">
			   <data att="needStrengthenNum" value="28000" />
		   </level2Data>
		   <level2Data level="28">
			   <data att="needStrengthenNum" value="29000" />
		   </level2Data>
		   <level2Data level="29">
			   <data att="needStrengthenNum" value="30000" />
		   </level2Data>
		   <level2Data level="30">
			   <data att="needStrengthenNum" value="31000" />
		   </level2Data>
		   <level2Data level="31">
			   <data att="needStrengthenNum" value="32000" />
		   </level2Data>
		   <level2Data level="32">
			   <data att="needStrengthenNum" value="33000" />
		   </level2Data>
		   <level2Data level="33">
			   <data att="needStrengthenNum" value="34000" />
		   </level2Data>
		   <level2Data level="34">
			   <data att="needStrengthenNum" value="35000" />
		   </level2Data>
		   <level2Data level="35">
			   <data att="needStrengthenNum" value="36000" />
		   </level2Data> 
	 
	   </level2Datas>
   </mount> 
   
</mounts>