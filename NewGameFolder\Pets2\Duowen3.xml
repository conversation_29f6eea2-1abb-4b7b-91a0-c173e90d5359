<?xml version="1.0" encoding="utf-8" ?>
<data>
	<skillLinkData>
		<data part1SkillId="Skill_BossTeleport" part2SkillId="Duowen3Skill1" />
		<data part1SkillId="Skill_BossSkill1" part2SkillId="Duowen3Skill2" />
		<data part1SkillId="bossSkill1" part2SkillId="Duowen3Skill3" />
	</skillLinkData>
	<!--<hurtAnimation2 defId="hurt2_jiaoRen" playFrameLabel="1" recoverFrameLabel="recover^stop^" />-->
	<autoAttackAI className="YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetAI1" />
	<petAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="500" />
		<data att="unableAttackMaxInterval" value="1500" />
	</petAttackData>
   
	<animal id="Duowen3" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="140" bodyHeight="180" walkSpeed="150"
		runSpeed="300">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="-20" y="-10" z="-1" xRange="200" yRange="20" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
			
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" x="-200"
			y="-150" z="-1" xRange="450" yRange="350" zRange="100" bodyDefId="skill1Animation"  hurtDuration="1000"  
			skillAttackEffectDefId="bossSkill1AttackEffect" randomPlaceXRange="10" randomPlaceYRange="-60" attackInterval="200"
			skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^" >
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
		</skill>
		<skill id="bossSkill1" className="YJFY.BossMode.Boss1.Skill_PushSkill" x="-100"
			y="-100" z="-1" xRange="300" yRange="300" zRange="200" bodyDefId="skill3Animation" skillAttackReachFrameLabel="skillReach" shakeFrameLabel="shake" hurtDuration="1500" pushForce="10000"
			skillEndFrameLabel="skillEnd^stop^">
			<shakeView swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf" className="ShakeView" />
			<moveData swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf" className="MoveDataOfSkill" />
		</skill>
		<skill id="SkillResurgence" className="YJFY.LevelMode2.Levels1.Skill_AllResurgence"  bodyDefId="skillShow_resurgence"
			x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="300">
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf" 
				showClass="CBossRun" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="CBossBeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="CBossDead" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="skillShow_resurgence" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="skill_resurgence" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill3Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="CBossSkill3Show" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="DisappearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="AppearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="5"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf"
					showClass="ShadowOfAutomaticPet1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
			<animationDefinition id="yunQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Duowen3.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>
         <shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="bossFootShadow1" showSeriesId="d"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet1"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="c"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet2"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="b"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet3"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="a"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet4"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="s"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet5"
				x_offset="0" y_offset="0" />
				
		</shows>


	</animal>
</data>