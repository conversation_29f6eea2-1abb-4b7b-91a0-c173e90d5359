<?xml version="1.0" encoding="utf-8" ?>
<data>
	<skillLinkData>
	</skillLinkData>
	<hurtAnimation2 defId="autoAttackPet1Hurt2" playFrameLabel="1" recoverFrameLabel="recover^stop^" />
	<autoAttackAI className="YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetAI1" />
	<petAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="500" />
		<data att="unableAttackMaxInterval" value="2000" />
	</petAttackData>
	<animal id="smallShrimp" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="150" runSpeed="300">
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />
        <idle defId="autoAttackPet1Idle" />
		<walk defId="autoAttackPet1Walk" />
		<run defId="autoAttackPet1Run" />
		<attack defId="autoAttackPet1Attack" />
		<hurt defId="autoAttackPet1Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>
		<hurt2 defId="autoAttackPet1Hurt2" />

		<die defId="autoAttackPet1Die" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="autoAttackPet1Shadow" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>

		<skill id="SkillResurgence" className="YJFY.LevelMode2.Levels1.Skill_AllResurgence"  bodyDefId="skillShow_resurgence"
			x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="300">
		</skill>


		<animationDefinitions>

			<animationDefinition id="autoAttackPet1Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf"
					showClass="IdleOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="autoAttackPet1Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf"
					showClass="WalkOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
            <animationDefinition id="autoAttackPet1Run" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf"
					showClass="RunOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="autoAttackPet1Hurt" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf"
					showClass="Hurt1OfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="autoAttackPet1Hurt2" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf"
					showClass="Hurt2OfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="autoAttackPet1Attack" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf"
					showClass="AttackOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="autoAttackPet1Die" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf"
					showClass="DieOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillShow_resurgence" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf"
					showClass="skill_resurgence" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="autoAttackPet1Shadow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="5"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf"
					showClass="ShadowOfAutomaticPet1" x_offset="0" y_offset="0"  />
			</animationDefinition>
			
			
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="autoAttackPet1Shadow" showSeriesId="d"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet1"
				x_offset="0" y_offset="0" />
			<show defId="autoAttackPet1Shadow" showSeriesId="c"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet2"
				x_offset="0" y_offset="0" />
			<show defId="autoAttackPet1Shadow" showSeriesId="b"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet3"
				x_offset="0" y_offset="0" />
			<show defId="autoAttackPet1Shadow" showSeriesId="a"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet4"
				x_offset="0" y_offset="0" />
			<show defId="autoAttackPet1Shadow" showSeriesId="s"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet5"
				x_offset="0" y_offset="0" />
				
		</shows>

	</animal>
</data>
