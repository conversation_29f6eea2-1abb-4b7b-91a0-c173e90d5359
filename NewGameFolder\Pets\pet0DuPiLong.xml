<?xml version="1.0" encoding="utf-8" ?>
<data>
	 <!--<skillAINeedData>
		<data  priorityForRun="0" priorityForRunInHurt="1" isInvincibleInRun="1" isAbleRunInHurt="1" />
	</skillAINeedData>-->
	<animal id="pet0" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet0Idle" />
		<walk defId="pet0Walk" />
		<run defId="pet0Run" />
		<attack defId="pet0Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet0Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet0Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
		<skill id="Skill_Pet0Skill1" className="YJFY.Skill.PetSkills.Skill_Pet0Skill" x="-480"
			y="-280" z="-1" xRange="960" yRange="560" zRange="100" bodyDefId="pet0SillBodyShow" bodyAttackReachFrameLabel="skillAttackReach" 
			bodySkillEndFrameLabel="skillEnd^stop^"  everyEntityAddShowDefId="pet0SillEffect" 
			everyEntityAddShowIsFrontOfBody="1" skillTimeOfDuration="0" attackInterval="0">
			<animationDefinition id="pet0SillEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet0All.swf"
					showClass="PetSkill0Effect_Attack" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<shakeView swfPath="NewGameFolder/PetSource/Pet0All.swf" className="PetSkill0ShakeView" />
		</skill>
		<skill id="Skill_Pet0Skill2" className="YJFY.Skill.PetSkills.Skill_Pet0Skill" x="-480"
			y="-280" z="-1" xRange="960" yRange="560" zRange="100" bodyDefId="pet0SillBodyShow" bodyAttackReachFrameLabel="skillAttackReach" 
			bodySkillEndFrameLabel="skillEnd^stop^"  everyEntityAddShowDefId="pet0SillEffect" 
			everyEntityAddShowIsFrontOfBody="1" skillTimeOfDuration="0" attackInterval="0">
			<animationDefinition id="pet0SillEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet0All.swf"
					showClass="PetSkill0Effect_Attack" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<shakeView swfPath="NewGameFolder/PetSource/Pet0All.swf" className="PetSkill0ShakeView" />
		</skill>
		<skill id="Skill_Pet0Skill3" className="YJFY.Skill.PetSkills.Skill_Pet0Skill" x="-480"
			y="-280" z="-1" xRange="960" yRange="560" zRange="100" bodyDefId="pet0SillBodyShow" bodyAttackReachFrameLabel="skillAttackReach" 
			bodySkillEndFrameLabel="skillEnd^stop^"  everyEntityAddShowDefId="pet0SillEffect" 
			everyEntityAddShowIsFrontOfBody="1" >
			<animationDefinition id="pet0SillEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet0All.swf"
					showClass="PetSkill0Effect_Attack" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<shakeView swfPath="NewGameFolder/PetSource/Pet0All.swf" className="PetSkill0ShakeView" />
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet0Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet0All.swf"
					showClass="PetStand0_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet0Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet0All.swf"
					showClass="PetWalk0_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet0SillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet0All.swf"
					showClass="PetSkill0Attack_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet0SillFrontShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet0All.swf"
					showClass="PetSkill0Effect_Word_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		    <!--技能攻击效果-->
			<animationDefinition id="pet0SillEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet0All.swf"
					showClass="PetSkill0Effect_Attack" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet0Idle" eqClassName="Pet_Tyrannosaurs_1"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetStand0_1"
				x_offset="0" y_offset="0" />
			<show defId="pet0Walk" eqClassName="Pet_Tyrannosaurs_1"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetWalk0_1"
				x_offset="0" y_offset="0" />
           <show defId="pet0SillBodyShow" eqClassName="Pet_Tyrannosaurs_1"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetSkill0Attack_1"
				x_offset="0" y_offset="0" />
			<show defId="pet0SillFrontShow" eqClassName="Pet_Tyrannosaurs_1"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetSkill0Effect_Word_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet0Idle" eqClassName="Pet_Tyrannosaurs_2"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetStand0_2"
				x_offset="0" y_offset="0" />
			<show defId="pet0Walk" eqClassName="Pet_Tyrannosaurs_2"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetWalk0_2"
				x_offset="0" y_offset="0" />
           <show defId="pet0SillBodyShow" eqClassName="Pet_Tyrannosaurs_2"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetSkill0Attack_2"
				x_offset="0" y_offset="0" />
			<show defId="pet0SillFrontShow" eqClassName="Pet_Tyrannosaurs_2"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetSkill0Effect_Word_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet0Idle" eqClassName="Pet_Tyrannosaurs_3"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetStand0_3"
				x_offset="0" y_offset="0" />
			<show defId="pet0Walk" eqClassName="Pet_Tyrannosaurs_3"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetWalk0_3"
				x_offset="0" y_offset="0" />
           <show defId="pet0SillBodyShow" eqClassName="Pet_Tyrannosaurs_3"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetSkill0Attack_3"
				x_offset="0" y_offset="0" />
			<show defId="pet0SillFrontShow" eqClassName="Pet_Tyrannosaurs_3"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetSkill0Effect_Word_3"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
