<?xml version="1.0" encoding="UTF-8"?>
<Skill>
	<!-- id 类别：主动：1 被动：2 所有者： 无：00 SunWuKong：01 BaiLongMa：02 -->

	<!-- 技能属性说明 -->
	<!-- id : 技能id className： 技能所对应的图片类名 name：秘能名称 level： 技能等级 serialNumber： 
		程序所需的某个属性 initLevel： 该技能能够升级所需人物等级 maxLevel： 该技能的最大等级 description： 技能描述 upgradePrice 
		： 升级价格 owner ： 所有者 type：技能类型 -->
	<!-- 技能类型有一下几种 ： "playerPassive"： 人物被动技能 "playerActive"： 人物主动技能 "petActive"： 
		宠物主动技能 "petPassive"：宠物被动技能 -->




	<!-- 人物被动技能 -->


	<item id="10201000" className="Critical" name="暴击" level="0"
		serialNumber="0" initLevel="3" maxLevel="3" description="有一定概率对敌方造成额外伤害"
		upgradePrice="100" owner="" type="playerPassive" />
	<item id="10201100" className="Critical" name="暴击" level="1"
		serialNumber="0" initLevel="3" maxLevel="3" description="有一定概率对敌方造成额外伤害"
		upgradePrice="200" owner="" type="playerPassive" />
	<item id="10201200" className="Critical" name="暴击" level="2"
		serialNumber="0" initLevel="3" maxLevel="3" description="有一定概率对敌方造成额外伤害"
		upgradePrice="200" owner="" type="playerPassive" />
	<item id="10201300" className="Critical" name="暴击" level="3"
		serialNumber="0" initLevel="3" maxLevel="3" description="有一定概率对敌方造成额外伤害"
		upgradePrice="200" owner="" type="playerPassive" />





	<!-- 人物主动技能 -->

	<!-- 人物主动技能其他属性说明：additionalAttack : 附加伤害 nextAdditinalAttack： 技能下一级的附加伤害（注意这个数值要与下级的数值对应相同） 
		coolDown： 技能冷却时间 nextCoolDown： 技能下一级冷却时间 manaCost： 技能耗魔 nextManaCost: 技能下一级技能耗魔 
		hurt： 技能伤害 nextHurt：技能下一级的伤害 lengthOfTime: 技能持续时间 nextLengthOfTime： 技能下一级持续时间 -->
	<item id="10101000" className="ChangeSkill" name="72变" level="0"
		additionalAttack="0" nextAdditionalAttack="100" serialNumber="3"
		initLevel="10" maxLevel="3" description="悟空拔下一撮毫毛在全屏中每个僵尸面前变幻出一个分身连续击打僵尸。"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="0"
		nextCoolDown="16" manaCost="0" nextManaCost="0" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101100" className="ChangeSkill" name="72变" level="1"
		initLevel="10" additionalAttack="90" nextAdditionalAttack="120"
		serialNumber="3" maxLevel="3" description="悟空拔下一撮毫毛在全屏中每个僵尸面前变幻出一个分身连续击打僵尸。"
		upgradePrice="10000" owner="SunWuKong" type="playerActive" coolDown="16"
		nextCoolDown="25" manaCost="100" nextManaCost="130" hurt="30"
		nextHurt="50" lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101200" className="ChangeSkill" name="72变" level="2"
		additionalAttack="110" nextAdditionalAttack="130" serialNumber="3"
		initLevel="10" maxLevel="3" description="悟空拔下一撮毫毛在全屏中每个僵尸面前变幻出一个分身连续击打僵尸。"
		upgradePrice="20000" owner="SunWuKong" type="playerActive" coolDown="25"
		nextCoolDown="28" manaCost="130" nextManaCost="160" hurt="50"
		nextHurt="100" lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101300" className="ChangeSkill" name="72变" level="3"
		additionalAttack="130" nextAdditionalAttack="0" serialNumber="3"
		initLevel="10" maxLevel="3" description="悟空拔下一撮毫毛在全屏中每个僵尸面前变幻出一个分身连续击打僵尸。"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="28"
		nextCoolDown="0" manaCost="160" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10101001" className="RushSkill" name="冲锋棍" level="0"
		additionalAttack="0" nextAdditionalAttack="300" serialNumber="7"
		initLevel="2" maxLevel="3" description="迅速向前冲击，对沿途僵尸造成伤害并击退。"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="0"
		nextCoolDown="3" manaCost="15" nextManaCost="20" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101101" className="RushSkill" name="冲锋棍" level="1"
		additionalAttack="300" nextAdditionalAttack="400" serialNumber="7"
		initLevel="2" maxLevel="3" description="迅速向前冲击，对沿途僵尸造成伤害并击退。"
		upgradePrice="3000" owner="SunWuKong" type="playerActive" coolDown="3"
		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101201" className="RushSkill" name="冲锋棍" level="2"
		additionalAttack="400" nextAdditionalAttack="500" serialNumber="7"
		initLevel="2" maxLevel="3" description="迅速向前冲击，对沿途僵尸造成伤害并击退。"
		upgradePrice="6000" owner="SunWuKong" type="playerActive" coolDown="3"
		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101301" className="RushSkill" name="冲锋棍" level="3"
		additionalAttack="500" nextAdditionalAttack="0" serialNumber="7"
		initLevel="2" maxLevel="3" description="迅速向前冲击，对沿途僵尸造成伤害并击退。"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="3"
		nextCoolDown="0" manaCost="15" nextManaCost="0" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10101002" className="MeteorStick" name="流星棍" level="0"
		additionalAttack="0" nextAdditionalAttack="50" serialNumber="4"
		initLevel="3" maxLevel="3" description="悟空以肉眼无法看清的速度向前方进行连续的棍击，对前方的僵尸造成大量伤害。"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="0"
		nextCoolDown="5" manaCost="0" nextManaCost="40" hurt="0" nextHurt="15"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101102" className="MeteorStick" name="流星棍" level="1"
		additionalAttack="60" nextAdditionalAttack="70" serialNumber="4"
		initLevel="3" maxLevel="3" description="悟空以肉眼无法看清的速度向前方进行连续的棍击，对前方的僵尸造成大量伤害。"
		upgradePrice="5000" owner="SunWuKong" type="playerActive" coolDown="5"
		nextCoolDown="4" manaCost="30" nextManaCost="35" hurt="5" nextHurt="15"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101202" className="MeteorStick" name="流星棍" level="2"
		additionalAttack="70" nextAdditionalAttack="80" serialNumber="4"
		initLevel="3" maxLevel="3" description="悟空以肉眼无法看清的速度向前方进行连续的棍击，对前方的僵尸造成大量伤害。"
		upgradePrice="10000" owner="SunWuKong" type="playerActive" coolDown="4"
		nextCoolDown="3" manaCost="35" nextManaCost="40" hurt="15" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101302" className="MeteorStick" name="流星棍" level="3"
		additionalAttack="80" nextAdditionalAttack="0" serialNumber="4"
		initLevel="3" maxLevel="3" description="悟空以肉眼无法看清的速度向前方进行连续的棍击，对前方的僵尸造成大量伤害。"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="3"
		nextCoolDown="0" manaCost="40" nextManaCost="0" hurt="30" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10101003" className="Monkey_LieDiGun" name="裂地棍 " level="0"
		additionalAttack="0" nextAdditionalAttack="120" serialNumber="2"
		initLevel="8" maxLevel="3" description="悟空奋力一跃，用尽全身气力劈向地面，对劈打到的僵尸造成成吨伤害！"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="50" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101103" className="Monkey_LieDiGun" name="裂地棍" level="1"
		additionalAttack="120" nextAdditionalAttack="150" serialNumber="2"
		initLevel="8" maxLevel="3" description="悟空奋力一跃，用尽全身气力劈向地面，对劈打到的僵尸造成成吨伤害！"
		upgradePrice="8000" owner="SunWuKong" type="playerActive" coolDown="12"
		nextCoolDown="10" manaCost="50" nextManaCost="60" hurt="200" nextHurt="300"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101203" className="Monkey_LieDiGun" name="裂地棍" level="2"
		additionalAttack="150" nextAdditionalAttack="170" serialNumber="2"
		initLevel="8" maxLevel="3" description="悟空奋力一跃，用尽全身气力劈向地面，对劈打到的僵尸造成成吨伤害！"
		upgradePrice="15000" owner="SunWuKong" type="playerActive" coolDown="10"
		nextCoolDown="8" manaCost="60" nextManaCost="70" hurt="400" nextHurt="600"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101303" className="Monkey_LieDiGun" name="裂地棍" level="3"
		additionalAttack="170" nextAdditionalAttack="0" serialNumber="2"
		initLevel="8" maxLevel="3" description="悟空奋力一跃，用尽全身气力劈向地面，对劈打到的僵尸造成成吨伤害！"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="8"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="600" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />





	<item id="10101004" className="WhirlWindStick" name="旋风棍" level="0"
		additionalAttack="0" nextAdditionalAttack="50" serialNumber="5"
		initLevel="5" maxLevel="3" description="悟空以一根毫毛的代价变幻出一个分身旋转着棍子向前挥舞，击退并持续击打直线上的僵尸。"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="0"
		nextCoolDown="25" manaCost="60" nextManaCost="80" hurt="0" nextHurt="10"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101104" className="WhirlWindStick" name="旋风棍" level="1"
		additionalAttack="50" nextAdditionalAttack="60" serialNumber="5"
		initLevel="5" maxLevel="3" description="悟空以一根毫毛的代价变幻出一个分身旋转着棍子向前挥舞，击退并持续击打直线上的僵尸。"
		upgradePrice="5000" owner="SunWuKong" type="playerActive" coolDown="25"
		nextCoolDown="23" manaCost="60" nextManaCost="80" hurt="10" nextHurt="20"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101204" className="WhirlWindStick" name="旋风棍" level="2"
		additionalAttack="50" nextAdditionalAttack="70" serialNumber="5"
		initLevel="5" maxLevel="3" description="悟空以一根毫毛的代价变幻出一个分身旋转着棍子向前挥舞，击退并持续击打直线上的僵尸。"
		upgradePrice="8000" owner="SunWuKong" type="playerActive" coolDown="23"
		nextCoolDown="22" manaCost="80" nextManaCost="100" hurt="20" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10101304" className="WhirlWindStick" name="旋风棍" level="3"
		additionalAttack="70" nextAdditionalAttack="0" serialNumber="5"
		initLevel="5" maxLevel="3" description="悟空以一根毫毛的代价变幻出一个分身旋转着棍子向前挥舞，击退并持续击打直线上的僵尸。"
		upgradePrice="0" owner="SunWuKong" type="playerActive" coolDown="20"
		nextCoolDown="0" manaCost="100" nextManaCost="0" hurt="30" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />








	<item id="10102000" className="Dragon_HeiLongBo" name="黑龙波" level="0"
		additionalAttack="0" nextAdditionalAttack="100" serialNumber="7"
		initLevel="2" maxLevel="3" description="白龙马聚集周围的能量然后发射出黑龙形态的冲击波，冲击直线上的敌人。"
		upgradePrice="0" owner="BaiLongMa" type="playerActive" coolDown="0"
		nextCoolDown="0" manaCost="0" nextManaCost="20" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102100" className="Dragon_HeiLongBo" name="黑龙波" level="1"
		additionalAttack="20" nextAdditionalAttack="25" serialNumber="7"
		initLevel="2" maxLevel="3" description="白龙马聚集周围的能量然后发射出黑龙形态的冲击波，冲击直线上的敌人。"
		upgradePrice="3000" owner="BaiLongMa" type="playerActive" coolDown="0"
		nextCoolDown="0" manaCost="50" nextManaCost="50" hurt="10" nextHurt="20"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102200" className="Dragon_HeiLongBo" name="黑龙波" level="2"
		additionalAttack="25" nextAdditionalAttack="30" serialNumber="7"
		initLevel="2" maxLevel="3" description="白龙马聚集周围的能量然后发射出黑龙形态的冲击波，冲击直线上的敌人。"
		upgradePrice="5000" owner="BaiLongMa" type="playerActive" coolDown="0"
		nextCoolDown="0" manaCost="50" nextManaCost="50" hurt="50" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102300" className="Dragon_HeiLongBo" name="黑龙波" level="3"
		additionalAttack="30" nextAdditionalAttack="0" serialNumber="7"
		initLevel="2" maxLevel="3" description="白龙马聚集周围的能量然后发射出黑龙形态的冲击波，冲击直线上的敌人。"
		upgradePrice="0" owner="BaiLongMa" type="playerActive" coolDown="0"
		nextCoolDown="0" manaCost="50" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10102001" className="Dragon_MoYuBo" name="魔鱼波" level="0"
		additionalAttack="0" nextAdditionalAttack="80" serialNumber="2"
		initLevel="3" maxLevel="3" description="召唤东海魔鱼向前方怒吼，对前方僵尸造成伤害并击退它们。"
		upgradePrice="0" owner="BaiLongMa" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="40" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102101" className="Dragon_MoYuBo" name="魔鱼波" level="1"
		additionalAttack="80" nextAdditionalAttack="100" serialNumber="2"
		initLevel="3" maxLevel="3" description="召唤东海魔鱼向前方怒吼，对前方僵尸造成伤害并击退它们。"
		upgradePrice="5000" owner="BaiLongMa" type="playerActive" coolDown="12"
		nextCoolDown="10" manaCost="40" nextManaCost="50" hurt="50" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102201" className="Dragon_MoYuBo" name="魔鱼波" level="2"
		additionalAttack="100" nextAdditionalAttack="120" serialNumber="2"
		initLevel="3" maxLevel="3" description="召唤东海魔鱼向前方怒吼，对前方僵尸造成伤害并击退它们。"
		upgradePrice="10000" owner="BaiLongMa" type="playerActive" coolDown="10"
		nextCoolDown="8" manaCost="50" nextManaCost="60" hurt="100" nextHurt="200"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102301" className="Dragon_MoYuBo" name="魔鱼波" level="3"
		additionalAttack="120" nextAdditionalAttack="0" serialNumber="2"
		initLevel="3" maxLevel="3" description="召唤东海魔鱼向前方怒吼，对前方僵尸造成伤害并击退它们。"
		upgradePrice="0" owner="BaiLongMa" type="playerActive" coolDown="8"
		nextCoolDown="0" manaCost="60" nextManaCost="0" hurt="200" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10102002" className="Dragon_BingZhuiCi" name="冰锥刺"
		level="0" additionalAttack="0" nextAdditionalAttack="100"
		serialNumber="4" initLevel="5" maxLevel="3" description="召唤冰锥在每个僵尸的脚下突然刺起，使僵尸眩晕一段时间。"
		upgradePrice="0" owner="BaiLongMa" type="playerActive" coolDown="0"
		nextCoolDown="18" manaCost="0" nextManaCost="50" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="4" />
	<item id="10102102" className="Dragon_BingZhuiCi" name="冰锥刺"
		level="1" additionalAttack="0" nextAdditionalAttack="0" serialNumber="4"
		initLevel="5" maxLevel="3" description="召唤冰锥在每个僵尸的脚下突然刺起，使僵尸眩晕一段时间。"
		upgradePrice="4000" owner="BaiLongMa" type="playerActive" coolDown="20"
		nextCoolDown="20" manaCost="50" nextManaCost="60" hurt="0" nextHurt="0"
		lengthOfTime="6" nextLengthOfTime="8" />
	<item id="10102202" className="Dragon_BingZhuiCi" name="冰锥刺"
		level="2" additionalAttack="0" nextAdditionalAttack="0" serialNumber="4"
		initLevel="5" maxLevel="3" description="召唤冰锥在每个僵尸的脚下突然刺起，使僵尸眩晕一段时间。"
		upgradePrice="9000" owner="BaiLongMa" type="playerActive" coolDown="20"
		nextCoolDown="20" manaCost="60" nextManaCost="70" hurt="0" nextHurt="0"
		lengthOfTime="8" nextLengthOfTime="10" />
	<item id="10102302" className="Dragon_BingZhuiCi" name="冰锥刺"
		level="3" additionalAttack="0" nextAdditionalAttack="0" serialNumber="4"
		initLevel="5" maxLevel="3" description="召唤冰锥在每个僵尸的脚下突然刺起，使僵尸眩晕一段时间。"
		upgradePrice="0" owner="BaiLongMa" type="playerActive" coolDown="20"
		nextCoolDown="0" manaCost="60" nextManaCost="0" hurt="0" nextHurt="0"
		lengthOfTime="10" nextLengthOfTime="0" />



	<item id="10102003" className="Dragon_QianNiao" name="千鸟" level="0"
		additionalAttack="0" nextAdditionalAttack="60" serialNumber="5"
		initLevel="8" maxLevel="3" description="召唤出成百上千只受到惊吓的赤焰鸟对范围内的僵尸疯狂轰炸。"
		upgradePrice="0" owner="BaiLongMa" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="50" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102103" className="Dragon_QianNiao" name="千鸟" level="1"
		additionalAttack="60" nextAdditionalAttack="80" serialNumber="5"
		initLevel="8" maxLevel="3" description="召唤出成百上千只受到惊吓的赤焰鸟对范围内的僵尸疯狂轰炸。"
		upgradePrice="5000" owner="BaiLongMa" type="playerActive" coolDown="18"
		nextCoolDown="17" manaCost="50" nextManaCost="60" hurt="10" nextHurt="25"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102203" className="Dragon_QianNiao" name="千鸟" level="2"
		additionalAttack="80" nextAdditionalAttack="100" serialNumber="5"
		initLevel="8" maxLevel="3" description="召唤出成百上千只受到惊吓的赤焰鸟对范围内的僵尸疯狂轰炸。"
		upgradePrice="10000" owner="BaiLongMa" type="playerActive" coolDown="17"
		nextCoolDown="16" manaCost="60" nextManaCost="70" hurt="25" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102303" className="Dragon_QianNiao" name="千鸟" level="3"
		additionalAttack="100" nextAdditionalAttack="0" serialNumber="5"
		initLevel="8" maxLevel="3" description="召唤出成百上千只受到惊吓的赤焰鸟对范围内的僵尸疯狂轰炸。"
		upgradePrice="0" owner="BaiLongMa" type="playerActive" coolDown="16"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="50" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />




	<item id="10102004" className="Dragon_MoLongTeng" name="魔龙腾"
		level="0" additionalAttack="0" nextAdditionalAttack="105"
		serialNumber="3" initLevel="10" maxLevel="3"
		description="白龙马召唤两条混世魔龙从地面升腾而起，震慑全屏僵尸并造成大量伤害。" upgradePrice="0"
		owner="BaiLongMa" type="playerActive" coolDown="25" nextCoolDown="16"
		manaCost="0" nextManaCost="80" hurt="0" nextHurt="100" lengthOfTime="0"
		nextLengthOfTime="0" />
	<item id="10102104" className="Dragon_MoLongTeng" name="魔龙腾"
		level="1" additionalAttack="105" nextAdditionalAttack="120"
		serialNumber="3" initLevel="10" maxLevel="3"
		description="白龙马召唤两条混世魔龙从地面升腾而起，震慑全屏僵尸并造成大量伤害。" upgradePrice="10000"
		owner="BaiLongMa" type="playerActive" coolDown="25" nextCoolDown="22"
		manaCost="100" nextManaCost="130" hurt="10" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102204" className="Dragon_MoLongTeng" name="魔龙腾"
		level="2" additionalAttack="120" nextAdditionalAttack="150"
		serialNumber="3" initLevel="10" maxLevel="3"
		description="白龙马召唤两条混世魔龙从地面升腾而起，震慑全屏僵尸并造成大量伤害。" upgradePrice="20000"
		owner="BaiLongMa" type="playerActive" coolDown="22" nextCoolDown="20"
		manaCost="130" nextManaCost="160" hurt="30" nextHurt="70"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10102304" className="Dragon_MoLongTeng" name="魔龙腾"
		level="3" additionalAttack="150" nextAdditionalAttack="0"
		serialNumber="3" initLevel="10" maxLevel="3"
		description="白龙马召唤两条混世魔龙从地面升腾而起，震慑全屏僵尸并造成大量伤害。" upgradePrice="0"
		owner="BaiLongMa" type="playerActive" coolDown="20" nextCoolDown="0"
		manaCost="160" nextManaCost="0" hurt="70" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />


	<!-- 后羿技能 10108000-->
	<item id="10108000" className="HeiLongBo_Houyi" name="焱羽箭" level="0"
		additionalAttack="0" nextAdditionalAttack="120" serialNumber="7"
		initLevel="2" maxLevel="3" description="后羿射出蕴含火之力量的箭矢，在命中敌人后爆炸，对范围内的敌人造成伤害。"
		upgradePrice="0" owner="Houyi" type="playerActive" coolDown="0"
		nextCoolDown="0" manaCost="0" nextManaCost="20" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108100" className="HeiLongBo_Houyi" name="焱羽箭" level="1"
		additionalAttack="120" nextAdditionalAttack="160" serialNumber="7"
		initLevel="2" maxLevel="3" description="后羿射出蕴含火之力量的箭矢，在命中敌人后爆炸，对范围内的敌人造成伤害。"
		upgradePrice="3000" owner="Houyi" type="playerActive" coolDown="0"
		nextCoolDown="0" manaCost="50" nextManaCost="50" hurt="100" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108200" className="HeiLongBo_Houyi" name="焱羽箭" level="2"
		additionalAttack="160" nextAdditionalAttack="200" serialNumber="7"
		initLevel="2" maxLevel="3" description="后羿射出蕴含火之力量的箭矢，在命中敌人后爆炸，对范围内的敌人造成伤害。"
		upgradePrice="5000" owner="Houyi" type="playerActive" coolDown="0"
		nextCoolDown="0" manaCost="50" nextManaCost="50" hurt="150" nextHurt="200"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108300" className="HeiLongBo_Houyi" name="焱羽箭" level="3"
		additionalAttack="200" nextAdditionalAttack="0" serialNumber="7"
		initLevel="2" maxLevel="3" description="后羿射出蕴含火之力量的箭矢，在命中敌人后爆炸，对范围内的敌人造成伤害。"
		upgradePrice="0" owner="Houyi" type="playerActive" coolDown="0"
		nextCoolDown="0" manaCost="50" nextManaCost="0" hurt="200" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10108001" className="Dragon_MoYuBo_Houyi" name="弹击" level="0"
		additionalAttack="0" nextAdditionalAttack="140" serialNumber="2"
		initLevel="3" maxLevel="3" description="后羿挥舞弓箭弹击前方敌人，受到攻击的敌人被弹飞一段距离。"
		upgradePrice="0" owner="Houyi" type="playerActive" coolDown="0"
		nextCoolDown="16" manaCost="0" nextManaCost="40" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108101" className="Dragon_MoYuBo_Houyi" name="弹击" level="1"
		additionalAttack="140" nextAdditionalAttack="180" serialNumber="2"
		initLevel="3" maxLevel="3" description="后羿挥舞弓箭弹击前方敌人，受到攻击的敌人被弹飞一段距离。"
		upgradePrice="5000" owner="Houyi" type="playerActive" coolDown="16"
		nextCoolDown="15" manaCost="40" nextManaCost="50" hurt="100" nextHurt="200"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108201" className="Dragon_MoYuBo_Houyi" name="弹击" level="2"
		additionalAttack="180" nextAdditionalAttack="220" serialNumber="2"
		initLevel="3" maxLevel="3" description="后羿挥舞弓箭弹击前方敌人，受到攻击的敌人被弹飞一段距离。"
		upgradePrice="10000" owner="Houyi" type="playerActive" coolDown="15"
		nextCoolDown="14" manaCost="50" nextManaCost="60" hurt="200" nextHurt="300"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108301" className="Dragon_MoYuBo_Houyi" name="弹击" level="3"
		additionalAttack="220" nextAdditionalAttack="0" serialNumber="2"
		initLevel="3" maxLevel="3" description="后羿挥舞弓箭弹击前方敌人，受到攻击的敌人被弹飞一段距离。"
		upgradePrice="0" owner="Houyi" type="playerActive" coolDown="14"
		nextCoolDown="0" manaCost="60" nextManaCost="0" hurt="300" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10108002" className="Dragon_BingZhuiCi_Houyi" name="多重箭"
		level="0" additionalAttack="0" nextAdditionalAttack="100"
		serialNumber="4" initLevel="5" maxLevel="3" description="后羿搭载多只箭矢一次射出，对扇形区域敌人造成多段伤害。"
		upgradePrice="0" owner="Houyi" type="playerActive" coolDown="0"
		nextCoolDown="11" manaCost="0" nextManaCost="50" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="4" />
	<item id="10108102" className="Dragon_BingZhuiCi_Houyi" name="多重箭"
		level="1" additionalAttack="100" nextAdditionalAttack="130" serialNumber="4"
		initLevel="5" maxLevel="3" description="后羿搭载多只箭矢一次射出，对扇形区域敌人造成多段伤害。"
		upgradePrice="4000" owner="Houyi" type="playerActive" coolDown="11"
		nextCoolDown="10" manaCost="50" nextManaCost="60" hurt="100" nextHurt="200"
		lengthOfTime="6" nextLengthOfTime="8" />
	<item id="10108202" className="Dragon_BingZhuiCi_Houyi" name="多重箭"
		level="2" additionalAttack="130" nextAdditionalAttack="180" serialNumber="4"
		initLevel="5" maxLevel="3" description="后羿搭载多只箭矢一次射出，对扇形区域敌人造成多段伤害。"
		upgradePrice="9000" owner="Houyi" type="playerActive" coolDown="10"
		nextCoolDown="9" manaCost="60" nextManaCost="70" hurt="200" nextHurt="300"
		lengthOfTime="8" nextLengthOfTime="10" />
	<item id="10108302" className="Dragon_BingZhuiCi_Houyi" name="多重箭"
		level="3" additionalAttack="180" nextAdditionalAttack="0" serialNumber="4"
		initLevel="5" maxLevel="3" description="后羿搭载多只箭矢一次射出，对扇形区域敌人造成多段伤害。"
		upgradePrice="0" owner="Houyi" type="playerActive" coolDown="9"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="300" nextHurt="0"
		lengthOfTime="10" nextLengthOfTime="0" />



	<item id="10108003" className="Dragon_QianNiao_Houyi" name="穿透箭" level="0"
		additionalAttack="0" nextAdditionalAttack="150" serialNumber="5"
		initLevel="8" maxLevel="3" description="后羿全力挽弓射出巨大箭矢，对直线上的敌人造成大量伤害。"
		upgradePrice="0" owner="Houyi" type="playerActive" coolDown="0"
		nextCoolDown="1" manaCost="0" nextManaCost="50" hurt="0" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108103" className="Dragon_QianNiao_Houyi" name="穿透箭" level="1"
		additionalAttack="150" nextAdditionalAttack="230" serialNumber="5"
		initLevel="8" maxLevel="3" description="后羿全力挽弓射出巨大箭矢，对直线上的敌人造成大量伤害。"
		upgradePrice="5000" owner="Houyi" type="playerActive" coolDown="1"
		nextCoolDown="17" manaCost="50" nextManaCost="70" hurt="150" nextHurt="250"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108203" className="Dragon_QianNiao_Houyi" name="穿透箭" level="2"
		additionalAttack="230" nextAdditionalAttack="360" serialNumber="5"
		initLevel="8" maxLevel="3" description="后羿全力挽弓射出巨大箭矢，对直线上的敌人造成大量伤害。"
		upgradePrice="10000" owner="Houyi" type="playerActive" coolDown="17"
		nextCoolDown="16" manaCost="70" nextManaCost="90" hurt="250" nextHurt="350"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108303" className="Dragon_QianNiao_Houyi" name="穿透箭" level="3"
		additionalAttack="360" nextAdditionalAttack="0" serialNumber="5"
		initLevel="8" maxLevel="3" description="后羿全力挽弓射出巨大箭矢，对直线上的敌人造成大量伤害。"
		upgradePrice="0" owner="Houyi" type="playerActive" coolDown="16"
		nextCoolDown="0" manaCost="90" nextManaCost="0" hurt="350" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />




	<item id="10108004" className="Dragon_MoLongTeng_Houyi" name="逐日神箭"
		level="0" additionalAttack="0" nextAdditionalAttack="108"
		serialNumber="3" initLevel="10" maxLevel="3"
		description="后羿聚集全身力量向天空射出九支神箭，射落九个太阳，太阳陨落地面对地表敌人进行九次爆裂轰击。造成伤害并击退敌人。" upgradePrice="0"
		owner="Houyi" type="playerActive" coolDown="32" nextCoolDown="30"
		manaCost="0" nextManaCost="100" hurt="100" nextHurt="200" lengthOfTime="0"
		nextLengthOfTime="0" />
	<item id="10108104" className="Dragon_MoLongTeng_Houyi" name="逐日神箭"
		level="1" additionalAttack="108" nextAdditionalAttack="168"
		serialNumber="3" initLevel="10" maxLevel="3"
		description="后羿聚集全身力量向天空射出九支神箭，射落九个太阳，太阳陨落地面对地表敌人进行九次爆裂轰击。造成伤害并击退敌人。" upgradePrice="10000"
		owner="Houyi" type="playerActive" coolDown="30" nextCoolDown="28"
		manaCost="100" nextManaCost="130" hurt="200" nextHurt="300"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108204" className="Dragon_MoLongTeng_Houyi" name="逐日神箭"
		level="2" additionalAttack="168" nextAdditionalAttack="236"
		serialNumber="3" initLevel="10" maxLevel="3"
		description="后羿聚集全身力量向天空射出九支神箭，射落九个太阳，太阳陨落地面对地表敌人进行九次爆裂轰击。造成伤害并击退敌人。" upgradePrice="20000"
		owner="Houyi" type="playerActive" coolDown="28" nextCoolDown="26"
		manaCost="130" nextManaCost="180" hurt="300" nextHurt="400"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10108304" className="Dragon_MoLongTeng_Houyi" name="逐日神箭"
		level="3" additionalAttack="236" nextAdditionalAttack="0"
		serialNumber="3" initLevel="10" maxLevel="3"
		description="后羿聚集全身力量向天空射出九支神箭，射落九个太阳，太阳陨落地面对地表敌人进行九次爆裂轰击。造成伤害并击退敌人。" upgradePrice="0"
		owner="Houyi" type="playerActive" coolDown="26" nextCoolDown="0"
		manaCost="180" nextManaCost="0" hurt="400" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />

	<!-- 人物主动技能其他属性说明：additionalAttack : 附加伤害 nextAdditinalAttack： 技能下一级的附加伤害（注意这个数值要与下级的数值对应相同） 
		coolDown： 技能冷却时间 nextCoolDown： 技能下一级冷却时间 manaCost： 技能耗魔 nextManaCost: 技能下一级技能耗魔 
		hurt： 技能伤害 nextHurt：技能下一级的伤害 lengthOfTime: 技能持续时间 nextLengthOfTime： 技能下一级持续时间 -->

	<!--二郎神技能 -->
	<item id="10104000" className="ErLangShen_XiaoTianXi" name="哮天袭"
		level="0" additionalAttack="0" nextAdditionalAttack="100"
		serialNumber="7" initLevel="2" maxLevel="3" description="向前猛冲召唤哮天犬袭击敌人，造成伤害并击退它们。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="3"

		nextCoolDown="0" manaCost="0" nextManaCost="20" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104100" className="ErLangShen_XiaoTianXi" name="哮天袭"
		level="1" additionalAttack="250" nextAdditionalAttack="350"
		serialNumber="7" initLevel="2" maxLevel="3" description="向前猛冲召唤哮天犬袭击敌人，造成伤害并击退它们。"
		upgradePrice="3000" owner="ErLangShen" type="playerActive" coolDown="3"
		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104200" className="ErLangShen_XiaoTianXi" name="哮天袭"
		level="2" additionalAttack="350" nextAdditionalAttack="450"
		serialNumber="7" initLevel="2" maxLevel="3" description="向前猛冲召唤哮天犬袭击敌人，造成伤害并击退它们。"
		upgradePrice="6000" owner="ErLangShen" type="playerActive" coolDown="3"

		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104300" className="ErLangShen_XiaoTianXi" name="哮天袭"
		level="3" additionalAttack="450" nextAdditionalAttack="0"
		serialNumber="7" initLevel="2" maxLevel="3" description="向前猛冲召唤哮天犬袭击敌人，造成伤害并击退它们。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="3"

		nextCoolDown="0" manaCost="15" nextManaCost="0" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10104001" className="ErLangShen_XuanWuJian" name="旋舞剑"
		level="0" additionalAttack="70" nextAdditionalAttack="70"
		serialNumber="2" initLevel="3" maxLevel="3" description="双手握紧长剑原地极速的旋转，扫击周围敌人。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="40" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104101" className="ErLangShen_XuanWuJian" name="旋舞剑"
		level="1" additionalAttack="70" nextAdditionalAttack="80"
		serialNumber="2" initLevel="3" maxLevel="3" description="双手握紧长剑原地极速的旋转，扫击周围敌人。"
		upgradePrice="8000" owner="ErLangShen" type="playerActive" coolDown="8"
		nextCoolDown="7" manaCost="50" nextManaCost="60" hurt="5" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104201" className="ErLangShen_XuanWuJian" name="旋舞剑"
		level="2" additionalAttack="80" nextAdditionalAttack="100"
		serialNumber="2" initLevel="3" maxLevel="3" description="双手握紧长剑原地极速的旋转，扫击周围敌人。"
		upgradePrice="15000" owner="ErLangShen" type="playerActive" coolDown="7"
		nextCoolDown="6" manaCost="60" nextManaCost="70" hurt="30" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104301" className="ErLangShen_XuanWuJian" name="旋舞剑"
		level="3" additionalAttack="100" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="双手握紧长剑原地极速的旋转，扫击周围敌人。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="6"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10104002" className="ErLangShen_JiGuangJian" name="极光剑"
		level="0" additionalAttack="0" nextAdditionalAttack="60" serialNumber="3"
		initLevel="5" maxLevel="3" description="杨戬利用天眼将手中的剑变成极光之剑向前方斩去，威力惊天。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="0"
		nextCoolDown="18" manaCost="0" nextManaCost="50" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="4" />
	<item id="10104102" className="ErLangShen_JiGuangJian" name="极光剑"
		level="1" additionalAttack="60" nextAdditionalAttack="75"
		serialNumber="3" initLevel="5" maxLevel="3" description="杨戬利用天眼将手中的剑变成极光之剑向前方斩去，威力惊天。"
		upgradePrice="8000" owner="ErLangShen" type="playerActive" coolDown="25"
		nextCoolDown="24" manaCost="70" nextManaCost="80" hurt="15" nextHurt="60"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104202" className="ErLangShen_JiGuangJian" name="极光剑"
		level="2" additionalAttack="75" nextAdditionalAttack="105"
		serialNumber="3" initLevel="5" maxLevel="3" description="杨戬利用天眼将手中的剑变成极光之剑向前方斩去，威力惊天。"
		upgradePrice="10000" owner="ErLangShen" type="playerActive" coolDown="24"
		nextCoolDown="23" manaCost="80" nextManaCost="90" hurt="60" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104302" className="ErLangShen_JiGuangJian" name="极光剑"
		level="3" additionalAttack="105" nextAdditionalAttack="0"
		serialNumber="3" initLevel="5" maxLevel="3" description="杨戬利用天眼将手中的剑变成极光之剑向前方斩去，威力惊天。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="23"
		nextCoolDown="0" manaCost="90" nextManaCost="0" hurt="150" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10104003" className="ErLangShen_YingLiuZhang" name="影流斩"
		level="0" additionalAttack="0" nextAdditionalAttack="70" serialNumber="4"
		initLevel="8" maxLevel="3" description="对前方敌人发动多次连斩并打出一道不可抵挡的剑气。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="50" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104103" className="ErLangShen_YingLiuZhang" name="影流斩"
		level="1" additionalAttack="70" nextAdditionalAttack="80"
		serialNumber="4" initLevel="8" maxLevel="3" description="对前方敌人发动多次连斩并打出一道不可抵挡的剑气。"
		upgradePrice="6000" owner="ErLangShen" type="playerActive" coolDown="18"
		nextCoolDown="15" manaCost="60" nextManaCost="70" hurt="20" nextHurt="70 "
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104203" className="ErLangShen_YingLiuZhang" name="影流斩"
		level="2" additionalAttack="80" nextAdditionalAttack="90"
		serialNumber="4" initLevel="8" maxLevel="3" description="对前方敌人发动多次连斩并打出一道不可抵挡的剑气。"
		upgradePrice="8000" owner="ErLangShen" type="playerActive" coolDown="15"
		nextCoolDown="12" manaCost="70" nextManaCost="80" hurt="70" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104303" className="ErLangShen_YingLiuZhang" name="影流斩"
		level="3" additionalAttack="90" nextAdditionalAttack="0" serialNumber="4"
		initLevel="8" maxLevel="3" description="对前方敌人发动多次连斩并打出一道不可抵挡的剑气。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="12"
		nextCoolDown="0" manaCost="80" nextManaCost="0" hurt="150" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />




	<item id="10104004" className="ErLangShen_WangJianZhong" name="万剑冢"
		level="0" additionalAttack="0" nextAdditionalAttack="50" serialNumber="5"
		initLevel="10" maxLevel="3" description="万剑之冢，每把剑化作一次影袭对所有敌人造成伤害。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="25"
		nextCoolDown="16" manaCost="0" nextManaCost="80" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104104" className="ErLangShen_WangJianZhong" name="万剑冢"
		level="1" additionalAttack="50" nextAdditionalAttack="70"
		serialNumber="5" initLevel="10" maxLevel="3" description="万剑之冢，每把剑化作一次影袭对所有敌人造成伤害。"
		upgradePrice="10000" owner="ErLangShen" type="playerActive" coolDown="30"
		nextCoolDown="28" manaCost="100" nextManaCost="130" hurt="30"
		nextHurt="50" lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104204" className="ErLangShen_WangJianZhong" name="万剑冢"
		level="2" additionalAttack="70" nextAdditionalAttack="100"
		serialNumber="5" initLevel="10" maxLevel="3" description="万剑之冢，每把剑化作一次影袭对所有敌人造成伤害。"
		upgradePrice="20000" owner="ErLangShen" type="playerActive" coolDown="28"
		nextCoolDown="25" manaCost="130" nextManaCost="160" hurt="50"
		nextHurt="100" lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10104304" className="ErLangShen_WangJianZhong" name="万剑冢"
		level="3" additionalAttack="100" nextAdditionalAttack="0"
		serialNumber="5" initLevel="10" maxLevel="3" description="万剑之冢，每把剑化作一次影袭对所有敌人造成伤害。"
		upgradePrice="0" owner="ErLangShen" type="playerActive" coolDown="25"
		nextCoolDown="0" manaCost="160" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<!-- 人物主动技能其他属性说明：additionalAttack : 附加伤害 nextAdditinalAttack： 技能下一级的附加伤害（注意这个数值要与下级的数值对应相同） 
		coolDown： 技能冷却时间 nextCoolDown： 技能下一级冷却时间 manaCost： 技能耗魔 nextManaCost: 技能下一级技能耗魔 
		hurt： 技能伤害 nextHurt：技能下一级的伤害 lengthOfTime: 技能持续时间 nextLengthOfTime： 技能下一级持续时间 -->


	<!--嫦娥技能 -->
	<item id="10105000" className="ChangE_ChongFengPao" name="冲锋炮"
		level="0" additionalAttack="0" nextAdditionalAttack="100"
		serialNumber="7" initLevel="2" maxLevel="3" description="利用大炮的推进力，向前猛冲敌人，造成伤害并击退它们。"
		upgradePrice="0" owner="ChangE" type="playerActive" coolDown="3"
		nextCoolDown="0" manaCost="0" nextManaCost="20" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105100" className="ChangE_ChongFengPao" name="冲锋炮"
		level="1" additionalAttack="150" nextAdditionalAttack="350"
		serialNumber="7" initLevel="2" maxLevel="3" description="利用大炮的推进力，向前猛冲敌人，造成伤害并击退它们。"
		upgradePrice="3000" owner="ChangE" type="playerActive" coolDown="3"
		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105200" className="ChangE_ChongFengPao" name="冲锋炮"
		level="2" additionalAttack="350" nextAdditionalAttack="450"
		serialNumber="7" initLevel="2" maxLevel="3" description="利用大炮的推进力，向前猛冲敌人，造成伤害并击退它们。"
		upgradePrice="6000" owner="ChangE" type="playerActive" coolDown="3"
		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105300" className="ChangE_ChongFengPao" name="冲锋炮"
		level="3" additionalAttack="450" nextAdditionalAttack="0"
		serialNumber="7" initLevel="2" maxLevel="3" description="利用大炮的推进力，向前猛冲敌人，造成伤害并击退它们。"
		upgradePrice="0" owner="ChangE" type="playerActive" coolDown="3"
		nextCoolDown="0" manaCost="15" nextManaCost="0" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10105001" className="ChangE_HuoJianPao" name="火箭炮"
		level="0" additionalAttack="70" nextAdditionalAttack="50"
		serialNumber="2" initLevel="3" maxLevel="3" description="使用火箭炮轰炸前方一小段距离的敌人，威力强大。"
		upgradePrice="0" owner="ChangE" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="40" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105101" className="ChangE_HuoJianPao" name="火箭炮"
		level="1" additionalAttack="250" nextAdditionalAttack="350"
		serialNumber="2" initLevel="3" maxLevel="3" description="使用火箭炮轰炸前方一小段距离的敌人，威力强大。"
		upgradePrice="8000" owner="ChangE" type="playerActive" coolDown="8"
		nextCoolDown="7" manaCost="50" nextManaCost="60" hurt="5" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105201" className="ChangE_HuoJianPao" name="火箭炮"
		level="2" additionalAttack="350" nextAdditionalAttack="450"
		serialNumber="2" initLevel="3" maxLevel="3" description="使用火箭炮轰炸前方一小段距离的敌人，威力强大。"
		upgradePrice="15000" owner="ChangE" type="playerActive" coolDown="7"
		nextCoolDown="6" manaCost="60" nextManaCost="70" hurt="30" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105301" className="ChangE_HuoJianPao" name="火箭炮"
		level="3" additionalAttack="450" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="使用火箭炮轰炸前方一小段距离的敌人，威力强大。"
		upgradePrice="0" owner="ChangE" type="playerActive" coolDown="6"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10105002" className="ChangE_FengKuangSaoShe" name="疯狂射击"
		level="0" additionalAttack="0" nextAdditionalAttack="100"
		serialNumber="3" initLevel="5" maxLevel="3"
		description="嫦娥手中的手枪变成双枪，武动双枪，对周围敌人发出强威力的乱射袭击。" upgradePrice="0"
		owner="ChangE" type="playerActive" coolDown="0" nextCoolDown="9"
		manaCost="0" nextManaCost="50" hurt="0" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="4" />
	<item id="10105102" className="ChangE_FengKuangSaoShe" name="疯狂射击"
		level="1" additionalAttack="70" nextAdditionalAttack="80"
		serialNumber="3" initLevel="5" maxLevel="3"
		description="嫦娥手中的手枪变成双枪，武动双枪，对周围敌人发出强威力的乱射袭击。" upgradePrice="8000"
		owner="ChangE" type="playerActive" coolDown="9" nextCoolDown="8"
		manaCost="50" nextManaCost="60" hurt="15" nextHurt="60" lengthOfTime="0"
		nextLengthOfTime="0" />
	<item id="10105202" className="ChangE_FengKuangSaoShe" name="疯狂射击"
		level="2" additionalAttack="80" nextAdditionalAttack="100"
		serialNumber="3" initLevel="5" maxLevel="3"
		description="嫦娥手中的手枪变成双枪，武动双枪，对周围敌人发出强威力的乱射袭击。" upgradePrice="10000"
		owner="ChangE" type="playerActive" coolDown="8" nextCoolDown="7"
		manaCost="60" nextManaCost="70" hurt="60" nextHurt="150" lengthOfTime="0"
		nextLengthOfTime="0" />
	<item id="10105302" className="ChangE_FengKuangSaoShe" name="疯狂射击"
		level="3" additionalAttack="100" nextAdditionalAttack="0"
		serialNumber="3" initLevel="5" maxLevel="3"
		description="嫦娥手中的手枪变成双枪，武动双枪，对周围敌人发出强威力的乱射袭击。" upgradePrice="0"
		owner="ChangE" type="playerActive" coolDown="7" nextCoolDown="0"
		manaCost="70" nextManaCost="0" hurt="150" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />



	<item id="10105003" className="ChangE_JiGuangHuoPao" name="激光火炮"
		level="0" additionalAttack="0" nextAdditionalAttack="70" serialNumber="4"
		initLevel="8" maxLevel="3" description="使用威力巨大的激光炮向前方的敌人发射激光并击退敌人。"
		upgradePrice="0" owner="ChangE" type="playerActive" coolDown="0"
		nextCoolDown="17" manaCost="0" nextManaCost="60" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105103" className="ChangE_JiGuangHuoPao" name="激光火炮"
		level="1" additionalAttack="70" nextAdditionalAttack="80"
		serialNumber="4" initLevel="8" maxLevel="3" description="使用威力巨大的激光炮向前方的敌人发射激光并击退敌人。"
		upgradePrice="6000" owner="ChangE" type="playerActive" coolDown="17"
		nextCoolDown="16" manaCost="60" nextManaCost="70" hurt="20" nextHurt="70 "
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105203" className="ChangE_JiGuangHuoPao" name="激光火炮"
		level="2" additionalAttack="80" nextAdditionalAttack="100"
		serialNumber="4" initLevel="8" maxLevel="3" description="使用威力巨大的激光炮向前方的敌人发射激光并击退敌人。"
		upgradePrice="8000" owner="ChangE" type="playerActive" coolDown="16"
		nextCoolDown="15" manaCost="70" nextManaCost="80" hurt="70" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105303" className="ChangE_JiGuangHuoPao" name="激光火炮"
		level="3" additionalAttack="100" nextAdditionalAttack="0"
		serialNumber="4" initLevel="8" maxLevel="3" description="使用威力巨大的激光炮向前方的敌人发射激光并击退敌人。"
		upgradePrice="0" owner="ChangE" type="playerActive" coolDown="15"
		nextCoolDown="0" manaCost="80" nextManaCost="0" hurt="150" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />




	<item id="10105004" className="ChangE_HeDanFengBao" name="核弹风暴"
		level="0" additionalAttack="0" nextAdditionalAttack="50" serialNumber="5"
		initLevel="10" maxLevel="3" description="使用超时空传送召唤飞机对全屏的敌人进行核弹轰炸，对敌人造成大量伤害。"
		upgradePrice="0" owner="ChangE" type="playerActive" coolDown="25"
		nextCoolDown="16" manaCost="0" nextManaCost="80" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105104" className="ChangE_HeDanFengBao" name="核弹风暴"
		level="1" additionalAttack="100" nextAdditionalAttack="120"
		serialNumber="5" initLevel="10" maxLevel="3"
		description="使用超时空传送召唤飞机对全屏的敌人进行核弹轰炸，对敌人造成大量伤害。" upgradePrice="10000"
		owner="ChangE" type="playerActive" coolDown="30" nextCoolDown="28"
		manaCost="100" nextManaCost="130" hurt="30" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105204" className="ChangE_HeDanFengBao" name="核弹风暴"
		level="2" additionalAttack="120" nextAdditionalAttack="150"
		serialNumber="5" initLevel="10" maxLevel="3"
		description="使用超时空传送召唤飞机对全屏的敌人进行核弹轰炸，对敌人造成大量伤害。" upgradePrice="20000"
		owner="ChangE" type="playerActive" coolDown="28" nextCoolDown="25"
		manaCost="130" nextManaCost="160" hurt="50" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10105304" className="ChangE_HeDanFengBao" name="核弹风暴"
		level="3" additionalAttack="150" nextAdditionalAttack="0"
		serialNumber="5" initLevel="10" maxLevel="3"
		description="使用超时空传送召唤飞机对全屏的敌人进行核弹轰炸，对敌人造成大量伤害。" upgradePrice="0"
		owner="ChangE" type="playerActive" coolDown="25" nextCoolDown="0"
		manaCost="160" nextManaCost="0" hurt="100" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />

	<!--灵技能 -->
	<item id="10106000" className="LingHu_LiuHuaZhan" name="流花斩"
		level="0" additionalAttack="0" nextAdditionalAttack="100"
		serialNumber="7" initLevel="2" maxLevel="3"
		description="高速舞动双刃，如流花般向前方猛冲敌人，造成伤害并击退敌人。" upgradePrice="0" owner="Fox"
		type="playerActive" coolDown="3" nextCoolDown="0" manaCost="0"
		nextManaCost="20" hurt="0" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />
	<item id="10106100" className="LingHu_LiuHuaZhan" name="流花斩"
		level="1" additionalAttack="150" nextAdditionalAttack="350"
		serialNumber="7" initLevel="2" maxLevel="3"
		description="高速舞动双刃，如流花般向前方猛冲敌人，造成伤害并击退敌人。" upgradePrice="3000" owner="Fox"
		type="playerActive" coolDown="3" nextCoolDown="3" manaCost="15"
		nextManaCost="15" hurt="0" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />
	<item id="10106200" className="LingHu_LiuHuaZhan" name="流花斩"
		level="2" additionalAttack="350" nextAdditionalAttack="450"
		serialNumber="7" initLevel="2" maxLevel="3"
		description="高速舞动双刃，如流花般向前方猛冲敌人，造成伤害并击退敌人。" upgradePrice="6000" owner="Fox"
		type="playerActive" coolDown="3" nextCoolDown="3" manaCost="15"
		nextManaCost="15" hurt="0" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />
	<item id="10106300" className="LingHu_LiuHuaZhan" name="流花斩"
		level="3" additionalAttack="450" nextAdditionalAttack="0"
		serialNumber="7" initLevel="2" maxLevel="3"
		description="高速舞动双刃，如流花般向前方猛冲敌人，造成伤害并击退敌人。" upgradePrice="0" owner="Fox"
		type="playerActive" coolDown="3" nextCoolDown="0" manaCost="15"
		nextManaCost="0" hurt="0" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />



	<item id="10106001" className="LingHu_LingFenShen" name="灵分身"
		level="0" additionalAttack="70" nextAdditionalAttack="50"
		serialNumber="2" initLevel="3" maxLevel="3" description="向前方释放出一个灵狐分身，该分身继承灵狐的三个小技能。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="40" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106101" className="LingHu_LingFenShen" name="灵分身"
		level="1" additionalAttack="250" nextAdditionalAttack="350"
		serialNumber="2" initLevel="3" maxLevel="3" description="向前方释放出一个灵狐分身，该分身继承灵狐的三个小技能。"
		upgradePrice="8000" owner="Fox" type="playerActive" coolDown="9"
		nextCoolDown="8" manaCost="50" nextManaCost="60" hurt="5" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106201" className="LingHu_LingFenShen" name="灵分身"
		level="2" additionalAttack="350" nextAdditionalAttack="450"
		serialNumber="2" initLevel="3" maxLevel="3" description="向前方释放出一个灵狐分身，该分身继承灵狐的三个小技能。"
		upgradePrice="15000" owner="Fox" type="playerActive" coolDown="8"
		nextCoolDown="7" manaCost="60" nextManaCost="70" hurt="30" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106301" className="LingHu_LingFenShen" name="灵分身"
		level="3" additionalAttack="450" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="向前方释放出一个灵狐分身，该分身继承灵狐的三个小技能。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="7"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10106002" className="LingHu_LuanWuRen" name="乱舞刃" level="0"
		additionalAttack="0" nextAdditionalAttack="100" serialNumber="3"
		initLevel="5" maxLevel="3" description="灵狐曼舞，使自身进入剑舞状态，可移动攻击周围的敌人。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="0"
		nextCoolDown="18" manaCost="0" nextManaCost="50" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="4" />
	<item id="10106102" className="LingHu_LuanWuRen" name="乱舞刃" level="1"
		additionalAttack="70" nextAdditionalAttack="80" serialNumber="3"
		initLevel="5" maxLevel="3" description="灵狐曼舞，使自身进入剑舞状态，可移动攻击周围的敌人。"
		upgradePrice="8000" owner="Fox" type="playerActive" coolDown="15"
		nextCoolDown="14" manaCost="50" nextManaCost="60" hurt="15" nextHurt="60"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106202" className="LingHu_LuanWuRen" name="乱舞刃" level="2"
		additionalAttack="80" nextAdditionalAttack="100" serialNumber="3"
		initLevel="5" maxLevel="3" description="灵狐曼舞，使自身进入剑舞状态，可移动攻击周围的敌人。"
		upgradePrice="10000" owner="Fox" type="playerActive" coolDown="14"
		nextCoolDown="13" manaCost="60" nextManaCost="70" hurt="60" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106302" className="LingHu_LuanWuRen" name="乱舞刃" level="3"
		additionalAttack="100" nextAdditionalAttack="0" serialNumber="3"
		initLevel="5" maxLevel="3" description="灵狐曼舞，使自身进入剑舞状态，可移动攻击周围的敌人。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="13"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="150" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10106003" className="LingHu_PoKongZhan" name="破空斩"
		level="0" additionalAttack="0" nextAdditionalAttack="70" serialNumber="4"
		initLevel="8" maxLevel="3" description="把剑插入地面向前方召唤出灵火进行攻击，破空一刃，击飞前方敌人。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="60" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106103" className="LingHu_PoKongZhan" name="破空斩"
		level="1" additionalAttack="70" nextAdditionalAttack="120"
		serialNumber="4" initLevel="8" maxLevel="3"
		description="把剑插入地面向前方召唤出灵火进行攻击，破空一刃，击飞前方敌人。" upgradePrice="6000"
		owner="Fox" type="playerActive" coolDown="18" nextCoolDown="17"
		manaCost="60" nextManaCost="70" hurt="70" nextHurt="120 "
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106203" className="LingHu_PoKongZhan" name="破空斩"
		level="2" additionalAttack="120" nextAdditionalAttack="200"
		serialNumber="4" initLevel="8" maxLevel="3"
		description="把剑插入地面向前方召唤出灵火进行攻击，破空一刃，击飞前方敌人。" upgradePrice="8000"
		owner="Fox" type="playerActive" coolDown="17" nextCoolDown="15"
		manaCost="70" nextManaCost="80" hurt="120" nextHurt="170"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106303" className="LingHu_PoKongZhan" name="破空斩"
		level="3" additionalAttack="200" nextAdditionalAttack="0"
		serialNumber="4" initLevel="8" maxLevel="3"
		description="把剑插入地面向前方召唤出灵火进行攻击，破空一刃，击飞前方敌人。" upgradePrice="0" owner="Fox"
		type="playerActive" coolDown="15" nextCoolDown="0" manaCost="80"
		nextManaCost="0" hurt="170" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />




	<item id="10106004" className="LingHu_LingDongJiuTian" name="灵动九天"
		level="0" additionalAttack="0" nextAdditionalAttack="50" serialNumber="5"
		initLevel="10" maxLevel="3" description="召唤出灵狐之灵，对全屏的敌人造成大量伤害，舞动九天，破晓雷鸣，毁天灭地。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="25"
		nextCoolDown="16" manaCost="0" nextManaCost="80" hurt="0" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106104" className="LingHu_LingDongJiuTian" name="灵动九天"
		level="1" additionalAttack="100" nextAdditionalAttack="150"
		serialNumber="5" initLevel="10" maxLevel="3"
		description="召唤出灵狐之灵，对全屏的敌人造成大量伤害，舞动九天，破晓雷鸣，毁天灭地。" upgradePrice="10000"
		owner="Fox" type="playerActive" coolDown="30" nextCoolDown="28"
		manaCost="100" nextManaCost="130" hurt="100" nextHurt="200"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106204" className="LingHu_LingDongJiuTian" name="灵动九天"
		level="2" additionalAttack="200" nextAdditionalAttack="250"
		serialNumber="5" initLevel="10" maxLevel="3"
		description="召唤出灵狐之灵，对全屏的敌人造成大量伤害，舞动九天，破晓雷鸣，毁天灭地。" upgradePrice="20000"
		owner="Fox" type="playerActive" coolDown="28" nextCoolDown="25"
		manaCost="130" nextManaCost="160" hurt="200" nextHurt="300"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10106304" className="LingHu_LingDongJiuTian" name="灵动九天"
		level="3" additionalAttack="250" nextAdditionalAttack="0"
		serialNumber="5" initLevel="10" maxLevel="3"
		description="召唤出灵狐之灵，对全屏的敌人造成大量伤害，舞动九天，破晓雷鸣，毁天灭地。" upgradePrice="0"
		owner="Fox" type="playerActive" coolDown="25" nextCoolDown="0"
		manaCost="160" nextManaCost="0" hurt="300" nextHurt="0" lengthOfTime="0"
		nextLengthOfTime="0" />

<!-- 人物主动技能 -->

	<!-- 人物主动技能其他属性说明：additionalAttack : 附加伤害 nextAdditinalAttack： 技能下一级的附加伤害（注意这个数值要与下级的数值对应相同） 
		coolDown： 技能冷却时间 nextCoolDown： 技能下一级冷却时间 manaCost： 技能耗魔 nextManaCost: 技能下一级技能耗魔 
		hurt： 技能伤害 nextHurt：技能下一级的伤害 lengthOfTime: 技能持续时间 nextLengthOfTime： 技能下一级持续时间 -->

	<!--铁扇技能 -->
	<item id="10107000" className="TieShan_Chong" name="疾风突袭" level="0"
		additionalAttack="0" nextAdditionalAttack="100" serialNumber="7"
		initLevel="2" maxLevel="3" description="铁扇公主化作一道旋风，快速突进并击打前方敌人。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="3"
		nextCoolDown="0" manaCost="0" nextManaCost="20" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107100" className="TieShan_Chong" name="疾风突袭" level="1"
		additionalAttack="150" nextAdditionalAttack="300" serialNumber="7"
		initLevel="2" maxLevel="3" description="铁扇公主化作一道旋风，快速突进并击打前方敌人。"
		upgradePrice="3000" owner="TieShan" type="playerActive" coolDown="3"
		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107200" className="TieShan_Chong" name="疾风突袭"
		level="2" additionalAttack="300" nextAdditionalAttack="400"
		serialNumber="7" initLevel="2" maxLevel="3" description="铁扇公主化作一道旋风，快速突进并击打前方敌人。"
		upgradePrice="6000" owner="TieShan" type="playerActive" coolDown="3"
		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107300" className="TieShan_Chong" name="疾风突袭" level="3"
		additionalAttack="400" nextAdditionalAttack="0" serialNumber="7"
		initLevel="2" maxLevel="3" description="铁扇公主化作一道旋风，快速突进并击打前方敌人。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="3"
		nextCoolDown="0" manaCost="15" nextManaCost="0" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />

	<item id="10107001" className="TieShan_BianShenFeng" name="风火交织（风）"
		level="0" additionalAttack="70" nextAdditionalAttack="50"
		serialNumber="2" initLevel="3" maxLevel="3" description="铁扇公主拿出芭蕉扇转为近战，并改变另外两个技能为风属性。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="0"
		nextCoolDown="12" manaCost="0" nextManaCost="40" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
		
	<item id="10107101" className="TieShan_BianShenFeng" name="风火交织（风）"
		level="1" additionalAttack="0" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="铁扇公主拿出芭蕉扇转为近战，并改变另外两个技能为风属性。"
		upgradePrice="3000" owner="TieShan" type="playerActive" coolDown="15"
		nextCoolDown="10" manaCost="50" nextManaCost="60" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107201" className="TieShan_BianShenFeng" name="风火交织（风）"
		level="2" additionalAttack="0" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="铁扇公主拿出芭蕉扇转为近战，并改变另外两个技能为风属性。"
		upgradePrice="6000" owner="TieShan" type="playerActive" coolDown="10"
		nextCoolDown="7" manaCost="60" nextManaCost="70" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107301" className="TieShan_BianShenFeng" name="风火交织（风）"
		level="3" additionalAttack="0" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="铁扇公主拿出芭蕉扇转为近战，并改变另外两个技能为风属性。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="7"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10107002" className="TieShan_SanWeiZhenFeng" name="三昧真风"
		level="0" additionalAttack="70" nextAdditionalAttack="50"
		serialNumber="2" initLevel="5" maxLevel="3" description="铁扇公主扇动芭蕉扇，刮出三昧真风吹飞沿途怪物。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="0"
		nextCoolDown="18" manaCost="0" nextManaCost="40" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107102" className="TieShan_SanWeiZhenFeng" name="三昧真风"
		level="1" additionalAttack="80" nextAdditionalAttack="100"
		serialNumber="2" initLevel="5" maxLevel="3" description="铁扇公主扇动芭蕉扇，刮出三昧真风吹飞沿途怪物。"
		upgradePrice="5000" owner="TieShan" type="playerActive" coolDown="18"
		nextCoolDown="14" manaCost="40" nextManaCost="50" hurt="5" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107202" className="TieShan_SanWeiZhenFeng" name="三昧真风"
		level="2" additionalAttack="100" nextAdditionalAttack="120"
		serialNumber="2" initLevel="5" maxLevel="3" description="铁扇公主扇动芭蕉扇，刮出三昧真风吹飞沿途怪物。"
		upgradePrice="8000" owner="TieShan" type="playerActive" coolDown="14"
		nextCoolDown="10" manaCost="50" nextManaCost="70" hurt="30" nextHurt="70"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107302" className="TieShan_SanWeiZhenFeng" name="三昧真风"
		level="3" additionalAttack="120" nextAdditionalAttack="0"
		serialNumber="2" initLevel="5" maxLevel="3" description="铁扇公主扇动芭蕉扇，刮出三昧真风吹飞沿途怪物。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="10"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="70" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
		
	<item id="10107003" className="TieShan_FengShenFuTi" name="神风护体"
		level="0" additionalAttack="40" nextAdditionalAttack="40"
		serialNumber="2" initLevel="8" maxLevel="3" description="铁扇公主在身体周围制造不可穿越的风墙阻挡并击退周围怪物（每秒消耗总魔法值的4%）"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="0"
		nextCoolDown="1" manaCost="20" nextManaCost="30" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107103" className="TieShan_FengShenFuTi" name="神风护体"
		level="1" additionalAttack="40" nextAdditionalAttack="50"
		serialNumber="2" initLevel="8" maxLevel="3" description="铁扇公主在身体周围制造不可穿越的风墙阻挡并击退周围怪物（每秒消耗总魔法值的4%）"
		upgradePrice="8000" owner="TieShan" type="playerActive" coolDown="1"
		nextCoolDown="1" manaCost="30" nextManaCost="30" hurt="5" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107203" className="TieShan_FengShenFuTi" name="神风护体"
		level="2" additionalAttack="50" nextAdditionalAttack="60"
		serialNumber="2" initLevel="8" maxLevel="3" description="铁扇公主在身体周围制造不可穿越的风墙阻挡并击退周围怪物（每秒消耗总魔法值的4%）"
		upgradePrice="15000" owner="TieShan" type="playerActive" coolDown="1"
		nextCoolDown="1" manaCost="30" nextManaCost="30" hurt="30" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107303" className="TieShan_FengShenFuTi" name="神风护体"
		level="3" additionalAttack="60" nextAdditionalAttack="0"
		serialNumber="2" initLevel="8" maxLevel="3" description="铁扇公主在身体周围制造不可穿越的风墙阻挡并击退周围怪物（每秒消耗总魔法值的4%）"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="1"
		nextCoolDown="0" manaCost="30" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
			

		
	<item id="10107005" className="TieShan_BianShenHuo" name="风火交织（火）"
		level="0" additionalAttack="0" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="铁扇公主收起芭蕉扇变为远程攻击形态，并改变另外两个技能为火形态。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="0"
		nextCoolDown="15" manaCost="0" nextManaCost="40" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107105" className="TieShan_BianShenHuo" name="风火交织（火）"
		level="1" additionalAttack="0" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="铁扇公主收起芭蕉扇变为远程攻击形态，并改变另外两个技能为火形态。"
		upgradePrice="2000" owner="TieShan" type="playerActive" coolDown="15"
		nextCoolDown="10" manaCost="50" nextManaCost="60" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107205" className="TieShan_BianShenHuo" name="风火交织（火）"
		level="2" additionalAttack="0" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="铁扇公主收起芭蕉扇变为远程攻击形态，并改变另外两个技能为火形态。"
		upgradePrice="6000" owner="TieShan" type="playerActive" coolDown="10"
		nextCoolDown="7" manaCost="60" nextManaCost="70" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107305" className="TieShan_BianShenHuo" name="风火交织（火）"
		level="3" additionalAttack="0" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="铁扇公主收起芭蕉扇变为远程攻击形态，并改变另外两个技能为火形态。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="7"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10107006" className="TieShan_SanWeiZhenHuo" name="三昧真火"
		level="0" additionalAttack="0" nextAdditionalAttack="180"
		serialNumber="3" initLevel="5" maxLevel="3" description="铁扇公主引来火焰山的三昧真火，用魔法将火种引爆灼烧沿途怪物。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="0"
		nextCoolDown="18" manaCost="0" nextManaCost="40" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="4" />
	<item id="10107106" className="TieShan_SanWeiZhenHuo" name="三昧真火"
		level="1" additionalAttack="180" nextAdditionalAttack="260"
		serialNumber="3" initLevel="5" maxLevel="3" description="铁扇公主引来火焰山的三昧真火，用魔法将火种引爆灼烧沿途怪物。"
		upgradePrice="10000" owner="Fox" type="playerActive" coolDown="15"
		nextCoolDown="12" manaCost="40" nextManaCost="50" hurt="100" nextHurt="120"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107206" className="TieShan_SanWeiZhenHuo" name="三昧真火"
		level="2" additionalAttack="260" nextAdditionalAttack="320"
		serialNumber="3" initLevel="5" maxLevel="3" description="铁扇公主引来火焰山的三昧真火，用魔法将火种引爆灼烧沿途怪物。"
		upgradePrice="20000" owner="Fox" type="playerActive" coolDown="12"
		nextCoolDown="10" manaCost="50" nextManaCost="60" hurt="120" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107306" className="TieShan_SanWeiZhenHuo" name="三昧真火"
		level="3" additionalAttack="320" nextAdditionalAttack="0"
		serialNumber="3" initLevel="5" maxLevel="3" description="铁扇公主引来火焰山的三昧真火，用魔法将火种引爆灼烧沿途怪物。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="10"
		nextCoolDown="0" manaCost="60" nextManaCost="0" hurt="150" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />

	<item id="10107007" className="TieShan_HuoYanRanShao" name="火焰燃烧"
		level="0" additionalAttack="0" nextAdditionalAttack="80"
		serialNumber="3" initLevel="8" maxLevel="3" description="铁扇公主在身体周围燃起熊熊烈火灼烧周围怪物（每秒消耗总魔法值的4%）。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="0"
		nextCoolDown="1" manaCost="0" nextManaCost="120" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="4" />
	<item id="10107107" className="TieShan_HuoYanRanShao" name="火焰燃烧"
		level="1" additionalAttack="120" nextAdditionalAttack="180"
		serialNumber="3" initLevel="8" maxLevel="3" description="铁扇公主在身体周围燃起熊熊烈火灼烧周围怪物（每秒消耗总魔法值的4%）。"
		upgradePrice="8000" owner="Fox" type="playerActive" coolDown="1"
		nextCoolDown="1" manaCost="50" nextManaCost="60" hurt="15" nextHurt="60"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107207" className="TieShan_HuoYanRanShao" name="火焰燃烧"
		level="2" additionalAttack="180" nextAdditionalAttack="220"
		serialNumber="3" initLevel="8" maxLevel="3" description="铁扇公主在身体周围燃起熊熊烈火灼烧周围怪物（每秒消耗总魔法值的4%）。"
		upgradePrice="15000" owner="Fox" type="playerActive" coolDown="1"
		nextCoolDown="1" manaCost="60" nextManaCost="70" hurt="60" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107307" className="TieShan_HuoYanRanShao" name="火焰燃烧"
		level="3" additionalAttack="220" nextAdditionalAttack="0"
		serialNumber="3" initLevel="8" maxLevel="3" description="铁扇公主在身体周围燃起熊熊烈火灼烧周围怪物（每秒消耗总魔法值的4%）。"
		upgradePrice="0" owner="Fox" type="playerActive" coolDown="1"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="150" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10107004" className="TieShan_ZiReFengBao" name="炙热风暴"
		level="0" additionalAttack="70" nextAdditionalAttack="100"
		serialNumber="2" initLevel="10" maxLevel="3" description="铁扇公主驱动宝扇在空中快速旋转掀起席卷世界的炙热风暴，将所有怪物卷起并拉扯到空中数秒后猛落地面。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="0"
		nextCoolDown="36" manaCost="0" nextManaCost="40" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107104" className="TieShan_ZiReFengBao" name="炙热风暴"
		level="1" additionalAttack="100" nextAdditionalAttack="140"
		serialNumber="2" initLevel="10" maxLevel="3" description="铁扇公主驱动宝扇在空中快速旋转掀起席卷世界的炙热风暴，将所有怪物卷起并拉扯到空中数秒后猛落地面。"
		upgradePrice="15000" owner="TieShan" type="playerActive" coolDown="36"
		nextCoolDown="34" manaCost="100" nextManaCost="150" hurt="5" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107204" className="TieShan_ZiReFengBao" name="炙热风暴"
		level="2" additionalAttack="140" nextAdditionalAttack="180"
		serialNumber="2" initLevel="10" maxLevel="3" description="铁扇公主驱动宝扇在空中快速旋转掀起席卷世界的炙热风暴，将所有怪物卷起并拉扯到空中数秒后猛落地面。"
		upgradePrice="25000" owner="TieShan" type="playerActive" coolDown="34"
		nextCoolDown="32" manaCost="150" nextManaCost="220" hurt="30" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10107304" className="TieShan_ZiReFengBao" name="炙热风暴"
		level="3" additionalAttack="180" nextAdditionalAttack="0"
		serialNumber="2" initLevel="10" maxLevel="3" description="铁扇公主驱动宝扇在空中快速旋转掀起席卷世界的炙热风暴，将所有怪物卷起并拉扯到空中数秒后猛落地面。"
		upgradePrice="0" owner="TieShan" type="playerActive" coolDown="32"
		nextCoolDown="0" manaCost="220" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



<!-- 人物主动技能其他属性说明：additionalAttack : 附加伤害 nextAdditinalAttack： 技能下一级的附加伤害（注意这个数值要与下级的数值对应相同） 
		coolDown： 技能冷却时间 nextCoolDown： 技能下一级冷却时间 manaCost： 技能耗魔 nextManaCost: 技能下一级技能耗魔 
		hurt： 技能伤害 nextHurt：技能下一级的伤害 lengthOfTime: 技能持续时间 nextLengthOfTime： 技能下一级持续时间 -->

	<!--紫霞技能 -->
	<item id="10109000" className="ZiXia_ZiQingJianQi" name="紫青剑气"
		level="0" additionalAttack="0" nextAdditionalAttack="250"
		serialNumber="7" initLevel="2" maxLevel="3" description="紫霞用力挥舞宝剑，打出扇形范围伤害。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="5"

		nextCoolDown="0" manaCost="0" nextManaCost="20" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109100" className="ZiXia_ZiQingJianQi" name="紫青剑气"
		level="1" additionalAttack="250" nextAdditionalAttack="400"
		serialNumber="7" initLevel="2" maxLevel="3" description="紫霞用力挥舞宝剑，打出扇形范围伤害。"
		upgradePrice="3000" owner="ZiXia" type="playerActive" coolDown="5"
		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109200" className="ZiXia_ZiQingJianQi" name="紫青剑气"
		level="2" additionalAttack="400" nextAdditionalAttack="500"
		serialNumber="7" initLevel="2" maxLevel="3" description="紫霞用力挥舞宝剑，打出扇形范围伤害。"
		upgradePrice="6000" owner="ZiXia" type="playerActive" coolDown="5"

		nextCoolDown="3" manaCost="15" nextManaCost="15" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109300" className="ZiXia_ZiQingJianQi" name="紫青剑气"
		level="3" additionalAttack="500" nextAdditionalAttack="0"
		serialNumber="7" initLevel="2" maxLevel="3" description="紫霞用力挥舞宝剑，打出扇形范围伤害。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="5"

		nextCoolDown="0" manaCost="15" nextManaCost="0" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10109001" className="ZiXia_MiHuanXiangLing" name="迷幻响铃"
		level="0" additionalAttack="70" nextAdditionalAttack="90"
		serialNumber="2" initLevel="3" maxLevel="3" description="紫霞仙子摇动双手，手上铃铛持续响起，敌人不受控制丧失行动和攻击能力。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="16"
		nextCoolDown="16" manaCost="0" nextManaCost="40" hurt="0" nextHurt="50"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109101" className="ZiXia_MiHuanXiangLing" name="迷幻响铃"
		level="1" additionalAttack="90" nextAdditionalAttack="180"
		serialNumber="2" initLevel="3" maxLevel="3" description="紫霞仙子摇动双手，手上铃铛持续响起，敌人不受控制丧失行动和攻击能力。"
		upgradePrice="8000" owner="ZiXia" type="playerActive" coolDown="16"
		nextCoolDown="16" manaCost="50" nextManaCost="60" hurt="5" nextHurt="30"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109201" className="ZiXia_MiHuanXiangLing" name="迷幻响铃"
		level="2" additionalAttack="180" nextAdditionalAttack="250"
		serialNumber="2" initLevel="3" maxLevel="3" description="紫霞仙子摇动双手，手上铃铛持续响起，敌人不受控制丧失行动和攻击能力。"
		upgradePrice="15000" owner="ZiXia" type="playerActive" coolDown="16"
		nextCoolDown="16" manaCost="60" nextManaCost="70" hurt="30" nextHurt="100"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109301" className="ZiXia_MiHuanXiangLing" name="迷幻响铃"
		level="3" additionalAttack="250" nextAdditionalAttack="0"
		serialNumber="2" initLevel="3" maxLevel="3" description="紫霞仙子摇动双手，手上铃铛持续响起，敌人不受控制丧失行动和攻击能力。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="16"
		nextCoolDown="0" manaCost="70" nextManaCost="0" hurt="100" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<item id="10109002" className="ZiXia_ZiQingBaoJian" name="紫青宝剑"
		level="0" additionalAttack="0" nextAdditionalAttack="155" serialNumber="3"
		initLevel="5" maxLevel="3" description="紫霞仙子将宝剑抛出，宝剑巨大化持续攻击，对范围内敌人造成大量伤害。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="23"
		nextCoolDown="22" manaCost="0" nextManaCost="50" hurt="0" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="4" />
	<item id="10109102" className="ZiXia_ZiQingBaoJian" name="紫青宝剑"
		level="1" additionalAttack="155" nextAdditionalAttack="255"
		serialNumber="3" initLevel="5" maxLevel="3" description="紫霞仙子将宝剑抛出，宝剑巨大化持续攻击，对范围内敌人造成大量伤害。"
		upgradePrice="8000" owner="ZiXia" type="playerActive" coolDown="22"
		nextCoolDown="21" manaCost="70" nextManaCost="80" hurt="15" nextHurt="60"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109202" className="ZiXia_ZiQingBaoJian" name="紫青宝剑"
		level="2" additionalAttack="255" nextAdditionalAttack="355"
		serialNumber="3" initLevel="5" maxLevel="3" description="紫霞仙子将宝剑抛出，宝剑巨大化持续攻击，对范围内敌人造成大量伤害。"
		upgradePrice="10000" owner="ZiXia" type="playerActive" coolDown="21"
		nextCoolDown="20" manaCost="80" nextManaCost="90" hurt="60" nextHurt="150"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109302" className="ZiXia_ZiQingBaoJian" name="紫青宝剑"
		level="3" additionalAttack="355" nextAdditionalAttack="0"
		serialNumber="3" initLevel="5" maxLevel="3" description="紫霞仙子将宝剑抛出，宝剑巨大化持续攻击，对范围内敌人造成大量伤害。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="20"
		nextCoolDown="0" manaCost="90" nextManaCost="0" hurt="150" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />



	<item id="10109003" className="ZiXia_XuanFengBaoJian" name="旋风宝剑"
		level="0" additionalAttack="0" nextAdditionalAttack="70" serialNumber="4"
		initLevel="8" maxLevel="3" description="紫霞抛出宝剑，宝剑快速旋转小幅击退并连续攻击敌人。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="17"
		nextCoolDown="16" manaCost="0" nextManaCost="50" hurt="0" nextHurt="120"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109103" className="ZiXia_XuanFengBaoJian" name="旋风宝剑"
		level="1" additionalAttack="70" nextAdditionalAttack="80"
		serialNumber="4" initLevel="8" maxLevel="3" description="紫霞抛出宝剑，宝剑快速旋转小幅击退并连续攻击敌人。"
		upgradePrice="6000" owner="ZiXia" type="playerActive" coolDown="16"
		nextCoolDown="15" manaCost="60" nextManaCost="70" hurt="120" nextHurt="180 "
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109203" className="ZiXia_XuanFengBaoJian" name="旋风宝剑"
		level="2" additionalAttack="80" nextAdditionalAttack="90"
		serialNumber="4" initLevel="8" maxLevel="3" description="紫霞抛出宝剑，宝剑快速旋转小幅击退并连续攻击敌人。"

		upgradePrice="8000" owner="ZiXia" type="playerActive" coolDown="15"
		nextCoolDown="14" manaCost="70" nextManaCost="80" hurt="180" nextHurt="250"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109303" className="ZiXia_XuanFengBaoJian" name="旋风宝剑"
		level="3" additionalAttack="90" nextAdditionalAttack="0" serialNumber="4"
		initLevel="8" maxLevel="3" description="紫霞抛出宝剑，宝剑快速旋转小幅击退并连续攻击敌人。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="14"
		nextCoolDown="0" manaCost="80" nextManaCost="0" hurt="250" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />




	<item id="10109004" className="ZiXia_ShiGuangDaoLiu" name="时光倒流"
		level="0" additionalAttack="0" nextAdditionalAttack="180" serialNumber="5"
		initLevel="10" maxLevel="3" description="紫霞仙子发动月光宝盒，对敌人造成大量伤害并使敌人回到十秒前的位置。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="41"
		nextCoolDown="39" manaCost="0" nextManaCost="80" hurt="0" nextHurt="120"
		lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109104" className="ZiXia_ShiGuangDaoLiu" name="时光倒流"
		level="1" additionalAttack="180" nextAdditionalAttack="230"
		serialNumber="5" initLevel="10" maxLevel="3" description="紫霞仙子发动月光宝盒，对敌人造成大量伤害并使敌人回到十秒前的位置。"
		upgradePrice="10000" owner="ZiXia" type="playerActive" coolDown="39"
		nextCoolDown="37" manaCost="100" nextManaCost="130" hurt="120"
		nextHurt="160" lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109204" className="ZiXia_ShiGuangDaoLiu" name="时光倒流"
		level="2" additionalAttack="230" nextAdditionalAttack="280"
		serialNumber="5" initLevel="10" maxLevel="3" description="紫霞仙子发动月光宝盒，对敌人造成大量伤害并使敌人回到十秒前的位置。"
		upgradePrice="20000" owner="ZiXia" type="playerActive" coolDown="37"
		nextCoolDown="35" manaCost="130" nextManaCost="160" hurt="160"
		nextHurt="180" lengthOfTime="0" nextLengthOfTime="0" />
	<item id="10109304" className="ZiXia_ShiGuangDaoLiu" name="时光倒流"
		level="3" additionalAttack="280" nextAdditionalAttack="0"
		serialNumber="5" initLevel="10" maxLevel="3" description="紫霞仙子发动月光宝盒，对敌人造成大量伤害并使敌人回到十秒前的位置。"
		upgradePrice="0" owner="ZiXia" type="playerActive" coolDown="35"
		nextCoolDown="0" manaCost="160" nextManaCost="0" hurt="180" nextHurt="0"
		lengthOfTime="0" nextLengthOfTime="0" />


	<!-- 宠物技能 -->
	<!-- 宠物主动技能 -->
	<!-- 宠物主动技能的其他属性说明： coolDown 技能冷却时间 manaCost ： 技能耗魔 hurCoefficient 技能伤害系数 
		additionHurt 技能附加伤害 essenceCost 损耗精气 （公式： 技能系数（hurCoefficient) * 宠物等级 + 技能附加伤害（additionHurt）） -->
	<!-- 技能伤害分成：1.对怪物的伤害 2.PK伤害 hurt=宠物等级XhurCoefficient+additionHurt PKhurt=宠物等级XpkHurtCoefficient+additionPkHurt -->

	<item id="10103100" className="PetSkill_HanDiYing" name="撼地"
		level="1" serialNumber="0" initLevel="0" maxLevel="3"
		description="暴躁的龙族对地面进行震击，撼动地面，形成肉眼可见的龙影对敌人进行攻击。" upgradePrice="0"
		owner="" type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="5" additionHurt="30" pkHurtCoefficient="3"
		additionPkHurt="100" />
	<item id="10103200" className="PetSkill_HanDiYing" name="撼地"
		level="2" serialNumber="0" initLevel="0" maxLevel="3"
		description="暴躁的龙族对地面进行震击，撼动地面，形成肉眼可见的龙影对敌人进行攻击。" upgradePrice="0"
		owner="" type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="10" additionHurt="100" pkHurtCoefficient="5"
		additionPkHurt="150" />
	<item id="10103300" className="PetSkill_HanDiYing" name="撼地"
		level="3" serialNumber="0" initLevel="0" maxLevel="3"
		description="暴躁的龙族对地面进行震击，撼动地面，形成肉眼可见的龙影对敌人进行攻击。" upgradePrice="0"
		owner="" type="petActive" coolDown="80 " manaCost="100" essenceCost="40"
		hurCoefficient="15" additionHurt="250" pkHurtCoefficient="15"
		additionPkHurt="200" />


	<item id="10103101" className="PetSkill_NuTianLei" name="奔雷"
		level="1" serialNumber="0" initLevel="0" maxLevel="3"
		description="利用雷电奥义施放出十万伏特的电量，电击全屏怪物，使得怪物无所遁形。" upgradePrice="0"
		owner="" type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="15" additionHurt="90" pkHurtCoefficient="5"
		additionPkHurt="100" />
	<item id="10103201" className="PetSkill_NuTianLei" name="奔雷"
		level="2" serialNumber="0" initLevel="0" maxLevel="3"
		description="利用雷电奥义施放出十万伏特的电量，电击全屏怪物，使得怪物无所遁形。" upgradePrice="0"
		owner="" type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="30" additionHurt="300" pkHurtCoefficient="10"
		additionPkHurt="200" />
	<item id="10103301" className="PetSkill_NuTianLei" name="奔雷"
		level="3" serialNumber="0" initLevel="0" maxLevel="3"
		description="利用雷电奥义施放出十万伏特的电量，电击全屏怪物，使得怪物无所遁形。" upgradePrice="0"
		owner="" type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="45" additionHurt="750" pkHurtCoefficient="15"
		additionPkHurt="300" />

	<item id="10103102" className="PetSkill_YanBao" name="岩爆" level="1"
		serialNumber="0" initLevel="0" maxLevel="3"
		description="举起上千吨的石拳砸裂地面喷出大量地火对僵尸造成恐怖的伤害。" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="15" additionHurt="300" pkHurtCoefficient="10"
		additionPkHurt="100" />
	<item id="10103202" className="PetSkill_YanBao" name="岩爆" level="2"
		serialNumber="0" initLevel="0" maxLevel="3"
		description="举起上千吨的石拳砸裂地面喷出大量地火对僵尸造成恐怖的伤害。" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="30" additionHurt="1000" pkHurtCoefficient="15"
		additionPkHurt="200" />

	<item id="10103302" className="PetSkill_YanBao" name="岩爆" level="3"
		serialNumber="0" initLevel="0" maxLevel="3"
		description="举起上千吨的石拳砸裂地面喷出大量地火对僵尸造成恐怖的伤害。" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="45" additionHurt="2500" pkHurtCoefficient="20"
		additionPkHurt="400" />



	<item id="10103103" className="PetSkill_HuoQuan" name="烈拳" level="1"
		serialNumber="0" initLevel="0" maxLevel="3" description="将火焰高度集中然后像拳头般打出，被击中的敌人将痛苦不堪。"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="15" additionHurt="180"
		pkHurtCoefficient="5" additionPkHurt="100" />

	<item id="10103203" className="PetSkill_HuoQuan" name="烈拳" level="2"
		serialNumber="0" initLevel="0" maxLevel="3" description="将火焰高度集中然后像拳头般打出，被击中的敌人将痛苦不堪。"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="30" additionHurt="600"
		pkHurtCoefficient="10" additionPkHurt="150" />
	<item id="10103303" className="PetSkill_HuoQuan" name="烈拳" level="3"
		serialNumber="0" initLevel="0" maxLevel="3" description="将火焰高度集中然后像拳头般打出，被击中的敌人将痛苦不堪。"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="45" additionHurt="1500"
		pkHurtCoefficient="15" additionPkHurt="350" />

	<item id="10103104" className="PetSkill_DanMu" name="弹幕" level="1"
		serialNumber="0" initLevel="0" maxLevel="3" description="可怕的变形机械发射出密集的枪林弹雨对全屏敌人造成伤害。"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="15" additionHurt="90"
		pkHurtCoefficient="5" additionPkHurt="100" />

	<item id="10103204" className="PetSkill_DanMu" name="弹幕" level="2"
		serialNumber="0" initLevel="0" maxLevel="3" description="可怕的变形机械发射出密集的枪林弹雨对全屏敌人造成伤害。"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="30" additionHurt="300"
		pkHurtCoefficient="10" additionPkHurt="100" />
	<item id="10103304" className="PetSkill_DanMu" name="弹幕" level="3"
		serialNumber="0" initLevel="0" maxLevel="3" description="可怕的变形机械发射出密集的枪林弹雨对全屏敌人造成伤害。"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="45" additionHurt="750"
		pkHurtCoefficient="15" additionPkHurt="300" />


	<item id="10103105" className="PetSkill_BaoFengXue" name="暴风雪"
		level="1" serialNumber="0" initLevel="0" maxLevel="3"
		description="冰龙一族专属的技能，冰龙怒吼，召唤在极寒之巅的冰雪龙神，发射冻结一切的恐怖暴风雪!将万物化为白雪吧!"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="15" additionHurt="1500"
		pkHurtCoefficient="5" additionPkHurt="100" />
	<item id="10103205" className="PetSkill_BaoFengXue" name="暴风雪"
		level="2" serialNumber="0" initLevel="0" maxLevel="3"
		description="冰龙一族专属的技能，冰龙怒吼，召唤在极寒之巅的冰雪龙神，发射冻结一切的恐怖暴风雪!将万物化为白雪吧!"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="30" additionHurt="2500"
		pkHurtCoefficient="10" additionPkHurt="300" />
	<item id="10103305" className="PetSkill_BaoFengXue" name="暴风雪"
		level="3" serialNumber="0" initLevel="0" maxLevel="3"
		description="冰龙一族专属的技能，冰龙怒吼，召唤在极寒之巅的冰雪龙神，发射冻结一切的恐怖暴风雪!将万物化为白雪吧!"
		upgradePrice="0" owner="" type="petActive" coolDown="80" manaCost="100"
		essenceCost="40" hurCoefficient="45" additionHurt="3500"
		pkHurtCoefficient="20" additionPkHurt="600" />


	<item id="10103106" className="PetSkill_BaoBaoHe" name="爆爆盒"
		level="1" serialNumber="0" initLevel="0" maxLevel="3"
		description="调皮的六一礼盒爆炸出许多糖果对全屏的敌人造成伤害" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="15" additionHurt="500" pkHurtCoefficient="5"
		additionPkHurt="200" />
	<item id="10103206" className="PetSkill_BaoBaoHe" name="爆爆盒"
		level="2" serialNumber="0" initLevel="0" maxLevel="3"
		description="调皮的六一礼盒爆炸出许多糖果对全屏的敌人造成伤害" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="30" additionHurt="1500" pkHurtCoefficient="10"
		additionPkHurt="400" />
	<item id="10103306" className="PetSkill_BaoBaoHe" name="爆爆盒"
		level="3" serialNumber="0" initLevel="0" maxLevel="3"
		description="调皮的六一礼盒爆炸出许多糖果对全屏的敌人造成伤害" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="45" additionHurt="2500" pkHurtCoefficient="20"
		additionPkHurt="800" />


	<item id="10103107" className="PetSkill_Jian" name="炎破斩" level="1"
		serialNumber="0" initLevel="0" maxLevel="3"
		description="召唤远古炎魔挥动魔剑对前方的敌人造成大量伤害，并击退敌人。" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="15" additionHurt="1000" pkHurtCoefficient="5"
		additionPkHurt="100" />
	<item id="10103207" className="PetSkill_Jian" name="炎破斩" level="2"
		serialNumber="0" initLevel="0" maxLevel="3"
		description="召唤远古炎魔挥动魔剑对前方的敌人造成大量伤害，并击退敌人。" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="30" additionHurt="2000" pkHurtCoefficient="10"
		additionPkHurt="300" />
	<item id="10103307" className="PetSkill_Jian" name="炎破斩" level="3"
		serialNumber="0" initLevel="0" maxLevel="3"
		description="召唤远古炎魔挥动魔剑对前方的敌人造成大量伤害，并击退敌人。" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="45" additionHurt="3000" pkHurtCoefficient="20"
		additionPkHurt="400" />

	<!-- 宠物超进化技能 -->
	<item id="10103108" className="PetSkill_ShenLongZhan" name="觉醒·神龙斩"
		level="1" serialNumber="0" initLevel="0" maxLevel="1" description="神龙觉醒，天下无敌。"
		upgradePrice="0" owner="" type="petActive" coolDown="70" manaCost="100"
		essenceCost="40" hurCoefficient="45" additionHurt="5000"
		pkHurtCoefficient="25" additionPkHurt="1400" />


	<item id="10103109" className="PetSkill_BaoYanCi" name="觉醒·爆岩刺"
		level="1" serialNumber="0" initLevel="0" maxLevel="1" description="魔岩觉醒，威武霸气。"
		upgradePrice="0" owner="" type="petActive" coolDown="70" manaCost="100"
		essenceCost="40" hurCoefficient="45" additionHurt="3500"
		pkHurtCoefficient="25" additionPkHurt="1000" />


	<item id="10103110" className="PetSkill_ShanBengDiLie" name="觉醒·山崩地裂"
		level="1" serialNumber="0" initLevel="0" maxLevel="1" description="巨龙之锤，山崩地裂！"
		upgradePrice="0" owner="" type="petActive" coolDown="70" manaCost="100"
		essenceCost="40" hurCoefficient="50" additionHurt="3000"
		pkHurtCoefficient="25" additionPkHurt="800" />

	<item id="10103111" className="PetSkill_MoRiShenPan" name="觉醒·末日审判"
		level="1" serialNumber="0" initLevel="0" maxLevel="1"
		description="降魔剑圣发动魔剑的能量，劈裂大地，对前方的敌人造成大量伤害。" upgradePrice="0" owner=""
		type="petActive" coolDown="70" manaCost="100" essenceCost="40"
		hurCoefficient="45" additionHurt="4500" pkHurtCoefficient="25"
		additionPkHurt="1200" />

	<item id="10103112" className="PetSkill_JiSuLiuGuang" name="极速流光"
		level="1" serialNumber="0" initLevel="0" maxLevel="3"
		description="极速冲撞，流光闪烁，对前方的敌人照成大量伤害。" upgradePrice="0" owner=""
		type="petActive" coolDown="70" manaCost="100" essenceCost="40"
		hurCoefficient="15" additionHurt="500" pkHurtCoefficient="20"
		additionPkHurt="100" />
	<item id="10103212" className="PetSkill_JiSuLiuGuang" name="极速流光"
		level="2" serialNumber="0" initLevel="0" maxLevel="3"
		description="极速冲撞，流光闪烁，对前方的敌人照成大量伤害。" upgradePrice="0" owner=""
		type="petActive" coolDown="70" manaCost="100" essenceCost="40"
		hurCoefficient="30" additionHurt="1500" pkHurtCoefficient="20"
		additionPkHurt="300" />
	<item id="10103312" className="PetSkill_JiSuLiuGuang" name="极速流光"
		level="3" serialNumber="0" initLevel="0" maxLevel="3"
		description="极速冲撞，流光闪烁，对前方的敌人照成大量伤害。" upgradePrice="0" owner=""
		type="petActive" coolDown="70" manaCost="100" essenceCost="40"
		hurCoefficient="50" additionHurt="2500" pkHurtCoefficient="20"
		additionPkHurt="650" />


	<item id="10103113" className="PetSkill_ShenShengBingYu" name="神圣冰雨"
		level="1" serialNumber="0" initLevel="0" maxLevel="1"
		description="冰雪女神召唤神圣冰雨，对全屏的敌人造成大量伤害！" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="45" additionHurt="4000" pkHurtCoefficient="25"
		additionPkHurt="1300" />

	<item id="10103114" className="PetSkill_TongKuFengBao" name="痛苦风暴"
		level="1" serialNumber="0" initLevel="0" maxLevel="1"
		description="痛苦风暴，天地极寒，对全屏敌人造成大量伤害！" upgradePrice="0" owner=""
		type="petActive" coolDown="80" manaCost="100" essenceCost="40"
		hurCoefficient="45" additionHurt="4500" pkHurtCoefficient="30"
		additionPkHurt="1300" />

	<item id="10103115" className="PetSkill_MoHuanXingYu" name="魔幻星雨"
		level="1" serialNumber="0" initLevel="0" maxLevel="1"
		description="催动魔幻之力，向敌人发动流星攻击，被流星击中后会变成纸老虎一段时间。" upgradePrice="0"
		owner="" type="petActive" coolDown="70" manaCost="100" essenceCost="40"
		hurCoefficient="50" additionHurt="5000" pkHurtCoefficient="50"
		additionPkHurt="1500" />


	<item id="10103116" className="PetSkill_FengHuang" name="流羽飞霜"
		level="1" serialNumber="0" initLevel="0" maxLevel="3"
		description="寒冰凝聚，天地极寒，无人逃离冰雪侵袭。" upgradePrice="0" owner="" type="petActive"
		coolDown="60" manaCost="120" essenceCost="50" hurCoefficient="15"
		additionHurt="2000" pkHurtCoefficient="20" additionPkHurt="100" />
	<item id="10103216" className="PetSkill_FengHuang" name="流羽飞霜"
		level="2" serialNumber="0" initLevel="0" maxLevel="3"
		description="寒冰凝聚，天地极寒，无人逃离冰雪侵袭。" upgradePrice="0" owner="" type="petActive"
		coolDown="60" manaCost="120" essenceCost="50" hurCoefficient="30"
		additionHurt="3000" pkHurtCoefficient="20" additionPkHurt="300" />
	<item id="10103316" className="PetSkill_FengHuang" name="流羽飞霜"
		level="3" serialNumber="0" initLevel="0" maxLevel="3"
		description="寒冰凝聚，天地极寒，无人逃离冰雪侵袭。" upgradePrice="0" owner="" type="petActive"
		coolDown="60" manaCost="120" essenceCost="50" hurCoefficient="50"
		additionHurt="4000" pkHurtCoefficient="20" additionPkHurt="650" />


	<item id="10103117" className="PetSkill_FengHuang" name="雪虐风饕"
		level="1" serialNumber="0" initLevel="0" maxLevel="1"
		description="极寒之气弥漫天地，冰雪冲击地面对敌人造成大量伤害，并击退敌人！" upgradePrice="0" owner=""
		type="petActive" coolDown="60" manaCost="120" essenceCost="50"
		hurCoefficient="150" additionHurt="8000" pkHurtCoefficient="100"
		additionPkHurt="8000" />

	<!-- 宠物技能 -->
	<!-- 宠物主动技能 -->
	<!-- 宠物主动技能的其他属性说明： coolDown 技能冷却时间 manaCost ： 技能耗魔 hurCoefficient 技能伤害系数 
		additionHurt 技能附加伤害 essenceCost 损耗精气 （公式： 技能系数（hurCoefficient) * 宠物等级 + 技能附加伤害（additionHurt）） -->
	<!-- 技能伤害分成：1.对怪物的伤害 2.PK伤害 hurt=宠物等级XhurCoefficient+additionHurt PKhurt=宠物等级XpkHurtCoefficient+additionPkHurt -->

	<item id="10103118" className="PetSkill_ChongMing" name="雷鸣/羽暮"
		level="1" serialNumber="0" initLevel="0" maxLevel="3"
		description="一声啼叫如雷霆万钧，一眸寒光击碎银盔金甲，直吓得万千敌军瑟瑟发抖不堪一击！" upgradePrice="0"
		owner="" type="petActive" coolDown="75" manaCost="80" essenceCost="40"
		hurCoefficient="25" additionHurt="2000" pkHurtCoefficient="30"
		additionPkHurt="500" subWalkSpeed="10" />
	<item id="10103218" className="PetSkill_ChongMing" name="雷鸣/羽暮"
		level="2" serialNumber="0" initLevel="0" maxLevel="3"
		description="一声啼叫如雷霆万钧，一眸寒光击碎银盔金甲，直吓得万千敌军瑟瑟发抖不堪一击！" upgradePrice="0"
		owner="" type="petActive" coolDown="75" manaCost="100" essenceCost="50"
		hurCoefficient="50" additionHurt="3000" pkHurtCoefficient="30"
		additionPkHurt="1000" subWalkSpeed="15" />
	<item id="10103318" className="PetSkill_ChongMing" name="雷鸣/羽暮"
		level="3" serialNumber="0" initLevel="0" maxLevel="3"
		description="一声啼叫如雷霆万钧，一眸寒光击碎银盔金甲，直吓得万千敌军瑟瑟发抖不堪一击！" upgradePrice="0"
		owner="" type="petActive" coolDown="75" manaCost="120" essenceCost="60"
		hurCoefficient="70" additionHurt="4000" pkHurtCoefficient="30"
		additionPkHurt="2000" subWalkSpeed="20" />


	<item id="10103119" className="PetSkill_ChongMing" name="雷鸣/羽暮"
		level="1" serialNumber="0" initLevel="0" maxLevel="1"
		description="一声啼叫如雷霆万钧，一眸寒光击碎银盔金甲，直吓得万千敌军瑟瑟发抖不堪一击！" upgradePrice="0"
		owner="" type="petActive" coolDown="95" manaCost="180" essenceCost="80"
		hurCoefficient="200" additionHurt="8000" pkHurtCoefficient="90"
		additionPkHurt="6500" subWalkSpeed="30" />


	<!-- 宠物技能 -->
	<!-- 宠物主动技能 -->
	<!-- 宠物主动技能的其他属性说明： coolDown 技能冷却时间 manaCost ： 技能耗魔 hurCoefficient 技能伤害系数 
		additionHurt 技能附加伤害 essenceCost 损耗精气 （公式： 技能系数（hurCoefficient) * 宠物等级 + 技能附加伤害（additionHurt）） -->
	<!-- 技能伤害分成：1.对怪物的伤害 2.PK伤害 hurt=宠物等级XhurCoefficient+additionHurt PKhurt=宠物等级XpkHurtCoefficient+additionPkHurt -->

	<item id="10103120" className="PetSkill_Molong" name="魔龙之怒" level="1"
		serialNumber="0" initLevel="0" maxLevel="3" description="仰天一啸惊天地，擎天一怒镇群魔！"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="80"
		essenceCost="40" hurCoefficient="25" additionHurt="2000"
		pkHurtCoefficient="30" additionPkHurt="1000" subWalkSpeed="5" />

	<item id="10103220" className="PetSkill_Molong" name="魔龙之怒" level="2"
		serialNumber="0" initLevel="0" maxLevel="3" description="仰天一啸惊天地，擎天一怒镇群魔！！"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="100"
		essenceCost="50" hurCoefficient="50" additionHurt="3000"
		pkHurtCoefficient="30" additionPkHurt="2000" subWalkSpeed="7" />

	<item id="10103320" className="PetSkill_Molong" name="魔龙之怒" level="3"
		serialNumber="0" initLevel="0" maxLevel="3" description="仰天一啸惊天地，擎天一怒镇群魔！"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="120"
		essenceCost="60" hurCoefficient="70" additionHurt="4000"
		pkHurtCoefficient="30" additionPkHurt="4000" subWalkSpeed="10" />

	<item id="10103121" className="PetSkill_Molong" name="魔龙之怒" level="1"
		serialNumber="0" initLevel="0" maxLevel="1" description="仰天一啸惊天地，擎天一怒镇群魔！"
		upgradePrice="0" owner="" type="petActive" coolDown="95" manaCost="180"
		essenceCost="80" hurCoefficient="220" additionHurt="8000"
		pkHurtCoefficient="120" additionPkHurt="8000" subWalkSpeed="20" />

	<!-- 宠物技能 -->
	<!-- 宠物主动技能 -->
	<!-- 宠物主动技能的其他属性说明： coolDown 技能冷却时间 manaCost ： 技能耗魔 hurCoefficient 技能伤害系数 
		additionHurt 技能附加伤害 essenceCost 损耗精气 （公式： 技能系数（hurCoefficient) * 宠物等级 + 技能附加伤害（additionHurt）） -->
	<!-- 技能伤害分成：1.对怪物的伤害 2.PK伤害 hurt=宠物等级XhurCoefficient+additionHurt PKhurt=宠物等级XpkHurtCoefficient+additionPkHurt -->
	<item id="10103122" className="PetSkill_Zhulong" name="龙族夜斩" level="1"
		serialNumber="0" initLevel="0" maxLevel="3" description="天地之间我为尊，神技一出斩神魔！"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="80"
		essenceCost="40" hurCoefficient="40" additionHurt="2000"
		pkHurtCoefficient="30" additionPkHurt="2000" diePrecentBase="3" />

	<item id="10103222" className="PetSkill_Zhulong" name="龙族夜斩" level="2"
		serialNumber="0" initLevel="0" maxLevel="3" description="天地之间我为尊，神技一出斩神魔！"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="100"
		essenceCost="50" hurCoefficient="80" additionHurt="4000"
		pkHurtCoefficient="40" additionPkHurt="4000" diePrecentBase="5" />

	<item id="10103322" className="PetSkill_Zhulong" name="龙族夜斩" level="3"
		serialNumber="0" initLevel="0" maxLevel="3" description="天地之间我为尊，神技一出斩神魔！"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="120"
		essenceCost="60" hurCoefficient="120" additionHurt="6000"
		pkHurtCoefficient="60" additionPkHurt="6000" diePrecentBase="7" />

	<item id="10103123" className="PetSkill_Zhulong" name="龙族夜斩" level="1"
		serialNumber="0" initLevel="0" maxLevel="1" description="天地之间我为尊，神技一出斩神魔！"
		upgradePrice="0" owner="" type="petActive" coolDown="95" manaCost="180"
		essenceCost="80" hurCoefficient="320" additionHurt="12000"
		pkHurtCoefficient="160" additionPkHurt="12000" diePrecentBase="10" />

	<!-- 宠物技能 -->
	<!-- 宠物主动技能 -->
	<!-- 宠物主动技能的其他属性说明： coolDown 技能冷却时间 manaCost ： 技能耗魔 hurCoefficient 技能伤害系数 
		additionHurt 技能附加伤害 essenceCost 损耗精气 （公式： 技能系数（hurCoefficient) * 宠物等级 + 技能附加伤害（additionHurt）） -->
	<!-- 技能伤害分成：1.对怪物的伤害 2.PK伤害 hurt=宠物等级XhurCoefficient+additionHurt PKhurt=宠物等级XpkHurtCoefficient+additionPkHurt -->
	<!-- diePrecentBase ： 护甲减少百分比  buffTime:debuff持续时间-->
	<item id="10103124" className="PetSkill_QiongQi" name="炙焰屠戮" level="1"
		serialNumber="0" initLevel="0" maxLevel="3" description="上古凶兽降临！震天一吼对敌人造成伤害并破除防御"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="80"
		essenceCost="40" hurCoefficient="80" additionHurt="2000"
		pkHurtCoefficient="40" additionPkHurt="6000" diePrecentBase="25" buffTime="15000"/>

	<item id="10103224" className="PetSkill_QiongQi" name="炙焰屠戮" level="2"
		serialNumber="0" initLevel="0" maxLevel="3" description="上古凶兽降临！震天一吼对敌人造成伤害并破除防御"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="100"
		essenceCost="50" hurCoefficient="160" additionHurt="4000"
		pkHurtCoefficient="80" additionPkHurt="8000" diePrecentBase="30" buffTime="15000"/>

	<item id="10103324" className="PetSkill_QiongQi" name="炙焰屠戮" level="3"
		serialNumber="0" initLevel="0" maxLevel="3" description="上古凶兽降临！震天一吼对敌人造成伤害并破除防御"
		upgradePrice="0" owner="" type="petActive" coolDown="75" manaCost="120"
		essenceCost="60" hurCoefficient="220" additionHurt="6000"
		pkHurtCoefficient="100" additionPkHurt="10000" diePrecentBase="35" buffTime="15000"/>

	<item id="10103125" className="PetSkill_QiongQi" name="炙焰屠戮" level="1"
		serialNumber="0" initLevel="0" maxLevel="1" description="上古凶兽降临！震天一吼对敌人造成伤害并破除防御"
		upgradePrice="0" owner="" type="petActive" coolDown="95" manaCost="180"
		essenceCost="80" hurCoefficient="420" additionHurt="12000"
		pkHurtCoefficient="220" additionPkHurt="22000" diePrecentBase="40" buffTime="15000" />

	<!-- 20*45+4500=5400 1800 -->
	<!-- 宠物被动技能 -->
	<!-- 宠物被动技能其他属性说明： passiveType：宠物被动技能的类型 value: 宠物被动技能的数值 -->
	<!-- 宠物被动技能的类型有如下几个： "attack"： 攻击 "defence"：防御 "auxiliary"：辅助 -->

	<item id="10203101" className="PetSkill_GuWu" name="鼓舞" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的攻击力"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="15" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />

	<item id="10203201" className="PetSkill_GuWu" name="鼓舞" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的攻击力"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="30" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />

	<item id="10203301" className="PetSkill_GuWu" name="鼓舞" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的攻击力"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="60" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />

	<item id="10203401" className="PetSkill_GuWu" name="鼓舞" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的攻击力"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="100" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />

	<item id="10203102" className="PetSkill_JiNu" name="激怒" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物暴击率"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="5" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />
	<item id="10203202" className="PetSkill_JiNu" name="激怒" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物暴击率"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="8" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />
	<item id="10203302" className="PetSkill_JiNu" name="激怒" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物暴击率"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="15" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />

	<item id="10203402" className="PetSkill_JiNu" name="激怒" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物暴击率"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="20" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />

	<item id="10203103" className="PetSkill_FaShuZengQiang" name="技能增强"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="增加宠物的技能伤害"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="200" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />
	<item id="10203203" className="PetSkill_FaShuZengQiang" name="技能增强"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="增加宠物的技能伤害"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="600" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />
	<item id="10203303" className="PetSkill_FaShuZengQiang" name="技能增强"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="增加宠物的技能伤害"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="1500" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />
	<item id="10203403" className="PetSkill_FaShuZengQiang" name="技能增强"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="增加宠物的技能伤害"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="2000" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,MoLong,ZhuLong,QiongQi" />

	<item id="10203104" className="PetSkill_ShouHu" name="守护" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的防御力"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="10" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,ZhuLong,QiongQi" />
	<item id="10203204" className="PetSkill_ShouHu" name="守护" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的防御力"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="25" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,ZhuLong,QiongQi" />
	<item id="10203304" className="PetSkill_ShouHu" name="守护" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的防御力"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="40" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,ZhuLong,QiongQi" />
	<item id="10203404" className="PetSkill_ShouHu" name="守护" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的防御力"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="60" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,ZhuLong,QiongQi" />

	<item id="10203105" className="PetSkill_JianZhuang" name="健壮"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的血量"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="15" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203205" className="PetSkill_JianZhuang" name="健壮"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的血量"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="25" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203305" className="PetSkill_JianZhuang" name="健壮"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的血量"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="45" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203405" className="PetSkill_JianZhuang" name="健壮"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的血量"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="47" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />


	<item id="10203106" className="PetSkill_MiShuZhangWo" name="秘术掌握"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的最大魔法值"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="20" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,ZhuLong,QiongQi" />
	<item id="10203206" className="PetSkill_MiShuZhangWo" name="秘术掌握"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的最大魔法值"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="30" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,ZhuLong,QiongQi" />
	<item id="10203306" className="PetSkill_MiShuZhangWo" name="秘术掌握"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的最大魔法值"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="45" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,ZhuLong,QiongQi" />
	<item id="10203406" className="PetSkill_MiShuZhangWo" name="秘术掌握"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的最大魔法值"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="50" unit="%"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,ZhuLong,QiongQi" />

	<item id="10203107" className="PetSkill_AoYiGuangHuang" name="奥义光环"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="缩短人物所有技能冷却时间"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="1" unit="秒"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203207" className="PetSkill_AoYiGuangHuang" name="奥义光环"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="缩短人物所有技能冷却时间"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="2" unit="秒"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203307" className="PetSkill_AoYiGuangHuang" name="奥义光环"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="缩短人物所有技能冷却时间"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="3" unit="秒"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203407" className="PetSkill_AoYiGuangHuang" name="奥义光环"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="缩短人物所有技能冷却时间"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="4" unit="秒"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />


	<item id="10203108" className="PetSkill_JiNengAoYi" name="技能奥义"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="缩减宠物主动技能冷却时间"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="5" unit="秒"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203208" className="PetSkill_JiNengAoYi" name="技能奥义"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="缩减宠物主动技能冷却时间"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="10" unit="秒"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203308" className="PetSkill_JiNengAoYi" name="技能奥义"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="缩减宠物主动技能冷却时间"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="15" unit="秒"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203408" className="PetSkill_JiNengAoYi" name="技能奥义"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="缩减宠物主动技能冷却时间"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="17" unit="秒"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />


	<item id="10203109" className="PetSkill_JiNengLingWu" name="技能领悟"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="减少宠物主动技能精气消耗"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="5" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203209" className="PetSkill_JiNengLingWu" name="技能领悟"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="减少宠物主动技能精气消耗"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="7" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203309" className="PetSkill_JiNengLingWu" name="技能领悟"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="减少宠物主动技能精气消耗"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="10" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />
	<item id="10203409" className="PetSkill_JiNengLingWu" name="技能领悟"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="减少宠物主动技能精气消耗"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="12" unit="点"
		useSeriesType="tyrannosaurs,xiaoPiKaQiu,shiTouRen,huoRen,jiQiRen,bingLong,liHe,jian,renXingBingLong,junYanZhanJiang,hanDiJuLong,xiangMoJianSheng,lu,FengHuang,ChongMing,MoLong,ZhuLong,QiongQi" />

	<item id="10203110" className="PetSkill_GuBen" name="固本" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物防爆率"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="5" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />
	<item id="10203210" className="PetSkill_GuBen" name="固本" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物防爆率"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="8" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />
	<item id="10203310" className="PetSkill_GuBen" name="固本" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物防爆率"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="15" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />

	<item id="10203410" className="PetSkill_GuBen" name="固本" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物防爆率"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="20" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />


	<item id="10203111" className="PetSkill_ShanBi" name="闪避大师" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的闪避几率"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="1.5" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />

	<item id="10203211" className="PetSkill_ShanBi" name="闪避大师" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的闪避几率"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="3" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />

	<item id="10203311" className="PetSkill_ShanBi" name="闪避大师" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的闪避几率"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="6" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />

	<item id="10203411" className="PetSkill_ShanBi" name="闪避大师" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的闪避几率"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="10" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />


	<item id="10203112" className="PetSkill_YiShuJingTong" name="异术精通"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="1.5" unit="%" useSeriesType="ChongMing" />

	<item id="10203212" className="PetSkill_YiShuJingTong" name="异术精通"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="3" unit="%" useSeriesType="ChongMing" />

	<item id="10203312" className="PetSkill_YiShuJingTong" name="异术精通"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="6" unit="%" useSeriesType="ChongMing" />

	<item id="10203412" className="PetSkill_YiShuJingTong" name="异术精通"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="10" unit="%" useSeriesType="ChongMing" />


	<item id="10203113" className="PetSkill_ZhiShang" name="止伤" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="百分比减少人物所受伤害"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="2" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />

	<item id="10203213" className="PetSkill_ZhiShang" name="止伤" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="百分比减少人物所受伤害"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="4" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />

	<item id="10203313" className="PetSkill_ZhiShang" name="止伤" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="百分比减少人物所受伤害"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="6" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />

	<item id="10203413" className="PetSkill_ZhiShang" name="止伤" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="百分比减少人物所受伤害"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="10" unit="%" useSeriesType="ChongMing,ZhuLong,QiongQi" />


	<!-- 宠物被动技能 -->
	<!-- 宠物被动技能其他属性说明： passiveType：宠物被动技能的类型 value: 宠物被动技能的数值 -->
	<!-- 宠物被动技能的类型有如下几个： "attack"： 攻击 "defence"：防御 "auxiliary"：辅助 -->


	<item id="10203114" className="PetSkill_Xuenu" name="血怒" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="当人物血量低于百分之"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="5" unit="时获得2%攻击力提升" useSeriesType="MoLong,ZhuLong,QiongQi" bloodPerLow="2" /><!-- 
		幻化的值是value -->

	<item id="10203214" className="PetSkill_Xuenu" name="血怒" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="当人物血量低于百分之"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="8" unit="时获得4%攻击力提升" useSeriesType="MoLong,ZhuLong,QiongQi" bloodPerLow="4" />

	<item id="10203314" className="PetSkill_Xuenu" name="血怒" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="当人物血量低于百分之"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="12" unit="时获得6%攻击力提升" useSeriesType="MoLong,ZhuLong,QiongQi" bloodPerLow="6" />

	<item id="10203414" className="PetSkill_Xuenu" name="血怒" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="当人物血量低于百分之"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="16" unit="时获得12%攻击力提升" useSeriesType="MoLong,ZhuLong,QiongQi" bloodPerLow="12" />


	<item id="10203115" className="PetSkill_Kuangbao" name="狂暴" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="人物每损失百分之1的血量就增加1点暴击属性，上限"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="5" unit="" useSeriesType="MoLong,ZhuLong,QiongQi" />

	<item id="10203215" className="PetSkill_Kuangbao" name="狂暴" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="人物每损失百分之1的血量就增加1点暴击属性，上限"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="10" unit="" useSeriesType="MoLong,ZhuLong,QiongQi" />

	<item id="10203315" className="PetSkill_Kuangbao" name="狂暴" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="人物每损失百分之1的血量就增加1点暴击属性，上限"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="15" unit="" useSeriesType="MoLong,ZhuLong,QiongQi" />

	<item id="10203415" className="PetSkill_Kuangbao" name="狂暴" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="人物每损失百分之1的血量就增加1点暴击属性，上限"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="20" unit="" useSeriesType="MoLong,ZhuLong,QiongQi" />



	<item id="10203116" className="PetSkill_Shixue" name="嗜血" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="当人物血量低于百分之"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="5" unit="时获得总生命值0.2%的回血效果" useSeriesType="MoLong,ZhuLong,QiongQi" bloodPerLow="0.2" />

	<item id="10203216" className="PetSkill_Shixue" name="嗜血" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="当人物血量低于百分之"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="10" unit="时获得总生命值0.3%的回血效果" useSeriesType="MoLong,ZhuLong,QiongQi" bloodPerLow="0.3" />

	<item id="10203316" className="PetSkill_Shixue" name="嗜血" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="当人物血量低于百分之"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="15" unit="时获得总生命值0.5%的回血效果" useSeriesType="MoLong,ZhuLong,QiongQi" bloodPerLow="0.5" />

	<item id="10203416" className="PetSkill_Shixue" name="嗜血" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="当人物血量低于百分之"
		upgradePrice="0" owner="" type="petPassive" passiveType="defence"
		value="20" unit="时获得总生命值1%的回血效果" useSeriesType="MoLong,ZhuLong,QiongQi" bloodPerLow="1" />


	<item id="10203117" className="PetSkill_YiShuJingTong2" name="异术精通"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="1.5" unit="%" useSeriesType="MoLong" />

	<item id="10203217" className="PetSkill_YiShuJingTong2" name="异术精通"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="3" unit="%" useSeriesType="MoLong" />

	<item id="10203317" className="PetSkill_YiShuJingTong2" name="异术精通"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="6" unit="%" useSeriesType="MoLong" />

	<item id="10203417" className="PetSkill_YiShuJingTong2" name="异术精通"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="10" unit="%" useSeriesType="MoLong" />

	<item id="10203118" className="PetSkill_YiShuJingTong3" name="异术精通"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="1.5" unit="%" useSeriesType="ZhuLong" />

	<item id="10203218" className="PetSkill_YiShuJingTong3" name="异术精通"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="3" unit="%" useSeriesType="ZhuLong" />

	<item id="10203318" className="PetSkill_YiShuJingTong3" name="异术精通"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="6" unit="%" useSeriesType="ZhuLong" />

	<item id="10203418" className="PetSkill_YiShuJingTong3" name="异术精通"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="10" unit="%" useSeriesType="ZhuLong" />

	<item id="10203119" className="PetSkill_Mingzhong" name="弹无虚发" level="1"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的命中几率"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="2" unit="%" useSeriesType="ZhuLong,QiongQi" />

	<item id="10203219" className="PetSkill_Mingzhong" name="弹无虚发" level="2"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的命中几率"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="4" unit="%" useSeriesType="ZhuLong,QiongQi" />

	<item id="10203319" className="PetSkill_Mingzhong" name="弹无虚发" level="3"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的命中几率"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="6" unit="%" useSeriesType="ZhuLong,QiongQi" />

	<item id="10203419" className="PetSkill_Mingzhong" name="弹无虚发" level="4"
		serialNumber="0" initLevel="0" maxLevel="4" description="增加人物的命中几率"
		upgradePrice="0" owner="" type="petPassive" passiveType="attack"
		value="10" unit="%" useSeriesType="ZhuLong,QiongQi" />

	<item id="10203120" className="PetSkill_YiShuJingTong4" name="异术精通"
		level="1" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="1.5" unit="%" useSeriesType="QiongQi" />

	<item id="10203220" className="PetSkill_YiShuJingTong4" name="异术精通"
		level="2" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="3" unit="%" useSeriesType="QiongQi" />

	<item id="10203320" className="PetSkill_YiShuJingTong4" name="异术精通"
		level="3" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="6" unit="%" useSeriesType="QiongQi" />

	<item id="10203420" className="PetSkill_YiShuJingTong4" name="异术精通"
		level="4" serialNumber="0" initLevel="0" maxLevel="4" description="百分比增加主动技能减益效果"
		upgradePrice="0" owner="" type="petPassive" passiveType="auxiliary"
		value="10" unit="%" useSeriesType="QiongQi" />
	<!--觉醒宠物被动 -->

	<item id="petAwakePassive_1" level="1" name="觉醒技能1" className="PetAwakePassive_1"
		description="觉醒技能1" type="petAwakePassive" owner="" maxLevel="1">
		<promote attribute="promotePlayerAttr" value="" description="提升人物">
			<promote attribute="reBlood_petAdd2" value="10" description="增加人物回血每秒10点" />
			<promote attribute="reBlood_petAdd2" value="10" description="增加人物回血每秒10点" />
			<promote attribute="reBlood_petAdd2" value="10" description="增加人物回血每秒10点" />
			<promote attribute="reBlood_petAdd2" value="10" description="增加人物回血每秒10点" />
			<promote attribute="reMagic_petAdd2" value="2" description="增加人物回血每秒2点" />
			<promote attribute="reMagic_petAdd2" value="2" description="增加人物回血每秒2点" />
			<promote attribute="reMagic_petAdd2" value="2" description="增加人物回血每秒2点" />
		</promote>
		<promote attribute="promotePetAttr" value="" description="提升宠物">
			<!-- <promote attribute="reEssen_petAdd2" value="1" description="增加宠物回精气每秒1点" 
				/> -->
			<promote attribute="promoteActiveSkill" value="" description="提升主动技能">
				<promote attribute="pkHurt2" value="1000" description="增加主动技能PK伤害1000点" />
			</promote>
		</promote>


	</item>
	<item id="petAwakePassive_LiLiang_1" level="1" name="力量觉醒"
		className="PetAwakePassive_2" description="觉醒·力量，增加人物攻击与暴击" type="petAwakePassive"
		owner="" maxLevel="1">
		<promote attribute="promotePlayerAttr" value="" description="觉醒·力量，增加人物攻击与暴击">
			<promote attribute="attack_petAdd2" value="80" description="增加人物80点攻击力" />
			<promote attribute="criticalRate_petAdd2" value="5"
				description="增加人物5%暴击" />
		</promote>
	</item>

	<item id="petAwakePassive_NaiLi_1" level="1" name="耐力觉醒"
		className="PetAwakePassive_3" description="觉醒·耐力，增加人物防御与防爆" type="petAwakePassive"
		owner="" maxLevel="1">
		<promote attribute="promotePlayerAttr" value="" description="觉醒·耐力，增加人物防御与防爆">
			<promote attribute="defence_petAdd2" value="50" description="增加人物50点防御力" />
			<promote attribute="riot_petAdd2" value="0.03" description="增加人物3%防爆" />
		</promote>


	</item>
	<item id="petAwakePassive_TiZzhi_1" level="1" name="体质觉醒"
		className="PetAwakePassive_4" description="觉醒·体质，增加人物生命与回血" type="petAwakePassive"
		owner="" maxLevel="1">
		<promote attribute="promotePlayerAttr" value="" description="觉醒·体质，增加人物生命与回血">
			<promote attribute="blood_petAdd2" value="200" description="增加人物200点生命力" />
			<promote attribute="reBlood_petAdd2" value="5" description="每秒回复人物生命5点" />
		</promote>



	</item>
	<item id="petAwakePassive_FuZhu_1" level="1" name="辅助觉醒"
		className="PetAwakePassive_5" description="觉醒·辅助，减短宠物技能冷却与增加宠物PK伤害"
		type="petAwakePassive" owner="" maxLevel="1">

		<promote attribute="promotePetAttr" value="" description="提升宠物">
			<!-- <promote attribute="reEssen_petAdd2" value="1" description="增加宠物回精气每秒1点" 
				/> -->
			<promote attribute="promoteActiveSkill" value="" description="提升主动技能">
				<promote attribute="pkHurt2" value="100" description="增加主动技能PK伤害100点" />
				<promote attribute="coolDown2" value="-5" description="减短宠物主动技能冷却5秒" />
			</promote>
		</promote>


	</item>
	<item id="petAwakePassive_GaoJingLiLiang_1" level="1" name="高级力量觉醒"
		className="PetAwakePassive_6" description="高级觉醒·力量，增加人物攻击、暴击与宠物PK伤害"
		type="petAwakePassive" owner="" maxLevel="1">
		<promote attribute="promotePlayerAttr" value=""
			description="高级觉醒·力量，增加人物攻击、暴击与宠物PK伤害">
			<promote attribute="attack_petAdd2" value="250" description="增加人物250点攻击力" />
			<promote attribute="criticalRate_petAdd2" value="10"
				description="增加人物10%暴击" />
		</promote>
		<promote attribute="promotePetAttr" value="" description="提升宠物">
			<!-- <promote attribute="reEssen_petAdd2" value="1" description="增加宠物回精气每秒1点" 
				/> -->
			<promote attribute="promoteActiveSkill" value="" description="提升主动技能">
				<promote attribute="pkHurt2" value="100" description="增加主动技能PK伤害100点" />
			</promote>
		</promote>


	</item>
	<item id="petAwakePassive_GaoJiNaiLi_1" level="1" name="高级耐力觉醒"
		className="PetAwakePassive_7" description="高级觉醒·耐力，增加人物防御、防爆与生命" type="petAwakePassive"
		owner="" maxLevel="1">
		<promote attribute="promotePlayerAttr" value=""
			description="高级觉醒·耐力，增加人物防御、防爆与生命">
			<promote attribute="defence_petAdd2" value="150" description="增加人物150点防御力" />
			<promote attribute="blood_petAdd2" value="200" description="增加人物200点生命力" />
			<promote attribute="riot_petAdd2" value="0.06" description="增加人物6%防爆" />

		</promote>



	</item>
	<item id="petAwakePassive_GaoJiTiZzhi_1" level="1" name="高级体质觉醒"
		className="PetAwakePassive_8" description="高级觉醒·体质，增加人物生命、回血与防御" type="petAwakePassive"
		owner="" maxLevel="1">
		<promote attribute="promotePlayerAttr" value=""
			description="高级觉醒·体质，增加人物生命、回血与防御">
			<promote attribute="defence_petAdd2" value="100" description="增加人物150点防御力" />
			<promote attribute="blood_petAdd2" value="400" description="增加人物400点生命力" />
			<promote attribute="reBlood_petAdd2" value="10" description="每秒回复人物生命10点" />
		</promote>



	</item>
	<item id="petAwakePassive_GaoJiFuZhu_1" level="1" name="高级辅助觉醒"
		className="PetAwakePassive_9" description="高级觉醒·辅助，减短宠物技能冷却、增加宠物PK伤害与增加人物回魔速度"
		type="petAwakePassive" owner="" maxLevel="1">
		<promote attribute="promotePlayerAttr" value=""
			description="高级觉醒·辅助，减短宠物技能冷却、增加宠物PK伤害与增加人物回魔速度">

			<promote attribute="reMagic_petAdd2" value="5" description="每秒回复人物魔法5点" />
		</promote>
		<promote attribute="promotePetAttr" value="" description="提升宠物">
			<!-- <promote attribute="reEssen_petAdd2" value="1" description="增加宠物回精气每秒1点" 
				/> -->
			<promote attribute="promoteActiveSkill" value="" description="提升主动技能">
				<promote attribute="pkHurt2" value="300" description="增加主动技能PK伤害300点" />
				<promote attribute="coolDown2" value="-10" description="减短宠物主动技能冷却10秒" />
			</promote>
		</promote>


	</item>

</Skill>
