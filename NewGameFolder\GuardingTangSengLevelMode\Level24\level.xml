<?xml version="1.0" encoding="utf-8" ?>
<data id="Level24" swfPath="NewGameFolder/GuardingTangSengLevelMode/Level24/level.swf"
	className="LevelMap24" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf" className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
		<Waves totalWaveNum="15">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->																						

		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="0" duration="5000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="0" duration="5000" num="3"  isFallDown="0"  />
		
		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="15000" duration="5000" num="4" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="15000" duration="5000" num="4"  isFallDown="0"  />

		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="30000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="30000" duration="10000" num="3" isFallDown="0" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="45000" duration="50000" num="4" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="45000" duration="50000" num="4" isFallDown="0" />
		</Wave>				
		<Wave waveCount="5" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="60000" duration="10000" num="3" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="60000" duration="10000" num="3" isFallDown="0" />
		</Wave>		
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="75000" duration="5000" num="4" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="75000" duration="5000" num="4" isFallDown="0" />
		</Wave>		
		<Wave waveCount="7" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="90000" duration="10000" num="5" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="90000" duration="10000" num="5" isFallDown="0" />
		</Wave>		
		<Wave waveCount="8" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="105000" duration="5000" num="4" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="105000" duration="5000" num="4" isFallDown="0" />
		</Wave>		
		<Wave waveCount="9" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="120000" duration="5000" num="5" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="120000" duration="5000" num="5" isFallDown="0" />
		</Wave>	
		<Wave waveCount="10" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="135000" duration="10000" num="4" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="135000" duration="10000" num="4" isFallDown="0" />
		</Wave>	
		<Wave waveCount="11" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="150000" duration="10000" num="5" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="150000" duration="10000" num="5" isFallDown="0" />
		</Wave>		
		<Wave waveCount="12" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="165000" duration="10000" num="4" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="165000" duration="10000" num="4" isFallDown="0" />
		</Wave>	
		<Wave waveCount="13" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="180000" duration="10000" num="5" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="180000" duration="10000" num="5" isFallDown="0" />
		</Wave>														
		<Wave waveCount="14" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="195000" duration="10000" num="4" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="195000" duration="10000" num="4" isFallDown="0" />
		</Wave>																											
		<Wave waveCount="15" totalEnemyNum="10" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_SpeedUp" xmlPath="xiaoBing2" startTime="3000" duration="10000" num="5" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Enemy_WakeOfDeath" xmlPath="xiaoBing1" startTime="3000" duration="10000" num="5" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ma" xmlPath="boss2" startTime="3050" duration="1000" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_WakeOfDeath.Boss_Niu" xmlPath="boss1" startTime="3050" duration="1000" num="1"  />
		</Wave>
	</Waves>
	
	
<EqDrop>
   <xiaoBing noDropProWeight="200">
		   <!--proWeight 概率权重-->
		 
		
		  <!-- 麋鹿蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_LiHe_S" proWeight="2" />	   	       
		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp2_S" proWeight="4" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp2_S" proWeight="4" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack2_S" proWeight="4" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp2_S" proWeight="4" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence2_S" proWeight="4" />   
 		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp3_S" proWeight="2" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp3_S" proWeight="2" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack3_S" proWeight="2" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp3_S" proWeight="2" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence3_S" proWeight="2" />     	      
   	      
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="5" />   
   	      <!-- 碎石锤 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S" proWeight="5" /> 
   	     <!-- 朱雀 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ZhuQueStone_S" proWeight="2" />   
   	      <!-- 玄武 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_XuanWuStone_S" proWeight="2" />      
     	     <!-- 青龙 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_QingLongStone_S" proWeight="2" />   
   	      <!-- 白虎 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_BaiHuStone_S" proWeight="2" />      
		  <!-- 灵狐 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LingHuShi_S" proWeight="5" />   	      
   	      	   <!-- 臧牛石  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ZangNiuShi_S" proWeight="2" />
		  		   <!-- 紫霞灯芯 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_dengxinshi_S" proWeight="2" /> 
		     <!-- 后羿金乌 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_JinwuShi_S" proWeight="2" /> 
		        		   
		  <!-- 地狱灵芝 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LingZhi_S" proWeight="10" />		 
		  <!-- 瑶池圣莲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Flower_S" proWeight="10" />		 
   	      <!-- 龙胆果 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_GuoZi_S" proWeight="10" />

   	      <!--  红药 -->   
   	      <item dropClassName="Item_HpUp" proWeight="20" />
   	      <!--  蓝药 -->  
   	      <item dropClassName="Item_MpUp" proWeight="20" />     	      	        
                     <!--   灵兽石-->
   	      <item dropClassName="Item_StrengthenNum_10" proWeight="1"  /> 
             <!--  召唤卷轴 -->
   	      <item dropClassName="Item_ZhaoHuan_1" proWeight="1"  /> 

   	      
 

 
   	      <!-- 羽毛 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_SuperLu_2_S" proWeight="2" />
   	      <!-- 圣灵精华 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S" proWeight="1" />
   	      <!-- 一级火 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S" proWeight="5" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="20" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="5" />
   	      <!-- 高级升级宝石模具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="10" />
   	          
	  </xiaoBing>
	   <boss noDropProWeight="20">
		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="2" proWeight="5" />
				   <numData num="3" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="5" proWeight="8" />
				   <numData num="10" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>		  
		   
		
   	                           <!--   灵兽石-->
   	      <item dropClassName="Item_StrengthenNum_10" proWeight="5"  /> 
             <!--  召唤卷轴 -->
   	      <item dropClassName="Item_ZhaoHuan_1" proWeight="5"  /> 
		  <!-- 无敌药水-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="2" />		 
		  <!-- 幸运蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_CaiDan_S" proWeight="2" />		 
		  <!-- 麋鹿蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_LiHe_S" proWeight="5" />		 	
   	       
		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp2_S" proWeight="5" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp2_S" proWeight="5" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack2_S" proWeight="5" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp2_S" proWeight="5" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence2_S" proWeight="5" />   
		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp3_S" proWeight="5" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp3_S" proWeight="5" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack3_S" proWeight="5" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp3_S" proWeight="5" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence3_S" proWeight="5" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="5" /> 
   	      <!-- 碎石锤 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S" proWeight="10" /> 
   	     <!-- 朱雀 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ZhuQueStone_S" proWeight="5" />   
   	      <!-- 玄武 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_XuanWuStone_S" proWeight="5" />      
     	     <!-- 青龙 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_QingLongStone_S" proWeight="5" />   	        
   	      <!-- 白虎 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_BaiHuStone_S" proWeight="5" />
		  <!-- 灵狐 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LingHuShi_S" proWeight="5" />
		     <!-- 后羿金乌 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_JinwuShi_S" proWeight="2" /> 
		  		   <!-- 紫霞灯芯 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_dengxinshi_S" proWeight="2" /> 
   	      <!-- 高级防御书 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.PetSkillBook_6" proWeight="1" />   
		      <!-- 臧牛石  --> 
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ZangNiuShi_S" proWeight="5" />
		  

   	      	         	      
<!--   	       红药   
   	      <item dropClassName="Item_HpUp" proWeight="100" />
   	       金币  
   	      <item dropClassName="Item_MoneyUp" proWeight="300" />
   	       蓝药  
   	      <item dropClassName="Item_MpUp" proWeight="100" /> 
  -->

     

   	      <!-- 黑羽毛 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_SuperLu_2_S" proWeight="2" />
   	      <!-- 圣灵精华 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S" proWeight="2" />
   	      <!-- 一级火 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S" proWeight="2" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="1" />
   	      <!-- 高级升级宝石模具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="1" />
	   </boss>
	   
   </EqDrop>


	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   <xiaoBing1>
	   <wakeOfDeathData deathDuration="5000" />
	<!--敌人数据 -->
	<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
		<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
	</fallDownEffect>
	<enemyData>
	
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
		<data att="totalHp" value="1200000" />
		<data att="attack" value="6000" />
		<data att="expOfDieThisEnemy" value="60000" />
		<data att="defence" value="30" />
		<data att="dogdeRate" value="0.08" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.5" />
		<data att="hitRate" value="0.09" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy23" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="90" maxAttackNum="2" walkSpeed="22"
		runSpeed="50">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-50" z="-1" xRange="100" yRange="100"
			zRange="100" />

		<idle defId="walk_enemy" />
		<walk defId="walk_enemy" />
		<run defId="walk_enemy" />
		<attack defId="attack_enemy" />
		<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow1" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="walk_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf"
					showClass="Walk_Monster_23" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="hurt_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf"
					showClass="BeAttack_Monster_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf"
					showClass="Attack_1_Monster_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy2" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf"
					showClass="Attack_2_Monster_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf"
					showClass="Dead_Monster_23" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="wakeOfDeath" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf"
					showClass="WakeOfDeath_Monster_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="enemyFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
		</animationDefinitions>
        <shows>
			
			<show defId="walk_enemy" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf" 
				  showClass="Walk_Monster_2_23"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="hurt_enemy" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf" 
				  showClass="BeAttack_Monster_2_23"
				  x_offset="0" 
				  y_offset="0" />
		    <show defId="attack_enemy1" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf" 
				  showClass="Attack_1_Monster_2_23"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="attack_enemy2" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf" 
				  showClass="Attack_2_Monster_2_23"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="die_enemy" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Enemy.swf" 
				  showClass="Dead_Monster_2_23"
				  x_offset="0" 
				  y_offset="0" />
		</shows>


	</animal>
</xiaoBing1>
   <boss1>
   <attackShakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" className="AttackShakeView" />
	<wakeOfDeathData deathDuration="5000" />
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="3500000" />
		<data att="attack" value="8000" />
		<data att="expOfDieThisEnemy" value="200000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.25" />
	</enemyData>
	<animal id="boss23" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="walk_boss" />
		<walk defId="walk_boss" />
		<run defId="walk_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Walk_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss22" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="BeAttack_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Attack_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="Dead_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="wakeOfDeath" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf"
					showClass="WakeOfDeath_Boss_23" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>
        <shows>
			<show defId="walk_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Walk_Boss_2_23"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="hurt_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="BeAttack_Boss_2_23"
				  x_offset="0" 
				  y_offset="0" />
		    <show defId="attack_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Attack_Boss_2_23"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="die_boss" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level23/Boss.swf" 
				  showClass="Dead_Boss_2_23"
				  x_offset="0" 
				  y_offset="0" />
		</shows>


	</animal>
</boss1>
  <xiaoBing2>
	<!--敌人数据 -->
	<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
		<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
	</fallDownEffect>
	<enemyData>
	
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
		<data att="totalHp" value="1200000" />
		<data att="attack" value="6000" />
		<data att="expOfDieThisEnemy" value="60000" />
		<data att="defence" value="30" />
		<data att="dogdeRate" value="0.08" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.5" />
		<data att="hitRate" value="0.09" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy22" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="120" walkSpeed="20"
		runSpeed="50">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="40" yRange="60"
			zRange="100" />

		<idle defId="walk_enemy22" />
		<walk defId="walk_enemy22" />
		<run defId="walk_enemy22" />
		<attack defId="attack_enemy22" />
		<hurt defId="hurt_enemy22" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy22" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow1" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="walk_enemy22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf"
					showClass="Walk_Monster_22" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="hurt_enemy22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf"
					showClass="BeAttack_Monster_22" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf"
					showClass="Attack_Monster_22" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf"
					showClass="Dead_Monster_22" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="change" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf"
					showClass="Change" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="enemyFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
		</animationDefinitions>
        <shows>
			
			<show defId="walk_enemy22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf" 
				  showClass="Walk_Monster_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="hurt_enemy22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf" 
				  showClass="BeAttack_Monster_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
		    <show defId="attack_enemy22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf" 
				  showClass="Attack_Monster_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="die_enemy22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Enemy.swf" 
				  showClass="Dead_Monster_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
		</shows>


	</animal>
</xiaoBing2>
   <boss2>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="4000000" />
		<data att="attack" value="8000" />
		<data att="expOfDieThisEnemy" value="200000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.25" />
	</enemyData>
	<animal id="boss22" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="180" walkSpeed="22"
		runSpeed="150">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="walk_boss22" />
		<walk defId="walk_boss22" />
		<run defId="walk_boss22" />
		<attack defId="attack_boss22" />
		<hurt defId="hurt_boss22" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss22" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			
			<animationDefinition id="walk_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Walk_Boss_22" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss22" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="BeAttack_Boss_22" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Attack_Boss_22" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss22" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Dead_Boss_22" x_offset="0" y_offset="0" />
			</animationDefinition>


			<animationDefinition id="change" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf"
					showClass="Change" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!--<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>-->
		</animationDefinitions>
        <shows>
			<show defId="walk_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Walk_Boss_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="hurt_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="BeAttack_Boss_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
		    <show defId="attack_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Attack_Boss_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
			<show defId="die_boss22" eqClassName="change"
				  swfPath="NewGameFolder/GuardingTangSengLevelMode/Level22/Boss.swf" 
				  showClass="Dead_Boss_InSuper_22"
				  x_offset="0" 
				  y_offset="0" />
		</shows>


	</animal>
</boss2>


</data>
