<?xml version="1.0" encoding="utf-8" ?>
<data>
	<skillLinkData>
		<data part1SkillId="bossSkill1" part2SkillId="dragonKingSkill1" />
		<data part1SkillId="bossSkill2" part2SkillId="dragonKingSkill2" />
		
	</skillLinkData>
	<hurtAnimation2 defId="hurt2_dragonKing" playFrameLabel="1" recoverFrameLabel="recover^stop^" />
	<autoAttackAI className="YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetAI1" />
	<petAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="500" />
		<data att="unableAttackMaxInterval" value="1500" />
	</petAttackData>
	<animal id="dragonKing" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="100" bodyHeight="140" walkSpeed="200"
		runSpeed="300">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-50" z="-1" xRange="200" yRange="100" zRange="100" />

		<idle defId="idle_dragonKing" />
		<walk defId="walk_dragonKing" />
		<run defId="run_dragonKing" />
		<attack defId="attack_dragonKing" />
		<hurt defId="hurt1_dragonKing" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_dragonKing" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1_dragonKing" />
		
       
	    
		<skill id="bossSkill1" className="YJFY.LevelMode2.Levels1.Skill_DragonKingSkill1" x="-300"
			y="-50" z="-1" xRange="400" yRange="100" zRange="200" bodyDefId="skill1Show_dragonKing" skillAttackReachFrameLabel="skillReach"
			skillEndFrameLabel="skillEnd^stop^" skillAttackEffectDefId="bossAttackEffect_dragonKing">
			<moveData swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf" className="MoveDataOfSkill" />
			<!--攻击特效 -->
			<animationDefinition id="bossAttackEffect_dragonKing"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="AttackEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
	
		<skill id="bossSkill2" className="YJFY.LevelMode2.Levels1.Skill_DragonKingSkill2"  bodyDefId="skill2Show_dragonKing"
			x="-400" y="-150" z="-1" xRange="800" yRange="300" zRange="1000" attackInterval="300">
			
		</skill>
		<skill id="SkillResurgence" className="YJFY.LevelMode2.Levels1.Skill_AllResurgence"  bodyDefId="skillShow_resurgence"
			x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="300">
		</skill>
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="IdleOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="WalkOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_dragonKing" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf" 
				showClass="RunOfDragonKing" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt1_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="Hurt1OfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="Hurt2OfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="AttackOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="DieOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<animationDefinition id="skill1Show_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="Skill1ShowOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill2Show_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="Skill2ShowOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="skillShow_resurgence" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="skill_resurgence" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1_dragonKing" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf"
					showClass="ShadowOfAutomaticPet1" x_offset="-10" y_offset="-5" />
			</animationDefinition>
			
			<!--攻击特效 -->
			<animationDefinition id="bossAttackEffect_dragonKing"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf"
					showClass="AttackEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
			
		</animationDefinitions>

        <shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="bossFootShadow1_dragonKing" showSeriesId="d"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet1"
				x_offset="-10" y_offset="-5" />
			<show defId="bossFootShadow1_dragonKing" showSeriesId="c"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet2"
				x_offset="-10" y_offset="-5" />
			<show defId="bossFootShadow1_dragonKing" showSeriesId="b"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet3"
				x_offset="-10" y_offset="-5" />
			<show defId="bossFootShadow1_dragonKing" showSeriesId="a"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet4"
				x_offset="-10" y_offset="-5" />
			<show defId="bossFootShadow1_dragonKing" showSeriesId="s"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet5"
				x_offset="-10" y_offset="-5" />
				
		</shows>

	</animal>
</data>