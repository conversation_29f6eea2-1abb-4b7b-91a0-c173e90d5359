<?xml version="1.0" encoding="utf-8" ?>
<data>
	<skillLinkData>
		<data part1SkillId="Skill_BossSkill1" part2SkillId="Nezha3Skill1" />
		<data part1SkillId="BossGenSuiTarget" part2SkillId="Nezha3Skill3" />
		<data part1SkillId="BossDaZhao" part2SkillId="Nezha3Skill2" />
	</skillLinkData>
	<!--<hurtAnimation2 defId="hurt2_jiaoRen" playFrameLabel="1" recoverFrameLabel="recover^stop^" />-->
	<autoAttackAI className="YJFY.GameEntity.XydzjsAutoAttackPet.AutoAttackPetAI1" />
	<petAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="500" />
		<data att="unableAttackMaxInterval" value="1500" />
	</petAttackData>
	<petToSuper isCan="true" timeForBianshen="10" timeForHurt="7" hpPercentage="20"/>
	<getNewPositionInterval cdTime="1000"/>   
	<animal id="Nezha3" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="100" bodyHeight="120" walkSpeed="150"
		runSpeed="300">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="-20" y="-50" z="-1" xRange="200" yRange="100" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
		
		<skill id="BossGenSuiTarget" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" x="-210"
			y="-150" z="-1" xRange="420" yRange="300" zRange="100" bodyDefId="bianShenZhuan"  hurtDuration="1000"  
			skillAttackEffectDefId="bossSkill1AttackEffect" randomPlaceXRange="10" randomPlaceYRange="-60" attackInterval="500"
			skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="bianShenskillEnd^stop^" >
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao" x="-20"
			y="-10" z="-1" xRange="400" yRange="150" zRange="100" bodyDefId="skill1Animation"  hurtDuration="5000" 
			bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^" effectAddtoTargetId="yuanQuan"
			everyEntityAddShowIsFrontOfBody="0">
			<shakeView swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf" className="ShakeView" />
		    <animationDefinition id="yuanQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<skill id="BossDaZhao" className="YJFY.BossMode.Boss1.BossNaZhaKunBang"  bodyDefId="daZhaoBodyShow" hurtDuration="5000" addBuffId="kunbang" dropShowDefId="daZhaoDropShow" attTimer="4" moveSpeed="500">
			<dropAttackRange x="-50" y="-50" z="-1" xRange="100" yRange="100" zRange="100" />
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="kunbang" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="KunBang" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</skill>
		<skill id="SkillResurgence" className="YJFY.LevelMode2.Levels1.Skill_AllResurgence"  bodyDefId="skillShow_resurgence"
			x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="300">
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf" 
				showClass="CBossRun" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossBeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossDead" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="bianShenZhuan" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="SkillNormalWaitEffect_bianshen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoDropShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="ItemDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillShow_resurgence" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="skill_resurgence" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="change" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>
			<animationDefinition id="backAnimation" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
						showClass="Back" x_offset="0" y_offset="0" />
				</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossChallengeBossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
			<animationDefinition id="kunbang" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="KunBang" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="yuanQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>
         <shows>
				<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="bossFootShadow1" showSeriesId="d"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet1"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="c"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet2"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="b"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet3"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="a"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet4"
				x_offset="0" y_offset="0" />
			<show defId="bossFootShadow1" showSeriesId="s"
				swfPath="NewGameFolder/AutomaticPetSource/Shadow.swf" showClass="ShadowOfAutomaticPet5"
				x_offset="0" y_offset="0" />
		</shows>
		<showsToSupper>

				<show defId="walk_boss" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossWalk_bianshen" x_offset="0" y_offset="0" />
				<show defId="run_boss" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossRun_bianshen" x_offset="0" y_offset="0" />
				<show defId="idle_boss" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossStand_bianshen" x_offset="0" y_offset="0" />
				<show defId="attack_boss" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossAttack_bianshen" x_offset="0" y_offset="0" />
				<show defId="bianShenZhuan" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="SkillNormalWaitEffect_bianshen" x_offset="0" y_offset="0" />
		</showsToSupper>
		<backshows>
				<show defId="walk_boss" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
				<show defId="run_boss" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossRun" x_offset="0" y_offset="0" />
				<show defId="idle_boss" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<show defId="attack_boss" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
				<show defId="skill1Animation" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
				<show defId="daZhaoBodyShow" eqClassName="change"
					swfPath="NewGameFolder/AutomaticPetSource/Meiguoduizhang3.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
		</backshows>


	</animal>
</data>