<?xml version="1.0" encoding="utf-8"?>
<UIData>
	    <!--PKmd5加密值-->
		<ServerMD5Key key="fd9fb8fcf5a5b1f3512c05a387f8853d" />
        <ForeverFashionAddAttr>
	    	<value addPlayerAttribute="renPin_foreverFashionAdd"  addPlayerAttributeValue="80_50_30_20_15_10" /> <!-- 人品-->
			<value addPlayerAttribute="blood_FashionAdd"  addPlayerAttributeValue="1050_550_350_150_100_1550_350_150_100_350_150_100_350" /> <!-- 生命-->
			<value addPlayerAttribute="magic_FashionAdd"  addPlayerAttributeValue="700_800_500_300_150_100_1500" />  <!-- 魔法-->
			<value addPlayerAttribute="regmp_FashionAdd"  addPlayerAttributeValue="15_20_15_15_10_15_10_15_10" />   <!-- 回蓝-->
			<value addPlayerAttribute="reghp_FashionAdd"  addPlayerAttributeValue="150_100_80_60_40_100_80_60" />   <!-- 回血-->
			<value addPlayerAttribute="attack_FashionAdd"  addPlayerAttributeValue="600_500_150_100_150_100_150_100_1000_500_150_100_500_150_100_150_100" />   <!-- 攻击-->
			<value addPlayerAttribute="defence_FashionAdd"  addPlayerAttributeValue="350_250_150_850_650_450_250_150_250_150" />   <!-- 防御-->
			<value addPlayerAttribute="hit_FashionAdd"  addPlayerAttributeValue="0.20_0.15_0.12_0.08_0.06_0.07_0.05_0.08_0.06_0.07_0.05" />   <!-- 命中-->
			<value addPlayerAttribute="dodge_FashionAdd"  addPlayerAttributeValue="0.08_0.06_0.20_0.15_0.12_0.08_0.06_0.12_0.08_0.06_0.12_0.08_0.06" />   <!-- 闪避-->
			<value addPlayerAttribute="criticalRate_FashionAdd"  addPlayerAttributeValue="15_20_10_8_10_30_15_12_10_5_8_15_12_10_5_8_15_12_10_5_8" />   <!-- 暴击-->
			<value addPlayerAttribute="viotValue_FashionAdd"  addPlayerAttributeValue="0.30_0.20_0.15_0.18_0.10_0.08_0.10_0.10_0.08_0.10_0.10_0.08_0.10" />   <!-- 防爆-->
			<value addPlayerAttribute="offensiveValue_FashionAdd"  addPlayerAttributeValue="160_90_150_130_200_80_50_130_100_80_50_80_50_130_100_80_50" />   <!--先手--> 
		</ForeverFashionAddAttr>
		<blessProtectData ticket="19" ticketId="2023" />
		
		
		
		
		
		
		<!-- 捡到宠物时， 被动技能获取概率 -->
		<!-- level:宠物等级 thePassiveOdds： 与天赋类型相同的被动技能的获取概率 otherPassiveOdds： 与天赋类型不同的被动技能的获取概率 -->

		<PetOddsPassiveSkills>
			<item level="1" thePassiveOdds="15" otherPassiveOdds="5" />
			<item level="2" thePassiveOdds="60" otherPassiveOdds="35" />
			<item level="3" thePassiveOdds="70" otherPassiveOdds="40" />

		</PetOddsPassiveSkills>

        <!--宠物生成时获取一个技能的几率-->
		<PetOddsOnePassiveSkillInCreate odd="85" />



		<!-- 宠物升级时获取被动技能和提升天赋的概率 -->
		<!-- oddUpgradeTalent: 天赋升级概率 oddValue： 新的被动技能获取概率 -->

		<PetOddsUpgrade oddUpgradeTalent="0" oddValue="3" />


        <!-- 宠物孵化获取各等级天赋的概率 -->
             <!-- 普通孵化 -->
        <PetOddsTalentInNormalHatch   oneTalent="99" twoTalent="1" threeTalent="0" />
             <!-- 精致孵化 -->
        <PetOddsTalentInFineHatch   oneTalent="0"  twoTalent="99.9"  threeTalent="0.1" />
        
        

		<!-- 宠物进化时，天赋升级 被动技能获取概率 -->
		<!-- level : 宠物等级 oddUpgradeTalent: 宠物天赋升级概率 oddValue： 新的技能获取概率 -->

		<PetOddsEvolution>
			<item level="1" oddUpgradeTalent="1" oddValue="10" />
			<item level="2" oddUpgradeTalent="1" oddValue="20" />
			<item level="3" oddUpgradeTalent="1" oddValue="30" />

		</PetOddsEvolution>
		<!--重置挑战宠物boss的点券-->
		<resetPetChallenge ticket="10" ticketId="1128" />
		<Hatch  hatchPrice="1" />
		<ResetCompleteEveryDayTask   price="20" />
		<LockCellPrice  packageTicket="5" packageTicketId="391" storageTicket="5" storageTicketId="439" publicStorageTicket="5" publicStorageTicketId="440" />
        <TaskLimit everyDayTaskNum="3"  activityTaskNum="3"/>
		<!--背包金币上限-->
		<Money  maxMoney="99999999"  maxDxValue="1000000"/>
		<PKPoint maxPKPoint="30000" maxDxValue="10"/>
		<PKShop  maxBuyNum="20" />
		<!-- pk数据  compareDateForTwoPlayerPK 双人PK重置日期  resetTime  双人pk重置时间长度（小时） -->
		<PKData  compareDateForTwoPlayerPK="2013-03-04 00:00:00" resetTime="168"  resetPKPointTickets="2_4_8_16_30" resetPKPointTicketIds="1491_1492_1493_1494_1495" OneKeyTickets="20" OneKeyTicketsId="2527" />
		<!-- vip pk -->

		<ZhouNianHuoDong StartData="2016-03-09 00:00:00" EndData="2017-11-15 00:00:00" StartTime="19:00:00" EndTime="21:00:00" hour="48" isWeekday="0"/>

		<!-- 五倍经验 -->
		<ZhouNianHuoDongWuBeiJingyan StartData="2015-11-09 00:00:00" EndData="2017-11-15 00:00:00" StartTime="16:00:00" EndTime="22:00:00"/>
		<guanYinProtectData ticket="2" ticketId="1490" />
		<PetPotionUseRestriction>
		      <item id="11000009" className="Potion_PetTrainPotion"  level="3" petLevel="20" />
			  <item id="11000010" className="Potion_XiSuiPill"   level="3" petLevel="20" />
			  <item id="11000011" className="Potion_TalentStrengthenPill" level="3" petLevel="20" />
			  <item id="11000012" className="Potion_TalentStrengthenPill_XianPing" level="3" petLevel="20" />
			  <item id="11000018" className="Potion_ResetPetPassiveSkillPotion" level="3" petLevel="20" />
			  <item id="11000033" className="Potion_zhulongkaiwuyaoji" level="3" petLevel="30" />
              <item id="11000034" className="Potion_zhulongbeidongchongzhi" level="3" petLevel="30" />
		</PetPotionUseRestriction>
		<MirageEssence  baseNum="0.75" transFormMinRate="10"  transFormMaxRate="20" money="10000" baseSuccessRate="90"/>
		
		
		<!--幻化数/天 -->
		<MirageNum num="5" ticketPrice="50" ticketId="3240" />
		
		<!--快速使用物品-->
		<FastUseEquip>
			<blood>
				<item id="11000008" />
				<item id="11000017" />
			</blood>
			<magic>
				<item id="11000007" />
				<item id="11000016" />
			</magic>
		</FastUseEquip>
		
		<!--怒气-->
		<NuQi   addDelay="1" addValuePerS="40" decValuePerS="-5" allValue="1200" />
		
		<!--世界boss相关配置-->
		<WorldBossData  maxPowerValue="120" recoverPowerValuePerS="0.0167" roundNumForNowOverBtn="2" />
			
		
		
		<Key>
	   <Key>
			<item key=" J"  />
			<item key="K" />
			<item key="L" />
			<item key="U" />
			<item key="I" />
			<item key="O" />
		</Key>
		<Key>
			<item key=" 1"  />
			<item key="2" />
			<item key="3" />
			<item key="4" />
			<item key="5" />
			<item key="6" />
		</Key>
	
	</Key>
	
	<ChoiceBtns>
		<DetailBtn>
			<show equipmentType="scroll" detailShowClassName="UI.EquipmentDetailShow.ScrollDetailShow" />
			<show equipmentType="pet" detailShowClassName="UI.EquipmentDetailShow.PetDetailShow"/>
			<show equipmentType="egg" detailShowClassName="UI.EquipmentDetailShow.EggDetailShow"/>
		</DetailBtn>
	</ChoiceBtns>
	<CellNum>
		<package num="240" />   <!--背包-->
		<storage num="360" />    <!--仓库-->
		<publicStorage num="48" />
	</CellNum>
	<EquipmentUpgradeMaterial>
		<item equipmentType="weapon" implictProPlayerId="daSheng"  levelRange="0_8" materialId="10500022" />
		<item equipmentType="clothes" implictProPlayerId="daSheng" levelRange="0_8" materialId="10500022" />
		<item equipmentType="gourd" implictProPlayerId="daSheng" levelRange="0_8" materialId="10500022" />
		<item equipmentType="necklace" implictProPlayerId="daSheng" levelRange="0_8" materialId="10500022" />
		
		<item equipmentType="weapon" implictProPlayerId="shengLing"  levelRange="0_8" materialId="10500040" />
		<item equipmentType="clothes" implictProPlayerId="shengLing" levelRange="0_8" materialId="10500040" />
		<item equipmentType="gourd" implictProPlayerId="shengLing" levelRange="0_8" materialId="10500040" />
		<item equipmentType="necklace" implictProPlayerId="shengLing" levelRange="0_8" materialId="10500040" />
		
		<item equipmentType="weapon" implictProPlayerId="qiangHua"  levelRange="0_8" materialId="10500041" />
		<item equipmentType="clothes" implictProPlayerId="qiangHua" levelRange="0_8" materialId="10500041" />
		<item equipmentType="gourd" implictProPlayerId="qiangHua" levelRange="0_8" materialId="10500041" />
		<item equipmentType="necklace" implictProPlayerId="qiangHua" levelRange="0_8" materialId="10500041" />
		
		<item equipmentType="weapon" implictProPlayerId="shenYuan"  levelRange="0_8" materialId="10500041" />
		<item equipmentType="clothes" implictProPlayerId="shenYuan" levelRange="0_8" materialId="10500041" />
		<item equipmentType="gourd" implictProPlayerId="shenYuan" levelRange="0_8" materialId="10500041" />
		<item equipmentType="necklace" implictProPlayerId="shenYuan" levelRange="0_8" materialId="10500041" />

		<item equipmentType="weapon" implictProPlayerId="anHei"  levelRange="0_8" materialId="10500072" />
		<item equipmentType="clothes" implictProPlayerId="anHei" levelRange="0_8" materialId="10500072" />
		<item equipmentType="gourd" implictProPlayerId="anHei" levelRange="0_8" materialId="10500072" />
		<item equipmentType="necklace" implictProPlayerId="anHei" levelRange="0_8" materialId="10500072" />
		
		<item equipmentType="weapon" implictProPlayerId="ShengYu"  levelRange="0_8" materialId="10500090" />
		<item equipmentType="clothes" implictProPlayerId="ShengYu" levelRange="0_8" materialId="10500090" />
		<item equipmentType="gourd" implictProPlayerId="ShengYu" levelRange="0_8" materialId="10500090" />
		<item equipmentType="necklace" implictProPlayerId="ShengYu" levelRange="0_8" materialId="10500090" />
	
		<item equipmentType="weapon" implictProPlayerId="ShenYu"  levelRange="0_8" materialId="10500091" />
		<item equipmentType="clothes" implictProPlayerId="ShenYu" levelRange="0_8" materialId="10500091" />
		<item equipmentType="gourd" implictProPlayerId="ShenYu" levelRange="0_8" materialId="10500091" />
		<item equipmentType="necklace" implictProPlayerId="ShenYu" levelRange="0_8" materialId="10500091" />
		
		<item equipmentType="weapon" implictProPlayerId="LingYin"  levelRange="0_8" materialId="10500134" />
		<item equipmentType="clothes" implictProPlayerId="LingYin" levelRange="0_8" materialId="10500134" />
		<item equipmentType="gourd" implictProPlayerId="LingYin" levelRange="0_8" materialId="10500134" />
		<item equipmentType="necklace" implictProPlayerId="LingYin" levelRange="0_8" materialId="10500134" />
		
	
		<item equipmentType="weapon" implictProPlayerId="ShenYin"  levelRange="0_8" materialId="10500135" />
		<item equipmentType="clothes" implictProPlayerId="ShenYin" levelRange="0_8" materialId="10500135" />
		<item equipmentType="gourd" implictProPlayerId="ShenYin" levelRange="0_8" materialId="10500135" />
		<item equipmentType="necklace" implictProPlayerId="ShenYin" levelRange="0_8" materialId="10500135" />	
		
		<item equipmentType="weapon" implictProPlayerId="ChenGuang"  levelRange="0_8" materialId="10500193" />
		<item equipmentType="clothes" implictProPlayerId="ChenGuang" levelRange="0_8" materialId="10500193" />
		<item equipmentType="gourd" implictProPlayerId="ChenGuang" levelRange="0_8" materialId="10500193" />
		<item equipmentType="necklace" implictProPlayerId="ChenGuang" levelRange="0_8" materialId="10500193" />
		
	
		<item equipmentType="weapon" implictProPlayerId="ShenGuang"  levelRange="0_8" materialId="10500194" />
		<item equipmentType="clothes" implictProPlayerId="ShenGuang" levelRange="0_8" materialId="10500194" />
		<item equipmentType="gourd" implictProPlayerId="ShenGuang" levelRange="0_8" materialId="10500194" />
		<item equipmentType="necklace" implictProPlayerId="ShenGuang" levelRange="0_8" materialId="10500194" />	
		
		<item equipmentType="weapon" implictProPlayerId="ShiShi"  levelRange="0_8" materialId="10500244" />
		<item equipmentType="clothes" implictProPlayerId="ShiShi" levelRange="0_8" materialId="10500244" />
		<item equipmentType="gourd" implictProPlayerId="ShiShi" levelRange="0_8" materialId="10500244" />
		<item equipmentType="necklace" implictProPlayerId="ShiShi" levelRange="0_8" materialId="10500244" />
		
	
		<item equipmentType="weapon" implictProPlayerId="ChuanShuo"  levelRange="0_8" materialId="10500245" />
		<item equipmentType="clothes" implictProPlayerId="ChuanShuo" levelRange="0_8" materialId="10500245" />
		<item equipmentType="gourd" implictProPlayerId="ChuanShuo" levelRange="0_8" materialId="10500245" />
		<item equipmentType="necklace" implictProPlayerId="ChuanShuo" levelRange="0_8" materialId="10500245" />		
		
	</EquipmentUpgradeMaterial>
	
	
	<LookRechargeDetail   url="http://my.4399.com/forums-thread-tagid-81260-id-37030893.html" />
		<testPayMoney   value="2000" />
		<testPKRankListRank  rank="1" />
		
		
		<detection>
			<clothesEquipmentMaxDefence>6100</clothesEquipmentMaxDefence>
			<clothesEquipmentMaxRiot>1.80</clothesEquipmentMaxRiot>
			<weaponEquipmentMaxAttack>14000</weaponEquipmentMaxAttack>
			<necklaceEquipmentCriticalRate>200</necklaceEquipmentCriticalRate>
			<gourdEquipmentMaxMaxMagic>7000</gourdEquipmentMaxMaxMagic>
			<EquipmentMaxRenpin>60</EquipmentMaxRenpin>
			<type value="1">
				<preciousEquipmentBaseMaxNum>3</preciousEquipmentBaseMaxNum>
				<preciousEquipmentSpMaxNum>3</preciousEquipmentSpMaxNum>
				<!-- 基础属性:人品 -->
				<preciousEquipmentBaseaddrenpinMaxUp>18</preciousEquipmentBaseaddrenpinMaxUp>
				<!-- 基础属性:魔法 -->
				<preciousEquipmentBaseaddmagicMaxUp>17</preciousEquipmentBaseaddmagicMaxUp>
				<!-- 基础属性:血量 -->
				<preciousEquipmentBaseaddhpMaxUp>24</preciousEquipmentBaseaddhpMaxUp>
				<!-- 基础属性:防爆 -->
				<preciousEquipmentBaseaddfangbaoMaxUp>18</preciousEquipmentBaseaddfangbaoMaxUp>
				<!-- 基础属性:闪避 -->
				<preciousEquipmentBaseaddshanbiMaxUp>24</preciousEquipmentBaseaddshanbiMaxUp>
				<!-- 基础属性:暴击 -->
				<preciousEquipmentBaseaddbaojiMaxUp>16</preciousEquipmentBaseaddbaojiMaxUp>
				<!-- 基础属性:命中 -->
				<preciousEquipmentBaseaddmingzhongMaxUp>24</preciousEquipmentBaseaddmingzhongMaxUp>
				<!-- 基础属性:攻击 -->
				<preciousEquipmentBaseaddgongjiMaxUp>18</preciousEquipmentBaseaddgongjiMaxUp>
				<!-- 特殊属性: 2倍经验和金钱 -->
				<preciousEquipmentdoubleexpgoldavgValue>0</preciousEquipmentdoubleexpgoldavgValue>
				<preciousEquipmentdoubleexpgoldMax>2.5</preciousEquipmentdoubleexpgoldMax>
				<preciousEquipmentdoubleexpgoldMin>2</preciousEquipmentdoubleexpgoldMin>
				<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
				<preciousEquipmentincreaseAttackavgValue>100</preciousEquipmentincreaseAttackavgValue>
				<preciousEquipmentincreaseAttackMax>40</preciousEquipmentincreaseAttackMax>
				<preciousEquipmentincreaseAttackMin>10</preciousEquipmentincreaseAttackMin>
				<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
				<preciousEquipmentincreaseShanbiavgValue>1000</preciousEquipmentincreaseShanbiavgValue>
				<preciousEquipmentincreaseShanbiMax>2.2</preciousEquipmentincreaseShanbiMax>
				<preciousEquipmentincreaseShanbiMin>0.2</preciousEquipmentincreaseShanbiMin>
				<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
				<preciousEquipmentdincreasehpavgValue>1000</preciousEquipmentdincreasehpavgValue>
				<preciousEquipmentdincreasehpMax>3</preciousEquipmentdincreasehpMax>
				<preciousEquipmentdincreasehpMin>1</preciousEquipmentdincreasehpMin>
				<!-- 特殊属性: 防御力减半 -->
				<preciousEquipmentddincreaseDefavgValue>0</preciousEquipmentddincreaseDefavgValue>
				<preciousEquipmentddincreaseDefMax>30</preciousEquipmentddincreaseDefMax>
				<preciousEquipmentddincreaseDefMin>10</preciousEquipmentddincreaseDefMin>
				<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
				<preciousEquipmentincreaseBaojiavgValue>1000</preciousEquipmentincreaseBaojiavgValue>
				<preciousEquipmentincreaseBaojiMax>2.1</preciousEquipmentincreaseBaojiMax>
				<preciousEquipmentincreaseBaojiMin>0.5</preciousEquipmentincreaseBaojiMin>
				<!-- 特殊属性: 将人品值转换为防御力 -->
				<preciousEquipmentchangerenpinavgValue>0</preciousEquipmentchangerenpinavgValue>
				<preciousEquipmentchangerenpinMax>19</preciousEquipmentchangerenpinMax>
				<preciousEquipmentchangerenpinMin>10</preciousEquipmentchangerenpinMin>
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
				<preciousEquipmentincreaseMingzhongavgValue>10</preciousEquipmentincreaseMingzhongavgValue>
				<preciousEquipmentincreaseMingzhongMax>0.5</preciousEquipmentincreaseMingzhongMax>
				<preciousEquipmentincreaseMingzhongMin>1.6</preciousEquipmentincreaseMingzhongMin>
			</type>
			<type value="2">
				<!-- 基础属性:人品 -->
				<preciousEquipmentBaseaddrenpinMaxUp>18</preciousEquipmentBaseaddrenpinMaxUp>
				<!-- 基础属性:魔法 -->
				<preciousEquipmentBaseaddmagicMaxUp>17</preciousEquipmentBaseaddmagicMaxUp>
				<!-- 基础属性:血量 -->
				<preciousEquipmentBaseaddhpMaxUp>24</preciousEquipmentBaseaddhpMaxUp>
				<!-- 基础属性:防爆 -->
				<preciousEquipmentBaseaddfangbaoMaxUp>18</preciousEquipmentBaseaddfangbaoMaxUp>
				<!-- 基础属性:闪避 -->
				<preciousEquipmentBaseaddshanbiMaxUp>24</preciousEquipmentBaseaddshanbiMaxUp>
				<!-- 基础属性:暴击 -->
				<preciousEquipmentBaseaddbaojiMaxUp>16</preciousEquipmentBaseaddbaojiMaxUp>
				<!-- 基础属性:命中 -->
				<preciousEquipmentBaseaddmingzhongMaxUp>24</preciousEquipmentBaseaddmingzhongMaxUp>
				<!-- 基础属性:攻击 -->
				<preciousEquipmentBaseaddgongjiMaxUp>18</preciousEquipmentBaseaddgongjiMaxUp>
				<!-- 特殊属性: 2倍经验和金钱 -->
				<preciousEquipmentdoubleexpgoldavgValue>0</preciousEquipmentdoubleexpgoldavgValue>
				<preciousEquipmentdoubleexpgoldMax>2.5</preciousEquipmentdoubleexpgoldMax>
				<preciousEquipmentdoubleexpgoldMin>2</preciousEquipmentdoubleexpgoldMin>
				<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
				<preciousEquipmentincreaseAttackavgValue>100</preciousEquipmentincreaseAttackavgValue>
				<preciousEquipmentincreaseAttackMax>40</preciousEquipmentincreaseAttackMax>
				<preciousEquipmentincreaseAttackMin>10</preciousEquipmentincreaseAttackMin>
				<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
				<preciousEquipmentincreaseShanbiavgValue>1000</preciousEquipmentincreaseShanbiavgValue>
				<preciousEquipmentincreaseShanbiMax>2.2</preciousEquipmentincreaseShanbiMax>
				<preciousEquipmentincreaseShanbiMin>0.2</preciousEquipmentincreaseShanbiMin>
				<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
				<preciousEquipmentdincreasehpavgValue>1000</preciousEquipmentdincreasehpavgValue>
				<preciousEquipmentdincreasehpMax>3</preciousEquipmentdincreasehpMax>
				<preciousEquipmentdincreasehpMin>1</preciousEquipmentdincreasehpMin>
				<!-- 特殊属性: 防御力减半 -->
				<preciousEquipmentddincreaseDefavgValue>0</preciousEquipmentddincreaseDefavgValue>
				<preciousEquipmentddincreaseDefMax>30</preciousEquipmentddincreaseDefMax>
				<preciousEquipmentddincreaseDefMin>10</preciousEquipmentddincreaseDefMin>
				<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
				<preciousEquipmentincreaseBaojiavgValue>1000</preciousEquipmentincreaseBaojiavgValue>
				<preciousEquipmentincreaseBaojiMax>2.1</preciousEquipmentincreaseBaojiMax>
				<preciousEquipmentincreaseBaojiMin>0.5</preciousEquipmentincreaseBaojiMin>
				<!-- 特殊属性: 将人品值转换为防御力 -->
				<preciousEquipmentchangerenpinavgValue>0</preciousEquipmentchangerenpinavgValue>
				<preciousEquipmentchangerenpinMax>19</preciousEquipmentchangerenpinMax>
				<preciousEquipmentchangerenpinMin>10</preciousEquipmentchangerenpinMin>
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
				<preciousEquipmentincreaseMingzhongavgValue>10</preciousEquipmentincreaseMingzhongavgValue>
				<preciousEquipmentincreaseMingzhongMax>0.5</preciousEquipmentincreaseMingzhongMax>
				<preciousEquipmentincreaseMingzhongMin>1.6</preciousEquipmentincreaseMingzhongMin>
			</type>
			
			<type value="3">
				<!-- 基础属性:人品 -->
				<preciousEquipmentBaseaddrenpinMaxUp>18</preciousEquipmentBaseaddrenpinMaxUp>
				<!-- 基础属性:魔法 -->
				<preciousEquipmentBaseaddmagicMaxUp>17</preciousEquipmentBaseaddmagicMaxUp>
				<!-- 基础属性:血量 -->
				<preciousEquipmentBaseaddhpMaxUp>24</preciousEquipmentBaseaddhpMaxUp>
				<!-- 基础属性:防爆 -->
				<preciousEquipmentBaseaddfangbaoMaxUp>18</preciousEquipmentBaseaddfangbaoMaxUp>
				<!-- 基础属性:闪避 -->
				<preciousEquipmentBaseaddshanbiMaxUp>24</preciousEquipmentBaseaddshanbiMaxUp>
				<!-- 基础属性:暴击 -->
				<preciousEquipmentBaseaddbaojiMaxUp>18</preciousEquipmentBaseaddbaojiMaxUp>
				<!-- 基础属性:命中 -->
				<preciousEquipmentBaseaddmingzhongMaxUp>24</preciousEquipmentBaseaddmingzhongMaxUp>
				<!-- 基础属性:攻击 -->
				<preciousEquipmentBaseaddgongjiMaxUp>18</preciousEquipmentBaseaddgongjiMaxUp>
				<!-- 特殊属性: 2倍经验和金钱 -->
				<preciousEquipmentdoubleexpgoldavgValue>0</preciousEquipmentdoubleexpgoldavgValue>
				<preciousEquipmentdoubleexpgoldMax>2.5</preciousEquipmentdoubleexpgoldMax>
				<preciousEquipmentdoubleexpgoldMin>2</preciousEquipmentdoubleexpgoldMin>
				<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
				<preciousEquipmentincreaseAttackavgValue>100</preciousEquipmentincreaseAttackavgValue>
				<preciousEquipmentincreaseAttackMax>40</preciousEquipmentincreaseAttackMax>
				<preciousEquipmentincreaseAttackMin>10</preciousEquipmentincreaseAttackMin>
				<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
				<preciousEquipmentincreaseShanbiavgValue>1000</preciousEquipmentincreaseShanbiavgValue>
				<preciousEquipmentincreaseShanbiMax>2.2</preciousEquipmentincreaseShanbiMax>
				<preciousEquipmentincreaseShanbiMin>0.2</preciousEquipmentincreaseShanbiMin>
				<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
				<preciousEquipmentdincreasehpavgValue>1000</preciousEquipmentdincreasehpavgValue>
				<preciousEquipmentdincreasehpMax>3</preciousEquipmentdincreasehpMax>
				<preciousEquipmentdincreasehpMin>1</preciousEquipmentdincreasehpMin>
				<!-- 特殊属性: 防御力减半 -->
				<preciousEquipmentddincreaseDefavgValue>0</preciousEquipmentddincreaseDefavgValue>
				<preciousEquipmentddincreaseDefMax>30</preciousEquipmentddincreaseDefMax>
				<preciousEquipmentddincreaseDefMin>10</preciousEquipmentddincreaseDefMin>
				<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
				<preciousEquipmentincreaseBaojiavgValue>1000</preciousEquipmentincreaseBaojiavgValue>
				<preciousEquipmentincreaseBaojiMax>2.1</preciousEquipmentincreaseBaojiMax>
				<preciousEquipmentincreaseBaojiMin>0.5</preciousEquipmentincreaseBaojiMin>
				<!-- 特殊属性: 将人品值转换为防御力 -->
				<preciousEquipmentchangerenpinavgValue>0</preciousEquipmentchangerenpinavgValue>
				<preciousEquipmentchangerenpinMax>19</preciousEquipmentchangerenpinMax>
				<preciousEquipmentchangerenpinMin>10</preciousEquipmentchangerenpinMin>
				<!-- 特殊属性: 生命值每降低百分之十增加 点命中 -->
				<preciousEquipmentincreaseMingzhongavgValue>10</preciousEquipmentincreaseMingzhongavgValue>
				<preciousEquipmentincreaseMingzhongMax>0.5</preciousEquipmentincreaseMingzhongMax>
				<preciousEquipmentincreaseMingzhongMin>1.6</preciousEquipmentincreaseMingzhongMin>
			</type>
			
			<clothEqAddAttr><!-- 套装-->
	    		<value addPlayerAttribute="renPin_MagicAdd"  addPlayerAttributeValue="40" /> <!-- 人品-->
				<value addPlayerAttribute="blood_MagicAdd"  addPlayerAttributeValue="700" /> <!-- 生命-->
				<value addPlayerAttribute="magic_MagicAdd"  addPlayerAttributeValue="300" />  <!-- 魔法-->
				<value addPlayerAttribute="regmp_MagicAdd"  addPlayerAttributeValue="7" />   <!-- 回蓝-->
				<value addPlayerAttribute="reghp_MagicAdd"  addPlayerAttributeValue="100" />   <!-- 回血-->
				<value addPlayerAttribute="attack_MagicAdd"  addPlayerAttributeValue="300" />   <!-- 攻击-->
				<value addPlayerAttribute="defence_MagicAdd"  addPlayerAttributeValue="600" />   <!-- 防御-->
				<value addPlayerAttribute="hit_MagicAdd"  addPlayerAttributeValue="0.08" />   <!-- 命中-->
				<value addPlayerAttribute="dodge_MagicAdd"  addPlayerAttributeValue="0.15" />   <!-- 闪避-->
				<value addPlayerAttribute="criticalRate_MagicAdd"  addPlayerAttributeValue="5" />   <!-- 暴击-->
				<value addPlayerAttribute="viotValue_MagicAdd"  addPlayerAttributeValue="0.08" /> <!-- 防爆-->
				<value addPlayerAttribute="offensiveValue_MagicAdd"  addPlayerAttributeValue="30" /><!--先手-->
			</clothEqAddAttr>
			<weaponEqAddAttr><!-- 武器-->
	    		<value addPlayerAttribute="renPin_MagicAdd"  addPlayerAttributeValue="10" /> <!-- 人品-->
				<value addPlayerAttribute="blood_MagicAdd"  addPlayerAttributeValue="250" /> <!-- 生命-->
				<value addPlayerAttribute="magic_MagicAdd"  addPlayerAttributeValue="200" />  <!-- 魔法-->
				<value addPlayerAttribute="regmp_MagicAdd"  addPlayerAttributeValue="5" />   <!-- 回蓝-->
				<value addPlayerAttribute="reghp_MagicAdd"  addPlayerAttributeValue="80" />   <!-- 回血-->
				<value addPlayerAttribute="attack_MagicAdd"  addPlayerAttributeValue="700" />   <!-- 攻击-->
				<value addPlayerAttribute="defence_MagicAdd"  addPlayerAttributeValue="75" />   <!-- 防御-->
				<value addPlayerAttribute="hit_MagicAdd"  addPlayerAttributeValue="0.02" />   <!-- 命中-->
				<value addPlayerAttribute="dodge_MagicAdd"  addPlayerAttributeValue="0.02" />   <!-- 闪避-->
				<value addPlayerAttribute="criticalRate_MagicAdd"  addPlayerAttributeValue="6" />   <!-- 暴击-->
				<value addPlayerAttribute="viotValue_MagicAdd"  addPlayerAttributeValue="0.02" /> <!-- 防爆-->
				<value addPlayerAttribute="offensiveValue_MagicAdd"  addPlayerAttributeValue="50" /><!--先手-->
			</weaponEqAddAttr>
			<neckLaceEqAddAttr><!-- 项链-->
	    		<value addPlayerAttribute="renPin_MagicAdd"  addPlayerAttributeValue="20" /> <!-- 人品-->
				<value addPlayerAttribute="blood_MagicAdd"  addPlayerAttributeValue="225" /> <!-- 生命-->
				<value addPlayerAttribute="magic_MagicAdd"  addPlayerAttributeValue="300" />  <!-- 魔法-->
				<value addPlayerAttribute="regmp_MagicAdd"  addPlayerAttributeValue="20" />   <!-- 回蓝-->
				<value addPlayerAttribute="reghp_MagicAdd"  addPlayerAttributeValue="130" />   <!-- 回血-->
				<value addPlayerAttribute="attack_MagicAdd"  addPlayerAttributeValue="400" />   <!-- 攻击-->
				<value addPlayerAttribute="defence_MagicAdd"  addPlayerAttributeValue="200" />   <!-- 防御-->
				<value addPlayerAttribute="hit_MagicAdd"  addPlayerAttributeValue="0.13" />   <!-- 命中-->
				<value addPlayerAttribute="dodge_MagicAdd"  addPlayerAttributeValue="0.08" />   <!-- 闪避-->
				<value addPlayerAttribute="criticalRate_MagicAdd"  addPlayerAttributeValue="15" />   <!-- 暴击-->
				<value addPlayerAttribute="viotValue_MagicAdd"  addPlayerAttributeValue="0.05" /> <!-- 防爆-->
				<value addPlayerAttribute="offensiveValue_MagicAdd"  addPlayerAttributeValue="40" /><!--先手-->
			</neckLaceEqAddAttr>
			<gourdEqAddAttr><!--葫芦-->
	    		<value addPlayerAttribute="renPin_MagicAdd"  addPlayerAttributeValue="70" /> <!-- 人品-->
				<value addPlayerAttribute="blood_MagicAdd"  addPlayerAttributeValue="325" /> <!-- 生命-->
				<value addPlayerAttribute="magic_MagicAdd"  addPlayerAttributeValue="700" />  <!-- 魔法-->
				<value addPlayerAttribute="regmp_MagicAdd"  addPlayerAttributeValue="8" />   <!-- 回蓝-->
				<value addPlayerAttribute="reghp_MagicAdd"  addPlayerAttributeValue="70" />   <!-- 回血-->
				<value addPlayerAttribute="attack_MagicAdd"  addPlayerAttributeValue="200" />   <!-- 攻击-->
				<value addPlayerAttribute="defence_MagicAdd"  addPlayerAttributeValue="125" />   <!-- 防御-->
				<value addPlayerAttribute="hit_MagicAdd"  addPlayerAttributeValue="0.07" />   <!-- 命中-->
				<value addPlayerAttribute="dodge_MagicAdd"  addPlayerAttributeValue="0.05" />   <!-- 闪避-->
				<value addPlayerAttribute="criticalRate_MagicAdd"  addPlayerAttributeValue="10" />   <!-- 暴击-->
				<value addPlayerAttribute="viotValue_MagicAdd"  addPlayerAttributeValue="0.15" /> <!-- 防爆-->
				<value addPlayerAttribute="offensiveValue_MagicAdd"  addPlayerAttributeValue="80" /><!--先手-->
			</gourdEqAddAttr>
			
			<preciousEqAddAttr><!--灵符-->
	    		<value addPlayerAttribute="renPin_MagicAdd"  addPlayerAttributeValue="50" /> <!-- 人品-->
				<value addPlayerAttribute="blood_MagicAdd"  addPlayerAttributeValue="405" /> <!-- 生命-->
				<value addPlayerAttribute="magic_MagicAdd"  addPlayerAttributeValue="300" />  <!-- 魔法-->
				<value addPlayerAttribute="regmp_MagicAdd"  addPlayerAttributeValue="12" />   <!-- 回蓝-->
				<value addPlayerAttribute="reghp_MagicAdd"  addPlayerAttributeValue="40" />   <!-- 回血-->
				<value addPlayerAttribute="attack_MagicAdd"  addPlayerAttributeValue="500" />   <!-- 攻击-->
				<value addPlayerAttribute="defence_MagicAdd"  addPlayerAttributeValue="500" />   <!-- 防御-->
				<value addPlayerAttribute="hit_MagicAdd"  addPlayerAttributeValue="0.04" />   <!-- 命中-->
				<value addPlayerAttribute="dodge_MagicAdd"  addPlayerAttributeValue="0.05" />   <!-- 闪避-->
				<value addPlayerAttribute="criticalRate_MagicAdd"  addPlayerAttributeValue="8" />   <!-- 暴击-->
				<value addPlayerAttribute="viotValue_MagicAdd"  addPlayerAttributeValue="0.1" /> <!-- 防爆-->
				<value addPlayerAttribute="offensiveValue_MagicAdd"  addPlayerAttributeValue="35" /><!--先手-->
			</preciousEqAddAttr>
			
			
			<fashionEqAddAttr><!-- 时装-->
	    		<value addPlayerAttribute="renPin_foreverFashionAdd" addPlayerAttributeValue="80" /> <!-- 人品-->
				<value addPlayerAttribute="blood_FashionAdd" addPlayerAttributeValue="1800" /> <!-- 生命-->
				<value addPlayerAttribute="magic_FashionAdd" addPlayerAttributeValue="1500" />  <!-- 魔法-->
				<value addPlayerAttribute="regmp_FashionAdd" addPlayerAttributeValue="60" />   <!-- 回蓝-->
				<value addPlayerAttribute="reghp_FashionAdd" addPlayerAttributeValue="300" />   <!-- 回血-->
				<value addPlayerAttribute="attack_FashionAdd" addPlayerAttributeValue="1000" />   <!-- 攻击-->
				<value addPlayerAttribute="defence_FashionAdd" addPlayerAttributeValue="850" />   <!-- 防御-->
				<value addPlayerAttribute="hit_FashionAdd" addPlayerAttributeValue="0.20" />   <!-- 命中-->
				<value addPlayerAttribute="dodge_FashionAdd" addPlayerAttributeValue="0.20" />   <!-- 闪避-->
				<value addPlayerAttribute="criticalRate_FashionAdd" addPlayerAttributeValue="30" />   <!-- 暴击-->
				<value addPlayerAttribute="viotValue_FashionAdd" addPlayerAttributeValue="0.35" /> <!-- 防爆-->
				<value addPlayerAttribute="offensiveValue_FashionAdd" addPlayerAttributeValue="200" /><!--先手-->
			</fashionEqAddAttr>

			<petSkill promoteXMLId="0">
				<petSkillGuWu>161</petSkillGuWu>				
			    <petSkillJiNu>22</petSkillJiNu>
			    <petSkillFaShuZengQiang>5241</petSkillFaShuZengQiang>
			    <petSkillShouHu>204</petSkillShouHu>
			    <petSkillJianZhuang>199</petSkillJianZhuang>
			    <petSkillMiShuZhangWo>95</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>56</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>56</petSkillJiNengLingWu>
			    <petSkillShanBi>56</petSkillShanBi>
			    <petSkillGuBen>56</petSkillGuBen>
   			    <petSkillYiShuJingTong>56</petSkillYiShuJingTong>
			    <petSkillZhiShang>56</petSkillZhiShang>
			</petSkill>
			<petSkill promoteXMLId="1">
				<petSkillGuWu>350</petSkillGuWu>				
			    <petSkillJiNu>22</petSkillJiNu>
			    <petSkillFaShuZengQiang>8000</petSkillFaShuZengQiang>
			    <petSkillShouHu>350</petSkillShouHu>
			    <petSkillJianZhuang>350</petSkillJianZhuang>
			    <petSkillMiShuZhangWo>105</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>94.5</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>92</petSkillJiNengLingWu>
			</petSkill>
			<petSkill promoteXMLId="2">
				<petSkillGuWu>370</petSkillGuWu>				
			    <petSkillJiNu>28</petSkillJiNu>
			    <petSkillFaShuZengQiang>9400</petSkillFaShuZengQiang>
			    <petSkillShouHu>420</petSkillShouHu>
			    <petSkillJianZhuang>400</petSkillJianZhuang>
			    <petSkillMiShuZhangWo>150</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>94.5</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>92</petSkillJiNengLingWu>
			</petSkill>
			<petSkill promoteXMLId="3">
				<petSkillGuWu>250</petSkillGuWu>				
			    <petSkillJiNu>22</petSkillJiNu>
			    <petSkillFaShuZengQiang>5241</petSkillFaShuZengQiang>
			    <petSkillShouHu>204</petSkillShouHu>
			    <petSkillJianZhuang>199</petSkillJianZhuang>
			    <petSkillMiShuZhangWo>95</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>56</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>56</petSkillJiNengLingWu>
			</petSkill>
			<petSkill promoteXMLId="4">
				<petSkillGuWu>680</petSkillGuWu>				
			    <petSkillJiNu>27</petSkillJiNu>
			    <petSkillFaShuZengQiang>15900</petSkillFaShuZengQiang>
			    <petSkillShouHu>481</petSkillShouHu>
			    <petSkillJianZhuang>418</petSkillJianZhuang>
			    <petSkillMiShuZhangWo>150</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>99</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>99</petSkillJiNengLingWu>
			</petSkill>
			<petSkill promoteXMLId="5">
				<petSkillGuWu>264</petSkillGuWu>				
			    <petSkillJiNu>27</petSkillJiNu>
			    <petSkillFaShuZengQiang>15700</petSkillFaShuZengQiang>
			    <petSkillShouHu>270</petSkillShouHu>
			    <petSkillJianZhuang>270</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>95</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>56</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>56</petSkillJiNengLingWu>
			</petSkill>
			<petSkill promoteXMLId="6">
				<petSkillGuWu>442</petSkillGuWu>				
			    <petSkillJiNu>27</petSkillJiNu>
			    <petSkillFaShuZengQiang>13260</petSkillFaShuZengQiang>
			    <petSkillShouHu>260</petSkillShouHu>
			    <petSkillJianZhuang>260</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>90</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>62</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>62</petSkillJiNengLingWu>
			</petSkill>
			<petSkill promoteXMLId="7">
				<petSkillGuWu>350</petSkillGuWu>				
			    <petSkillJiNu>27</petSkillJiNu>
			    <petSkillFaShuZengQiang>7300</petSkillFaShuZengQiang>
			    <petSkillShouHu>306</petSkillShouHu>
			    <petSkillJianZhuang>306</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>91</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>77</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>77</petSkillJiNengLingWu>
			</petSkill>			
			<petSkill promoteXMLId="8">
				<petSkillGuWu>495</petSkillGuWu>				
			    <petSkillJiNu>27</petSkillJiNu>
			    <petSkillFaShuZengQiang>15000</petSkillFaShuZengQiang>
			    <petSkillShouHu>495</petSkillShouHu>
			    <petSkillJianZhuang>495</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>91</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>90</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>90</petSkillJiNengLingWu>
			</petSkill>
			
			<petSkill promoteXMLId="9">
				<petSkillGuWu>610</petSkillGuWu>				
			    <petSkillJiNu>28</petSkillJiNu>
			    <petSkillFaShuZengQiang>17000</petSkillFaShuZengQiang>
			    <petSkillShouHu>400</petSkillShouHu>
			    <petSkillJianZhuang>400</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>96</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>92</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>92</petSkillJiNengLingWu>
			</petSkill>
			<petSkill promoteXMLId="10">
				<petSkillGuWu>690</petSkillGuWu>				
			    <petSkillJiNu>29</petSkillJiNu>
			    <petSkillFaShuZengQiang>20100</petSkillFaShuZengQiang>
			    <petSkillShouHu>560</petSkillShouHu>
			    <petSkillJianZhuang>560</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>94</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>92</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>92</petSkillJiNengLingWu>
			</petSkill>
<!-- 			上限值=各个技能幻化最大值X1.3 -->
			<petSkill promoteXMLId="11">
				<petSkillGuWu>960</petSkillGuWu>				
			    <petSkillJiNu>28</petSkillJiNu>
			    <petSkillFaShuZengQiang>28000</petSkillFaShuZengQiang>
			    <petSkillShouHu>590</petSkillShouHu>
			    <petSkillJianZhuang>590</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>108</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>105</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>105</petSkillJiNengLingWu>
			</petSkill>
			
				<petSkill promoteXMLId="12">
				<petSkillGuWu>730</petSkillGuWu>				
			    <petSkillJiNu>29</petSkillJiNu>
			    <petSkillFaShuZengQiang>23000</petSkillFaShuZengQiang>
			    <petSkillShouHu>580</petSkillShouHu>
			    <petSkillJianZhuang>580</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>96</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>92</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>92</petSkillJiNengLingWu>
			</petSkill>

			<petSkill promoteXMLId="13">
			   
			    
			    <petSkillShouHu>510</petSkillShouHu>
			    <petSkillJianZhuang>510</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>95</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>55</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>50</petSkillJiNengLingWu>
				<petSkillShanBi>24</petSkillShanBi>
			    <petSkillGuBen>24</petSkillGuBen>
   			    <petSkillYiShuJingTong>44</petSkillYiShuJingTong>
			    <petSkillZhiShang>30</petSkillZhiShang>
				
			</petSkill>

			<petSkill promoteXMLId="14">
			   <petSkillShouHu>580</petSkillShouHu>
			    <petSkillJianZhuang>640</petSkillJianZhuang>
		        <petSkillMiShuZhangWo>111</petSkillMiShuZhangWo>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>68</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>83</petSkillJiNengLingWu>
				<petSkillShanBi>37</petSkillShanBi>
			    <petSkillGuBen>28</petSkillGuBen>
   			    <petSkillYiShuJingTong>56</petSkillYiShuJingTong>
			    <petSkillZhiShang>44</petSkillZhiShang>
				
			</petSkill>
			
					<petSkill promoteXMLId="15">
			  
				<petSkillGuWu>1200</petSkillGuWu>				
			    <petSkillJiNu>28</petSkillJiNu>
			    <petSkillFaShuZengQiang>37000</petSkillFaShuZengQiang>
			    <petSkillJianZhuang>540</petSkillJianZhuang>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>105</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>105</petSkillJiNengLingWu>
				<petSkillXuenu>63</petSkillXuenu>
				<petSkillKuangbao>97</petSkillKuangbao>
				<petSkillShixue>63</petSkillShixue>
				<petSkillYiShuJingTong>73</petSkillYiShuJingTong>
				
			</petSkill>
			
				<petSkill promoteXMLId="16">
			  
				<petSkillGuWu>970</petSkillGuWu>				
			    <petSkillJiNu>29</petSkillJiNu>
			    <petSkillFaShuZengQiang>28000</petSkillFaShuZengQiang>
			    <petSkillJianZhuang>410</petSkillJianZhuang>
			    <petSkillAoYiGuangHuang>8</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>105</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>105</petSkillJiNengLingWu>
				<petSkillXuenu>63</petSkillXuenu>
				<petSkillKuangbao>95</petSkillKuangbao>
				<petSkillShixue>63</petSkillShixue>
				<petSkillYiShuJingTong>73</petSkillYiShuJingTong>
				
			</petSkill>
			
			
			<petSkill promoteXMLId="17">
			  
				<petSkillGuWu>1660</petSkillGuWu>				
			    <petSkillJiNu>29</petSkillJiNu>
			    <petSkillFaShuZengQiang>28000</petSkillFaShuZengQiang>
			    <petSkillJianZhuang>375</petSkillJianZhuang>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>105</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>105</petSkillJiNengLingWu>
				<petSkillXuenu>60</petSkillXuenu>
				<petSkillKuangbao>95</petSkillKuangbao>
				<petSkillShixue>60</petSkillShixue>
				<petSkillYiShuJingTong>28</petSkillYiShuJingTong>
				<petSkillShouHu>1140</petSkillShouHu>
		        <petSkillMiShuZhangWo>122</petSkillMiShuZhangWo>
				<petSkillShanBi>28</petSkillShanBi>
				
			    <petSkillGuBen>28</petSkillGuBen>
			    <petSkillZhiShang>40</petSkillZhiShang>
				<petSkillMingzhong>28</petSkillMingzhong>
			</petSkill>
			
				<petSkill promoteXMLId="18">
			  
				<petSkillGuWu>3550</petSkillGuWu>				
			    <petSkillJiNu>41</petSkillJiNu>
			    <petSkillFaShuZengQiang>48500</petSkillFaShuZengQiang>
			    <petSkillJianZhuang>540</petSkillJianZhuang>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>105</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>105</petSkillJiNengLingWu>
				<petSkillXuenu>63</petSkillXuenu>
				<petSkillKuangbao>98</petSkillKuangbao>
				<petSkillShixue>60</petSkillShixue>
				<petSkillYiShuJingTong>41</petSkillYiShuJingTong>
				<petSkillShouHu>2250</petSkillShouHu>
		        <petSkillMiShuZhangWo>200</petSkillMiShuZhangWo>
				<petSkillShanBi>41</petSkillShanBi>
			    <petSkillGuBen>41</petSkillGuBen>
			    <petSkillZhiShang>41</petSkillZhiShang>
		        <petSkillMingzhong>41</petSkillMingzhong>
			</petSkill>	
			<!--穷奇1-3阶-->
			<petSkill promoteXMLId="19">
			  
				<petSkillGuWu>1660</petSkillGuWu>				
			    <petSkillJiNu>29</petSkillJiNu>
			    <petSkillFaShuZengQiang>28000</petSkillFaShuZengQiang>
			    <petSkillJianZhuang>375</petSkillJianZhuang>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>105</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>105</petSkillJiNengLingWu>
				<petSkillXuenu>60</petSkillXuenu>
				<petSkillKuangbao>95</petSkillKuangbao>
				<petSkillShixue>60</petSkillShixue>
				<petSkillYiShuJingTong>28</petSkillYiShuJingTong>
				<petSkillShouHu>1140</petSkillShouHu>
		        <petSkillMiShuZhangWo>122</petSkillMiShuZhangWo>
				<petSkillShanBi>28</petSkillShanBi>
				
			    <petSkillGuBen>28</petSkillGuBen>
			    <petSkillZhiShang>40</petSkillZhiShang>
				<petSkillMingzhong>28</petSkillMingzhong>
			</petSkill>	
			<!--穷奇超进化-->
			<petSkill promoteXMLId="20">
			  
				<petSkillGuWu>3550</petSkillGuWu>				
			    <petSkillJiNu>41</petSkillJiNu>
			    <petSkillFaShuZengQiang>72500</petSkillFaShuZengQiang>
			    <petSkillJianZhuang>699</petSkillJianZhuang>
			    <petSkillAoYiGuangHuang>7</petSkillAoYiGuangHuang>
			    <petSkillJiNengAoYi>105</petSkillJiNengAoYi>
			    <petSkillJiNengLingWu>105</petSkillJiNengLingWu>
				<petSkillXuenu>63</petSkillXuenu>
				<petSkillKuangbao>98</petSkillKuangbao>
				<petSkillShixue>60</petSkillShixue>
				<petSkillYiShuJingTong>41</petSkillYiShuJingTong>
				<petSkillShouHu>2500</petSkillShouHu>
		        <petSkillMiShuZhangWo>225</petSkillMiShuZhangWo>
				<petSkillShanBi>47</petSkillShanBi>
			    <petSkillGuBen>41</petSkillGuBen>
			    <petSkillZhiShang>41</petSkillZhiShang>
		        <petSkillMingzhong>47</petSkillMingzhong>
			</petSkill>	
		</detection>
		
		<entityShowData>
         <SunWuKong>
			<show  eqClassName="defaultClothes"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes0.swf" showClass="SunWuKong_ClotheShow_0"
				x_offset="0" y_offset="0" />
			<!--鼠装 -->
			<show  eqClassName="Clothes_Shu"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes1.swf" showClass="SunWuKong_ClotheShow_1"
				x_offset="0" y_offset="0" />
			<!--鸡装 -->
			<show  eqClassName="Clothes_Ji"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes2.swf" showClass="SunWuKong_ClotheShow_2"
				x_offset="0" y_offset="0" />
			<!--羊装 -->
			<show  eqClassName="Clothes_Yang"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes3.swf" showClass="SunWuKong_ClotheShow_3"
				x_offset="0" y_offset="0" />
			<!--狼装 -->
			<show  eqClassName="Clothes_Lang"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes4.swf" showClass="SunWuKong_ClotheShow_4"
				x_offset="0" y_offset="0" />
			<!--虎装 -->
			<show  eqClassName="Clothes_Hu"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes5.swf" showClass="SunWuKong_ClotheShow_5"
				x_offset="0" y_offset="0" />
			<!--孙悟空大圣装 -->
			<show  eqClassName="Clothes_JingLinBaoJia_DaSheng"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes6.swf" showClass="SunWuKong_ClotheShow_6"
				x_offset="0" y_offset="0" />
			<!--青龙混元甲·圣灵 -->
			<show  eqClassName="Clothes_QingLong_ShengLing"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes7.swf" showClass="SunWuKong_ClotheShow_7"
				x_offset="0" y_offset="0" />
			<!--真·青龙混元甲 -->
			<show  eqClassName="Clothes_QingLong_QiangHua"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes7.swf" showClass="SunWuKong_ClotheShow_7"
				x_offset="0" y_offset="0" />
			<!--深渊套装 -->
			<show  eqClassName="Clothes_ShenYuan_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes8.swf" showClass="SunWuKong_ClotheShow_8"
				x_offset="0" y_offset="0" />
			<!--暗黑套装 -->
			<show  eqClassName="Clothes_AnHei_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes9.swf" showClass="SunWuKong_ClotheShow_9"
				x_offset="0" y_offset="0" />				
				
			<!--圣域套装 -->
			<show  eqClassName="Clothes_ShengYu_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes10.swf" showClass="SunWuKong_ClotheShow_10"
				x_offset="0" y_offset="0" />		
				
				<!--神域套装 -->
			<show  eqClassName="Clothes_ShenYu_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes11.swf" showClass="SunWuKong_ClotheShow_11"
				x_offset="0" y_offset="0" />		
					
			<!--灵印套装 -->
			<show  eqClassName="Clothes_LingYin_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes12.swf" showClass="SunWuKong_ClotheShow_12"
				x_offset="0" y_offset="0" />

			<!--神印套装 -->
			<show  eqClassName="Clothes_ShenYin_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes13.swf" showClass="SunWuKong_ClotheShow_13"
				x_offset="0" y_offset="0" />		
				
			<!--晨光套装 -->
			<show  eqClassName="Clothes_ChenGuang_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes14.swf" showClass="SunWuKong_ClotheShow_14"
				x_offset="0" y_offset="0" />

			<!--神光套装 -->
			<show  eqClassName="Clothes_ShenGuang_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes15.swf" showClass="SunWuKong_ClotheShow_15"
				x_offset="0" y_offset="0" />				
			
			<!--史诗套装 -->
			<show  eqClassName="Clothes_ShiShi_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes16.swf" showClass="SunWuKong_ClotheShow_16"
				x_offset="0" y_offset="0" />

			<!--传说套装 -->
			<show  eqClassName="Clothes_ChuanShuo_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyClothes17.swf" showClass="SunWuKong_ClotheShow_17"
				x_offset="0" y_offset="0" />	
					
				
			<!--默认武器 -->
			<show  eqClassName="defaultWeapon"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon0.swf" showClass="SunWuKong_WeaponShow_0"
				x_offset="0" y_offset="0" />
			<!--行者棍 -->
			<show eqClassName="Weapon_XingZheGun"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon0.swf" showClass="SunWuKong_WeaponShow_0"
				x_offset="0" y_offset="0" />
			<!--试炼棍 -->
			<show eqClassName="Weapon_ShiLianGun"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon1.swf" showClass="SunWuKong_WeaponShow_1"
				x_offset="0" y_offset="0" />
			<!--刺牙 -->
			<show eqClassName="Weapon_CiYa"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon2.swf" showClass="SunWuKong_WeaponShow_2"
				x_offset="0" y_offset="0" />
			<!--龙脊 -->
			<show  eqClassName="Weapon_LongJi"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon3.swf" showClass="SunWuKong_WeaponShow_3"
				x_offset="0" y_offset="0" />
			<!--辉耀 -->
			<show  eqClassName="Weapon_HuiYao"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon4.swf" showClass="SunWuKong_WeaponShow_4"
				x_offset="0" y_offset="0" />
			<!--金箍棒·大圣 -->
			<show  eqClassName="Weapon_JinGuBan_DaSheng"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon5.swf" showClass="SunWuKong_WeaponShow_5"
				x_offset="0" y_offset="0" />
			<!--青龙神棍·圣灵 -->
			<show  eqClassName="Weapon_QingLong_ShengLing"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon6.swf" showClass="SunWuKong_WeaponShow_6"
				x_offset="0" y_offset="0" />
			<!--真·青龙神棍 -->
			<show  eqClassName="Weapon_QingLong_QiangHua"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon6.swf" showClass="SunWuKong_WeaponShow_6"
				x_offset="0" y_offset="0" />				
				
			<!--深渊战棍 -->
			<show  eqClassName="Weapon_ShenYuan_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon7.swf" showClass="SunWuKong_WeaponShow_7"
				x_offset="0" y_offset="0" />				
			<!--暗黑战棍 -->
			<show  eqClassName="Weapon_AnHei_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon8.swf" showClass="SunWuKong_WeaponShow_8"
				x_offset="0" y_offset="0" />				
				
				<!--圣域战棍 -->
			<show  eqClassName="Weapon_ShengYu_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon9.swf" showClass="SunWuKong_WeaponShow_9"
				x_offset="0" y_offset="0" />			
				
					<!--神域战棍 -->
			<show  eqClassName="Weapon_ShenYu_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon9.swf" showClass="SunWuKong_WeaponShow_9"
				x_offset="0" y_offset="0" />		

			<!--灵印战棍 -->
			<show  eqClassName="Weapon_LingYin_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon11.swf" showClass="SunWuKong_WeaponShow_11"
				x_offset="0" y_offset="0" />	
            
            <!--神印战棍 -->
			<show  eqClassName="Weapon_ShenYin_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon12.swf" showClass="SunWuKong_WeaponShow_12"
				x_offset="0" y_offset="0" />		
				
				
			<!--晨光战棍 -->
			<show  eqClassName="Weapon_ChenGuang_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon13.swf" showClass="SunWuKong_WeaponShow_13"
				x_offset="0" y_offset="0" />	
            
            <!--神光战棍 -->
			<show  eqClassName="Weapon_ShenGuang_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon13.swf" showClass="SunWuKong_WeaponShow_13"
				x_offset="0" y_offset="0" />		
			<!--史诗战棍 -->
			<show  eqClassName="Weapon_ShiShi_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon14.swf" showClass="SunWuKong_WeaponShow_14"
				x_offset="0" y_offset="0" />	
            
            <!--传说战棍 -->
			<show  eqClassName="Weapon_ChuanShuo_Monkey"
				swfPath="NewGameFolder/MonkeySource/MonkeyWeapon14.swf" showClass="SunWuKong_WeaponShow_14"
				x_offset="0" y_offset="0" />				
				
				
				
				
			<!-- 时装 -->
			<show eqClassName="Fashion_FeiJi"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion0.swf" showClass="SunWuKong_ClotheShow_Show_0"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_AoTeMan"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion1.swf" showClass="SunWuKong_ClotheShow_Show_1"
				x_offset="0" y_offset="0" />
			<show eqClassName="Fashion_HuoYing"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion2.swf" showClass="SunWuKong_ClotheShow_Show_2"
				x_offset="0" y_offset="0" />
			<show eqClassName="Fashion_NewYearFashion"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion3.swf" showClass="SunWuKong_ClotheShow_Show_3"
				x_offset="0" y_offset="0" />
			<show eqClassName="Fashion_QiLongZhu"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion4.swf" showClass="SunWuKong_ClotheShow_Show_4"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Fashion_Milu"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion5.swf" showClass="SunWuKong_ClotheShow_Show_5"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Fashion_Xueren"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion6.swf" showClass="SunWuKong_ClotheShow_Show_6"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Fashion_Jiqimao"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion7.swf" showClass="SunWuKong_ClotheShow_Show_7"
				x_offset="0" y_offset="0" />		
				
			<show eqClassName="Fashion_Tubaobao"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion8.swf" showClass="SunWuKong_ClotheShow_Show_8"
				x_offset="0" y_offset="0" />	
			<show eqClassName="Fashion_Weilaizhanshi"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion9.swf" showClass="SunWuKong_ClotheShow_Show_9"
				x_offset="0" y_offset="0" />	

			<show eqClassName="Fashion_AngelDevil"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion10.swf" showClass="SunWuKong_ClotheShow_Show_10"
				x_offset="0" y_offset="0" />

			<show eqClassName="Fashion_Super"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion11.swf" showClass="SunWuKong_ClotheShow_Show_11"
				x_offset="0" y_offset="0" />	
				
				
			<!--永久时装-->
			<show eqClassName="ForeverFashion_NewYearFashion"
				swfPath="NewGameFolder/MonkeySource/MonkeyFashion3.swf" showClass="SunWuKong_ClotheShow_Show_3"
				x_offset="0" y_offset="0" />
				
			
	</SunWuKong>
	     <BaiLongMa>
			<show  eqClassName="defaultClothes"
				swfPath="NewGameFolder/DragonSource/DragonClothes0.swf" showClass="BaiLongMa_ClotheShow_0"
				x_offset="0" y_offset="0" />
			<!--虾装 -->
			<show defId="dragonIdle" eqClassName="Clothes_Xia"
				swfPath="NewGameFolder/DragonSource/DragonClothes1.swf" showClass="BaiLongMa_ClotheShow_1"
				x_offset="0" y_offset="0" />
			<!--虾装 -->
			<show  eqClassName="Clothes_Xie"
				swfPath="NewGameFolder/DragonSource/DragonClothes2.swf" showClass="BaiLongMa_ClotheShow_2"
				x_offset="0" y_offset="0" />
			<!--鱼装 -->
			<show  eqClassName="Clothes_Yu"
				swfPath="NewGameFolder/DragonSource/DragonClothes3.swf" showClass="BaiLongMa_ClotheShow_3"
				x_offset="0" y_offset="0" />
			<!--乌贼装 -->
			<show  eqClassName="Clothes_WuZei"
				swfPath="NewGameFolder/DragonSource/DragonClothes4.swf" showClass="BaiLongMa_ClotheShow_4"
				x_offset="0" y_offset="0" />
			<!--鲨鱼装 -->
			<show  eqClassName="Clothes_ShaYu"
				swfPath="NewGameFolder/DragonSource/DragonClothes5.swf" showClass="BaiLongMa_ClotheShow_5"
				x_offset="0" y_offset="0" />
			<!--龙鳞袍甲·大圣 -->
			<show  eqClassName="Clothes_LongLinPaoJia_DaSheng"
				swfPath="NewGameFolder/DragonSource/DragonClothes6.swf" showClass="BaiLongMa_ClotheShow_6"
				x_offset="0" y_offset="0" />
		    <!--玄武天幻衣·圣灵 -->
			<show  eqClassName="Clothes_XuanWu_ShengLing"
				swfPath="NewGameFolder/DragonSource/DragonClothes7.swf" showClass="BaiLongMa_ClotheShow_7"
				x_offset="0" y_offset="0" />				
			  <!--真·玄武天幻衣 -->
			<show  eqClassName="Clothes_XuanWu_QiangHua"
				swfPath="NewGameFolder/DragonSource/DragonClothes7.swf" showClass="BaiLongMa_ClotheShow_7"
				x_offset="0" y_offset="0" />				
			  <!--深渊魔衣 -->
			<show  eqClassName="Clothes_ShenYuan_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes8.swf" showClass="BaiLongMa_ClotheShow_8"
				x_offset="0" y_offset="0" />						
			  <!--暗黑魔衣 -->
			<show  eqClassName="Clothes_AnHei_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes9.swf" showClass="BaiLongMa_ClotheShow_9"
				x_offset="0" y_offset="0" />						
				
			<!--圣域魔衣 -->
			<show  eqClassName="Clothes_ShengYu_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes10.swf" showClass="BaiLongMa_ClotheShow_10"
				x_offset="0" y_offset="0" />	
				
			<!--神域魔衣 -->
			<show  eqClassName="Clothes_ShenYu_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes11.swf" showClass="BaiLongMa_ClotheShow_11"
				x_offset="0" y_offset="0" />	

			<!--灵印魔衣 -->
			<show  eqClassName="Clothes_LingYin_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes12.swf" showClass="BaiLongMa_ClotheShow_12"
				x_offset="0" y_offset="0" />	
			<!--神印魔衣 -->
			<show  eqClassName="Clothes_ShenYin_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes13.swf" showClass="BaiLongMa_ClotheShow_13"
				x_offset="0" y_offset="0" />	
				
				
			<!--晨光魔衣 -->
			<show  eqClassName="Clothes_ChenGuang_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes14.swf" showClass="BaiLongMa_ClotheShow_14"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Clothes_ChenGuang_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes14.swf" showClass="BLMStand_Clothe_head_14"
				x_offset="0" y_offset="0" />		
			<!--神光魔衣 -->
			<show  eqClassName="Clothes_ShenGuang_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes15.swf" showClass="BaiLongMa_ClotheShow_15"
				x_offset="0" y_offset="0" />		
							
			<!--史诗魔衣 -->
			<show  eqClassName="Clothes_ShiShi_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes16.swf" showClass="BaiLongMa_ClotheShow_16"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Clothes_ShiShi_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes16.swf" showClass="BLMStand_Clothe_head_16"
				x_offset="0" y_offset="0" />		
			<!--传说魔衣 -->
			<show  eqClassName="Clothes_ChuanShuo_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonClothes17.swf" showClass="BaiLongMa_ClotheShow_17"
				x_offset="0" y_offset="0" />		
				
			<!--默认武器 -->
			<show  eqClassName="defaultWeapon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon0.swf" showClass="BaiLongMa_WeaponShow_0"
				x_offset="0" y_offset="0" />
			<!--藤杖 -->
			<show  eqClassName="Weapon_TengZhan"
				swfPath="NewGameFolder/DragonSource/DragonWeapon0.swf" showClass="BaiLongMa_WeaponShow_0"
				x_offset="0" y_offset="0" />
			<!--翡绿 -->
			<show  eqClassName="Weapon_FeiLv"
				swfPath="NewGameFolder/DragonSource/DragonWeapon1.swf" showClass="BaiLongMa_WeaponShow_1"
				x_offset="0" y_offset="0" />
			<!--蓝翎 -->
			<show  eqClassName="Weapon_LangLing"
				swfPath="NewGameFolder/DragonSource/DragonWeapon2.swf" showClass="BaiLongMa_WeaponShow_2"
				x_offset="0" y_offset="0" />
			<!--龙须-->
			<show  eqClassName="Weapon_LongXu"
				swfPath="NewGameFolder/DragonSource/DragonWeapon3.swf" showClass="BaiLongMa_WeaponShow_3"
				x_offset="0" y_offset="0" />
			<!--金芒-->
			<show  eqClassName="Weapon_JinMan"
				swfPath="NewGameFolder/DragonSource/DragonWeapon4.swf" showClass="BaiLongMa_WeaponShow_4"
				x_offset="0" y_offset="0" />
			<!--金环杖·大圣 -->
			<show  eqClassName="Weapon_JinHuangZhan_DaSheng"
				swfPath="NewGameFolder/DragonSource/DragonWeapon5.swf" showClass="BaiLongMa_WeaponShow_5"
				x_offset="0" y_offset="0" />
			<!--玄武法杖·圣灵 -->
			<show  eqClassName="Weapon_XuanWu_ShengLing"
				swfPath="NewGameFolder/DragonSource/DragonWeapon6.swf" showClass="BaiLongMa_WeaponShow_6"
				x_offset="0" y_offset="0" />
			<!--真·玄武法杖 -->
			<show  eqClassName="Weapon_XuanWu_QiangHua"
				swfPath="NewGameFolder/DragonSource/DragonWeapon6.swf" showClass="BaiLongMa_WeaponShow_6"
				x_offset="0" y_offset="0" />				
			<!--深渊魔杖 -->
			<show  eqClassName="Weapon_ShenYuan_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon7.swf" showClass="BaiLongMa_WeaponShow_7"
				x_offset="0" y_offset="0" />				
			<!--暗黑魔杖 -->
			<show  eqClassName="Weapon_AnHei_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon8.swf" showClass="BaiLongMa_WeaponShow_8"
				x_offset="0" y_offset="0" />							
			<!--圣域魔杖-->	 
			<show  eqClassName="Weapon_ShengYu_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon9.swf" showClass="BaiLongMa_WeaponShow_9"
				x_offset="0" y_offset="0" />		
				<!--神域魔杖 -->
			<show  eqClassName="Weapon_ShenYu_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon9.swf" showClass="BaiLongMa_WeaponShow_9"
				x_offset="0" y_offset="0" />	

			<!--灵印魔杖 -->
			<show  eqClassName="Weapon_LingYin_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon11.swf" showClass="BaiLongMa_WeaponShow_11"
				x_offset="0" y_offset="0" />	

			<!--神印魔杖 -->
			<show  eqClassName="Weapon_ShenYin_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon12.swf" showClass="BaiLongMa_WeaponShow_12"
				x_offset="0" y_offset="0" />	
			
						<!--晨光魔杖 -->
			<show  eqClassName="Weapon_ChenGuang_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon13.swf" showClass="BaiLongMa_WeaponShow_13"
				x_offset="0" y_offset="0" />	

			<!--神光魔杖 -->
			<show  eqClassName="Weapon_ShenGuang_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon13.swf" showClass="BaiLongMa_WeaponShow_13"
				x_offset="0" y_offset="0" />		
			<!--史诗魔杖 -->
			<show  eqClassName="Weapon_ShiShi_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon14.swf" showClass="BaiLongMa_WeaponShow_14"
				x_offset="0" y_offset="0" />	

			<!--传说魔杖 -->
			<show  eqClassName="Weapon_ChuanShuo_Dragon"
				swfPath="NewGameFolder/DragonSource/DragonWeapon14.swf" showClass="BaiLongMa_WeaponShow_14"
				x_offset="0" y_offset="0" />	
				
				
			<!-- 时装 -->
			<show  eqClassName="Fashion_FeiJi"
				swfPath="NewGameFolder/DragonSource/DragonFashion0.swf" showClass="BaiLongMa_ClotheShow_Show_0"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_AoTeMan"
				swfPath="NewGameFolder/DragonSource/DragonFashion1.swf" showClass="BaiLongMa_ClotheShow_Show_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_HuoYing"
				swfPath="NewGameFolder/DragonSource/DragonFashion2.swf" showClass="BaiLongMa_ClotheShow_Show_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_NewYearFashion"
				swfPath="NewGameFolder/DragonSource/DragonFashion3.swf" showClass="BaiLongMa_ClotheShow_Show_3"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Fashion_Milu"
				swfPath="NewGameFolder/DragonSource/DragonFashion4.swf" showClass="BaiLongMa_ClotheShow_Show_4"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Fashion_Xueren"
				swfPath="NewGameFolder/DragonSource/DragonFashion5.swf" showClass="BaiLongMa_ClotheShow_Show_5"
				x_offset="0" y_offset="0" />
			<show eqClassName="Fashion_Jiqimao"
				swfPath="NewGameFolder/DragonSource/DragonFashion6.swf" showClass="BaiLongMa_ClotheShow_Show_6"
				x_offset="0" y_offset="0" />	
				
			<show eqClassName="Fashion_Tubaobao"
				swfPath="NewGameFolder/DragonSource/DragonFashion7.swf" showClass="BaiLongMa_ClotheShow_Show_7"
				x_offset="0" y_offset="0" />		
			
			<show eqClassName="Fashion_Weilaizhanshi"
				swfPath="NewGameFolder/DragonSource/DragonFashion8.swf" showClass="BaiLongMa_ClotheShow_Show_8"
				x_offset="0" y_offset="0" />	
				
            
            <show eqClassName="Fashion_AngelDevil"
				swfPath="NewGameFolder/DragonSource/DragonFashion9.swf" showClass="BaiLongMa_ClotheShow_Show_9"
				x_offset="0" y_offset="0" />

		   <show eqClassName="Fashion_Super"
				swfPath="NewGameFolder/DragonSource/DragonFashion10.swf" showClass="BaiLongMa_ClotheShow_Show_10"
				x_offset="0" y_offset="0" />


			<!--永久时装-->
			<show eqClassName="ForeverFashion_NewYearFashion"
				swfPath="NewGameFolder/DragonSource/DragonFashion3.swf" showClass="BaiLongMa_ClotheShow_Show_3"
				x_offset="0" y_offset="0" />
	</BaiLongMa>
	<Houyi  ver="1">
			<show eqClassName="defaultClothes">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes0.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_1">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes1.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_2">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes2.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_3">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes3.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_4">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes4.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_5">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes5.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_6">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes6.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_7">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes7.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_8">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes7.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_9">

				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes8.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_10">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes9.swf" showClass="HouyiStand_tail_Clothe"
				x_offset="0" y_offset="0" i="0" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes9.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_11">

				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes10.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_12">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes11.swf" showClass="HouyiStand_tail_Clothe"
				x_offset="0" y_offset="0" i="0" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes11.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_13">

				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes12.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_14">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes13.swf" showClass="HouyiStand_tail_Clothe"
				x_offset="0" y_offset="0" i="0" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes13.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
				
			</show>
			
			<show eqClassName="Clothes_HouyiClothes_15">

				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes14.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<show eqClassName="Clothes_HouyiClothes_16">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes15.swf" showClass="HouyiStand_tail_Clothe"
				x_offset="0" y_offset="0" i="0" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes15.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
				
			</show>
			<!--史诗衣服 -->
			<show eqClassName="Clothes_HouyiClothes_17">

				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes16.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--传说衣服 -->
			<show eqClassName="Clothes_HouyiClothes_18">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes17.swf" showClass="HouyiStand_tail_Clothe"
				x_offset="0" y_offset="0" i="0" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiClothes17.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" i="2" />
				
			</show>
			
			<!--默认武器 -->
			<show eqClassName="defaultWeapon" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon0.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon0.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_1" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon0.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon0.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_2" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon1.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon1.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_3" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon2.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon2.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_4" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon3.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon3.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_5" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon4.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon4.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>		
			<show eqClassName="Weapon_HouyiWeapon_6" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon5.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon5.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_7" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon6.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon6.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>	
			<show eqClassName="Weapon_HouyiWeapon_8" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon6.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon6.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_9" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon7.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon7.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>	
			<show eqClassName="Weapon_HouyiWeapon_10" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon8.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon8.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>	
			<show eqClassName="Weapon_HouyiWeapon_11" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon9.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon9.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_12" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon9.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon9.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<show eqClassName="Weapon_HouyiWeapon_13" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon11.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon11.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			
				<show eqClassName="Weapon_ShenYin_Houyi" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon11.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon11.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			
			<show eqClassName="Weapon_HouyiWeapon_15" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon13.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon13.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			
				<show eqClassName="Weapon_ShenGuang_Houyi" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon13.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon13.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!-- 史诗武器 -->
			<show eqClassName="Weapon_HouyiWeapon_16" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon14.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon14.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!-- 传说武器 -->
			<show eqClassName="Weapon_ChuanShuo_Houyi" >
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon14.swf" showClass="HouyiShow_up_Weapon"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiWeapon14.swf" showClass="HouyiShow_down_Weapon"
				x_offset="0" y_offset="0" i="1" />
			</show>
			
			
				
			<!-- 时装 -->
			<show eqClassName="Fashion_FeiJi">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion0.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_AoTeMan">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion1.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_HuoYing">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion2.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_NewYearFashion">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion3.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Milu">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion4.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Xueren">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion5.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Jiqimao">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion6.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Tubaobao">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion7.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Weilaizhanshi">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion8.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_AngelDevil">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion9.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Super">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion10.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>


			<!--永久时装-->
			<show eqClassName="ForeverFashion_NewYearFashion">
				<subShow swfPath="NewGameFolder/HouyiSource/HouyiFashion3.swf" showClass="HouyiStand_ClotheShow"
				x_offset="0" y_offset="0" />
			</show>
	</Houyi>
	     <ErLangShen>
			<show  eqClassName="defaultClothes"
				swfPath="NewGameFolder/DogSource/DogClothes0.swf" showClass="ErLangShen_ClotheShow_0"
				x_offset="0" y_offset="0" />
			<!--蜜蜂装 -->
			<show  eqClassName="Clothes_MiFengKaiJia"
				swfPath="NewGameFolder/DogSource/DogClothes1.swf" showClass="ErLangShen_ClotheShow_1"
				x_offset="0" y_offset="0" />
			<!--花蝶装 -->
			<show  eqClassName="Clothes_HuaDieKaiJia"
				swfPath="NewGameFolder/DogSource/DogClothes2.swf" showClass="ErLangShen_ClotheShow_2"
				x_offset="0" y_offset="0" />
			<!--狮鹫装 -->
			<show  eqClassName="Clothes_ShiJiuKaiJia"
				swfPath="NewGameFolder/DogSource/DogClothes3.swf" showClass="ErLangShen_ClotheShow_3"
				x_offset="0" y_offset="0" />
			<!--龙王装 -->
			<show  eqClassName="Clothes_LongWanKaiJia"
				swfPath="NewGameFolder/DogSource/DogClothes4.swf" showClass="ErLangShen_ClotheShow_4"
				x_offset="0" y_offset="0" />	
			<!--凤武装 -->
			<show  eqClassName="Clothes_FengHuangKaiJia"
				swfPath="NewGameFolder/DogSource/DogClothes5.swf" showClass="ErLangShen_ClotheShow_5"
				x_offset="0" y_offset="0" />
			<!--大圣装 -->
			<show  eqClassName="Clothes_YanLingZhanJia_DaSheng"
				swfPath="NewGameFolder/DogSource/DogClothes6.swf" showClass="ErLangShen_ClotheShow_6"
				x_offset="0" y_offset="0" />
			<!--白虎护心甲·圣灵 -->
			<show  eqClassName="Clothes_BaiHu_ShengLing"
				swfPath="NewGameFolder/DogSource/DogClothes7.swf" showClass="ErLangShen_ClotheShow_7"
				x_offset="0" y_offset="0" />			
			<!--真·白虎护心甲 -->
			<show  eqClassName="Clothes_BaiHu_QiangHua"
				swfPath="NewGameFolder/DogSource/DogClothes7.swf" showClass="ErLangShen_ClotheShow_7"
				x_offset="0" y_offset="0" />				
			<!--深渊铁甲 -->
			<show  eqClassName="Clothes_ShenYuan_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes8.swf" showClass="ErLangShen_ClotheShow_8"
				x_offset="0" y_offset="0" />				
			<!--暗黑铁甲 -->
			<show  eqClassName="Clothes_AnHei_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes9.swf" showClass="ErLangShen_ClotheShow_9"
				x_offset="0" y_offset="0" />		
				
				<!--圣域铁甲 -->	
			<show  eqClassName="Clothes_ShengYu_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes10.swf" showClass="ErLangShen_ClotheShow_10"
				x_offset="0" y_offset="0" />		
					<!--神域铁甲 -->
			<show  eqClassName="Clothes_ShenYu_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes11.swf" showClass="ErLangShen_ClotheShow_11"
				x_offset="0" y_offset="0" />	

				<!--灵印铁甲 -->
			<show  eqClassName="Clothes_LingYin_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes12.swf" showClass="ErLangShen_ClotheShow_12"
				x_offset="0" y_offset="0" />	

				<!--神印铁甲 -->
			<show  eqClassName="Clothes_ShenYin_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes13.swf" showClass="ErLangShen_ClotheShow_13"
				x_offset="0" y_offset="0" />	
					
			<!--晨光铁甲 -->
			<show  eqClassName="Clothes_ChenGuang_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes14.swf" showClass="ErLangShen_ClotheShow_14"
				x_offset="0" y_offset="0" />	

				<!--神光铁甲 -->
			<show  eqClassName="Clothes_ShenGuang_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes15.swf" showClass="ErLangShen_ClotheShow_15"
				x_offset="0" y_offset="0" />	
			<!--史诗铁甲 -->
			<show  eqClassName="Clothes_ShiShi_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes16.swf" showClass="ErLangShen_ClotheShow_16"
				x_offset="0" y_offset="0" />	

				<!--传说铁甲 -->
			<show  eqClassName="Clothes_ChuanShuo_Dog"
				swfPath="NewGameFolder/DogSource/DogClothes17.swf" showClass="ErLangShen_ClotheShow_17"
				x_offset="0" y_offset="0" />		
				
				
			<!--默认武器 -->
			<show  eqClassName="defaultWeapon"
				swfPath="NewGameFolder/DogSource/DogWeapon0.swf" showClass="ErLangShen_WeaponShow_0"
				x_offset="0" y_offset="0" />
			<!--铁剑 -->
			<show  eqClassName="Weapon_TieJian"
				swfPath="NewGameFolder/DogSource/DogWeapon0.swf" showClass="ErLangShen_WeaponShow_0"
				x_offset="0" y_offset="0" />
			<!--羽瞳剑 -->
			<show  eqClassName="Weapon_YuTongJian"
				swfPath="NewGameFolder/DogSource/DogWeapon1.swf" showClass="ErLangShen_WeaponShow_1"
				x_offset="0" y_offset="0" />
			<!--玄冰剑 -->
			<show  eqClassName="Weapon_XuanBingJian"
				swfPath="NewGameFolder/DogSource/DogWeapon2.swf" showClass="ErLangShen_WeaponShow_2"
				x_offset="0" y_offset="0" />
			<!--鬼焰 -->
			<show  eqClassName="Weapon_GuiYan"
				swfPath="NewGameFolder/DogSource/DogWeapon3.swf" showClass="ErLangShen_WeaponShow_3"
				x_offset="0" y_offset="0" />
			<!--圣翼-->
			<show  eqClassName="Weapon_ShengYi"
				swfPath="NewGameFolder/DogSource/DogWeapon4.swf" showClass="ErLangShen_WeaponShow_4"
				x_offset="0" y_offset="0" />
			<!--赤狱•大圣-->
			<show  eqClassName="Weapon_ChiYu_DaSheng"
				swfPath="NewGameFolder/DogSource/DogWeapon5.swf" showClass="ErLangShen_WeaponShow_5"
				x_offset="0" y_offset="0" />
			<!--白虎圣剑·圣灵-->
			<show  eqClassName="Weapon_BaiHu_ShengLing"
				swfPath="NewGameFolder/DogSource/DogWeapon6.swf" showClass="ErLangShen_WeaponShow_6"
				x_offset="0" y_offset="0" />			
			<!--真·白虎圣剑-->
			<show  eqClassName="Weapon_BaiHu_QiangHua"
				swfPath="NewGameFolder/DogSource/DogWeapon6.swf" showClass="ErLangShen_WeaponShow_6"
				x_offset="0" y_offset="0" />		
			<!--深渊圣剑-->
			<show  eqClassName="Weapon_ShenYuan_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon7.swf" showClass="ErLangShen_WeaponShow_7"
				x_offset="0" y_offset="0" />		
			<!--暗黑圣剑-->
			<show  eqClassName="Weapon_AnHei_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon8.swf" showClass="ErLangShen_WeaponShow_8"
				x_offset="0" y_offset="0" />		
								
				<!--圣域圣剑-->	
			<show  eqClassName="Weapon_ShengYu_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon9.swf" showClass="ErLangShen_WeaponShow_9"
				x_offset="0" y_offset="0" />		
				
				<!--神域圣剑-->
			<show  eqClassName="Weapon_ShenYu_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon9.swf" showClass="ErLangShen_WeaponShow_9"
				x_offset="0" y_offset="0" />	

			<!--灵印圣剑-->
			<show  eqClassName="Weapon_LingYin_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon11.swf" showClass="ErLangShen_WeaponShow_11"
				x_offset="0" y_offset="0" />			

			<!--神印圣剑-->
			<show  eqClassName="Weapon_ShenYin_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon12.swf" showClass="ErLangShen_WeaponShow_12"
				x_offset="0" y_offset="0" />			
					
			<!--晨光圣剑-->
			<show  eqClassName="Weapon_ChenGuang_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon13.swf" showClass="ErLangShen_WeaponShow_13"
				x_offset="0" y_offset="0" />			

			<!--神光圣剑-->
			<show  eqClassName="Weapon_ShenGuang_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon13.swf" showClass="ErLangShen_WeaponShow_13"
				x_offset="0" y_offset="0" />			
			<!--史诗圣剑-->
			<show  eqClassName="Weapon_ShiShi_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon14.swf" showClass="ErLangShen_WeaponShow_14"
				x_offset="0" y_offset="0" />			

			<!--传说圣剑-->
			<show  eqClassName="Weapon_ChuanShuo_Dog"
				swfPath="NewGameFolder/DogSource/DogWeapon14.swf" showClass="ErLangShen_WeaponShow_14"
				x_offset="0" y_offset="0" />						
			<!-- 时装 -->
			<show  eqClassName="Fashion_FeiJi"
				swfPath="NewGameFolder/DogSource/DogFashion0.swf" showClass="ErLangShen_ClotheShow_Show_0"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_AoTeMan"
				swfPath="NewGameFolder/DogSource/DogFashion1.swf" showClass="ErLangShen_ClotheShow_Show_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_HuoYing"
				swfPath="NewGameFolder/DogSource/DogFashion2.swf" showClass="ErLangShen_ClotheShow_Show_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_NewYearFashion"
				swfPath="NewGameFolder/DogSource/DogFashion3.swf" showClass="ErLangShen_ClotheShow_Show_3"
				x_offset="0" y_offset="0" />
			<show eqClassName="Fashion_Milu"
				swfPath="NewGameFolder/DogSource/DogFashion4.swf" showClass="ErLangShen_ClotheShow_Show_4"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Fashion_Xueren"
				swfPath="NewGameFolder/DogSource/DogFashion5.swf" showClass="ErLangShen_ClotheShow_Show_5"
				x_offset="0" y_offset="0" />
			<show eqClassName="Fashion_Jiqimao"
				swfPath="NewGameFolder/DogSource/DogFashion6.swf" showClass="ErLangShen_ClotheShow_Show_6"
				x_offset="0" y_offset="0" />		
				
				
			<show eqClassName="Fashion_Tubaobao"
				swfPath="NewGameFolder/DogSource/DogFashion7.swf" showClass="ErLangShen_ClotheShow_Show_7"
				x_offset="0" y_offset="0" />	
				
			<show eqClassName="Fashion_Weilaizhanshi"
				swfPath="NewGameFolder/DogSource/DogFashion8.swf" showClass="ErLangShen_ClotheShow_Show_8"
				x_offset="0" y_offset="0" />		

			
			<show eqClassName="Fashion_AngelDevil"
				swfPath="NewGameFolder/DogSource/DogFashion9.swf" showClass="ErLangShen_ClotheShow_Show_9"
				x_offset="0" y_offset="0" />	

			<show eqClassName="Fashion_Super"
				swfPath="NewGameFolder/DogSource/DogFashion10.swf" showClass="ErLangShen_ClotheShow_Show_10"
				x_offset="0" y_offset="0" />	
			
			<!--永久时装-->
			<show eqClassName="ForeverFashion_NewYearFashion"
				swfPath="NewGameFolder/DogSource/DogFashion3.swf" showClass="ErLangShen_ClotheShow_Show_3"
				x_offset="0" y_offset="0" />
				
				
			
				
				
				
				
	</ErLangShen>
	     <ChangE>
			<show  eqClassName="defaultClothes"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes0.swf" showClass="ChangEMei_ClotheShow_0"
				x_offset="0" y_offset="0" />
			<!--牛仔装 -->
			<show  eqClassName="Clothes_NiuZaiKaiJia"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes1.swf" showClass="ChangEMei_ClotheShow_1"
				x_offset="0" y_offset="0" />
			<!--警察装 -->
			<show  eqClassName="Clothes_JingChaKaiJia"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes2.swf" showClass="ChangEMei_ClotheShow_2"
				x_offset="0" y_offset="0" />
			<!--士兵装 -->
			<show  eqClassName="Clothes_ShiBinKaiJia"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes3.swf" showClass="ChangEMei_ClotheShow_3"
				x_offset="0" y_offset="0" />
			<!--特战装 -->
			<show  eqClassName="Clothes_TeZhanKaiJia"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes4.swf" showClass="ChangEMei_ClotheShow_4"
				x_offset="0" y_offset="0" />	
			<!--机械装 -->
			<show  eqClassName="Clothes_JiXieKaiJia"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes5.swf" showClass="ChangEMei_ClotheShow_5"
				x_offset="0" y_offset="0" />
			<!--大圣装 -->
			<show  eqClassName="Clothes_FengChiYuJia_DaSheng"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes6.swf" showClass="ChangEMei_ClotheShow_6"
				x_offset="0" y_offset="0" />
			<!--朱雀五彩衣·圣灵-->
			<show  eqClassName="Clothes_ZhuQue_ShengLing"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes7.swf" showClass="ChangEMei_ClotheShow_7"
				x_offset="0" y_offset="0" />					
			<!--真·朱雀五彩衣-->
			<show  eqClassName="Clothes_ZhuQue_QiangHua"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes7.swf" showClass="ChangEMei_ClotheShow_7"
				x_offset="0" y_offset="0" />						
			<!--深渊彩衣-->
			<show  eqClassName="Clothes_ShenYuan_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes8.swf" showClass="ChangEMei_ClotheShow_8"
				x_offset="0" y_offset="0" />						
			<!--暗黑彩衣-->
			<show  eqClassName="Clothes_AnHei_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes9.swf" showClass="ChangEMei_ClotheShow_9"
				x_offset="0" y_offset="0" />						
				
				<!--圣域彩衣-->
			<show  eqClassName="Clothes_ShengYu_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes10.swf" showClass="ChangEMei_ClotheShow_10"
				x_offset="0" y_offset="0" />		
					<!--神域彩衣-->
			<show  eqClassName="Clothes_ShenYu_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes11.swf" showClass="ChangEMei_ClotheShow_11"
				x_offset="0" y_offset="0" />	
				
			<!--灵印彩衣-->
			<show  eqClassName="Clothes_LingYin_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes12.swf" showClass="ChangEMei_ClotheShow_12"
				x_offset="0" y_offset="0" />	

			<!--神印彩衣-->  
			<show  eqClassName="Clothes_ShenYin_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes13.swf" showClass="ChangEMei_ClotheShow_13"
				x_offset="0" y_offset="0" />	
			<show  eqClassName="Clothes_ShenYin_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes13.swf" showClass="ChangEMei_ClotheShow_fz_13"
				x_offset="0" y_offset="0" />	
				
			<!--晨光彩衣-->
			<show  eqClassName="Clothes_ChenGuang_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes14.swf" showClass="ChangEMei_ClotheShow_14"
				x_offset="0" y_offset="0" />	
			<show  eqClassName="Clothes_ChenGuang_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes14.swf" showClass="ChangEMei_ClotheShow_head_14"
				x_offset="0" y_offset="0" />	
	

			<!--神光彩衣-->
			<show  eqClassName="Clothes_ShenGuang_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes15.swf" showClass="ChangEMei_ClotheShow_15"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Clothes_ShenGuang_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes15.swf" showClass="ChangEMei_ClotheShow_fz_15"
				x_offset="0" y_offset="0" />	

			<!--史诗彩衣-->
			<show  eqClassName="Clothes_ShiShi_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes16.swf" showClass="ChangEMei_ClotheShow_16"
				x_offset="0" y_offset="0" />	
			<show  eqClassName="Clothes_ShiShi_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes16.swf" showClass="ChangEMei_ClotheShow_head_16"
				x_offset="0" y_offset="0" />	
	

			<!--传说彩衣-->
			<show  eqClassName="Clothes_ChuanShuo_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes17.swf" showClass="ChangEMei_ClotheShow_17"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Clothes_ChuanShuo_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitClothes17.swf" showClass="ChangEMei_ClotheShow_fz_17"
				x_offset="0" y_offset="0" />		

				
				
			<!--默认武器 -->
			<show  eqClassName="defaultWeapon"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon0.swf" showClass="ChangEMei_WeaponShow_0"
				x_offset="0" y_offset="0" />
			<!--左轮手枪 -->
			<show  eqClassName="Weapon_ZuoLunShouQiang"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon0.swf" showClass="ChangEMei_WeaponShow_0"
				x_offset="0" y_offset="0" />
			<!--冲锋手枪 -->
			<show  eqClassName="Weapon_ChongFengShouQiang"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon1.swf" showClass="ChangEMei_WeaponShow_1"
				x_offset="0" y_offset="0" />
			<!--狙击手枪 -->
			<show  eqClassName="Weapon_JuJiShouQiang"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon2.swf" showClass="ChangEMei_WeaponShow_2"
				x_offset="0" y_offset="0" />
			<!--特战手枪 -->
			<show  eqClassName="Weapon_TeZhanShouQiang"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon3.swf" showClass="ChangEMei_WeaponShow_3"
				x_offset="0" y_offset="0" />
			<!--激光手枪-->
			<show  eqClassName="Weapon_JiGuangShouQiang"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon4.swf" showClass="ChangEMei_WeaponShow_4"
				x_offset="0" y_offset="0" />
			<!--降龙·大圣-->
			<show  eqClassName="Weapon_XiangLong_DaSheng"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon5.swf" showClass="ChangEMei_WeaponShow_5"
				x_offset="0" y_offset="0" />
			<!--朱雀圣枪·圣灵-->
			<show  eqClassName="Weapon_ZhuQue_ShengLing"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon6.swf" showClass="ChangEMei_WeaponShow_6"
				x_offset="0" y_offset="0" />		
			<!--真·朱雀圣枪-->
			<show  eqClassName="Weapon_ZhuQue_QiangHua"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon6.swf" showClass="ChangEMei_WeaponShow_6"
				x_offset="0" y_offset="0" />					
			<!--深渊圣枪-->
			<show  eqClassName="Weapon_ShenYuan_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon7.swf" showClass="ChangEMei_WeaponShow_7"
				x_offset="0" y_offset="0" />					
			<!--暗黑圣枪-->
			<show  eqClassName="Weapon_AnHei_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon8.swf" showClass="ChangEMei_WeaponShow_8"
				x_offset="0" y_offset="0" />				
				<!--圣域圣枪-->
			<show  eqClassName="Weapon_ShengYu_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon9.swf" showClass="ChangEMei_WeaponShow_9"
				x_offset="0" y_offset="0" />		
				
					<!--神域圣枪-->
			<show  eqClassName="Weapon_ShenYu_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon9.swf" showClass="ChangEMei_WeaponShow_9"
				x_offset="0" y_offset="0" />	

			<!--灵印圣枪-->
			<show  eqClassName="Weapon_LingYin_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon11.swf" showClass="ChangEMei_WeaponShow_11"
				x_offset="0" y_offset="0" />	

			<!--神印圣枪-->
			<show  eqClassName="Weapon_ShenYin_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon12.swf" showClass="ChangEMei_WeaponShow_12"
				x_offset="0" y_offset="0" />	
					
			<!--晨光圣枪-->
			<show  eqClassName="Weapon_ChenGuang_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon13.swf" showClass="ChangEMei_WeaponShow_13"
				x_offset="0" y_offset="0" />	

			<!--神光圣枪-->
			<show  eqClassName="Weapon_ShenGuang_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon13.swf" showClass="ChangEMei_WeaponShow_13"
				x_offset="0" y_offset="0" />		
				
			<!--史诗圣枪-->
			<show  eqClassName="Weapon_ShiShi_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon14.swf" showClass="ChangEMei_WeaponShow_14"
				x_offset="0" y_offset="0" />	

			<!--传说圣枪-->
			<show  eqClassName="Weapon_ChuanShuo_Rabbit"
				swfPath="NewGameFolder/RabbitSource/RabbitWeapon14.swf" showClass="ChangEMei_WeaponShow_14"
				x_offset="0" y_offset="0" />		
				
				
			<!-- 时装 -->
			<show  eqClassName="Fashion_FeiJi"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion0.swf" showClass="ChangEMei_ClotheShow_Show_0"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_AoTeMan"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion1.swf" showClass="ChangEMei_ClotheShow_Show_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_HuoYing"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion2.swf" showClass="ChangEMei_ClotheShow_Show_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Fashion_NewYearFashion"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion3.swf" showClass="ChangEMei_ClotheShow_Show_3"
				x_offset="0" y_offset="0" />
			
			<show eqClassName="Fashion_Milu"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion4.swf" showClass="ChangEMei_ClotheShow_Show_4"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Fashion_Xueren"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion5.swf" showClass="ChangEMei_ClotheShow_Show_5"
				x_offset="0" y_offset="0" />
						
			<show eqClassName="Fashion_Jiqimao"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion6.swf" showClass="ChangEMei_ClotheShow_Show_6"
				x_offset="0" y_offset="0" />	
				
			<show eqClassName="Fashion_Tubaobao"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion7.swf" showClass="ChangEMei_ClotheShow_Show_7"
				x_offset="0" y_offset="0" />		
				
			<show eqClassName="Fashion_Weilaizhanshi"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion8.swf" showClass="ChangEMei_ClotheShow_Show_8"
				x_offset="0" y_offset="0" />		
				
			<show eqClassName="Fashion_AngelDevil"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion9.swf" showClass="ChangEMei_ClotheShow_Show_9"
				x_offset="0" y_offset="0" />

			<show eqClassName="Fashion_Super"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion10.swf" showClass="ChangEMei_ClotheShow_Show_10"
				x_offset="0" y_offset="0" />
			<!--永久时装-->
			<show eqClassName="ForeverFashion_NewYearFashion"
				swfPath="NewGameFolder/RabbitSource/RabbitFashion3.swf" showClass="ChangEMei_ClotheShow_Show_3"
				x_offset="0" y_offset="0" />
															
	</ChangE>	
	     <Fox ver="1">
			<show  eqClassName="defaultClothes">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes0.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes0.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--1-->	
			<show  eqClassName="Clothes_FoxClothes1">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes1.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes1.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--2-->	
			<show  eqClassName="Clothes_FoxClothes2">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes2.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes2.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--3-->	
			<show  eqClassName="Clothes_FoxClothes3">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes3.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes3.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--4-->	
			<show  eqClassName="Clothes_FoxClothes4">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes4.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes4.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
		    <!--5-->	
			<show  eqClassName="Clothes_FoxClothes5">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes5.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes5.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--6-->	
			<show  eqClassName="Clothes_FoxClothes6">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes6.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes6.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--7-->	
			<show  eqClassName="Clothes_FoxClothes7">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes7.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes7.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--8-->	
			<show  eqClassName="Clothes_FoxClothes8">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes7.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes7.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--9-->	
			<show  eqClassName="Clothes_FoxClothes9">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes8.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes8.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--10-->	
			<show  eqClassName="Clothes_FoxClothes10">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes9.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes9.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
		   
				<!--11    -->	
			<show  eqClassName="Clothes_FoxClothes11">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes10.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes10.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>              
		   
				<!--12-->	
			<show  eqClassName="Clothes_FoxClothes12">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes11.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes11.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>	
			
             <!--灵印-->	
			<show  eqClassName="Clothes_FoxClothes13">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes12.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes12.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>

			<!--神印-->	
			<show  eqClassName="Clothes_FoxClothes14">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes13.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes13.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>

	             <!--晨光-->	
			<show  eqClassName="Clothes_FoxClothes15">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes14.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes14.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>

			<!--神光-->	
			<show  eqClassName="Clothes_FoxClothes16">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes15.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes15.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			 <!--史诗-->	
			<show  eqClassName="Clothes_FoxClothes17">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes16.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes16.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>

			<!--传说-->	
			<show  eqClassName="Clothes_FoxClothes18">
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes17.swf" showClass="FoxShow_Body"
				x_offset="0" y_offset="0" i="2" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxClothes17.swf" showClass="FoxShow_Tail"
				x_offset="0" y_offset="0" i="0" />
			</show>
			
			
			
			<!--默认武器 -->
			<show  eqClassName="defaultWeapon" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon0.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon0.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--1 -->
			<show  eqClassName="Weapon_FoxWeapon1" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon0.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon0.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--2 -->
			<show  eqClassName="Weapon_FoxWeapon2" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon1.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon1.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--3-->
			<show  eqClassName="Weapon_FoxWeapon3" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon2.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon2.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--4-->
			<show  eqClassName="Weapon_FoxWeapon4" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon3.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon3.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--5-->
			<show  eqClassName="Weapon_FoxWeapon5" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon4.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon4.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--6-->
			<show  eqClassName="Weapon_FoxWeapon6" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon5.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon5.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--7-->
			<show  eqClassName="Weapon_FoxWeapon7" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon6.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon6.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--8-->
			<show  eqClassName="Weapon_FoxWeapon8" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon6.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon6.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--9-->
			<show  eqClassName="Weapon_FoxWeapon9" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon7.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon7.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--10-->
			<show  eqClassName="Weapon_FoxWeapon10" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon8.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon8.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			
			
				<!--11 -->
			<show  eqClassName="Weapon_FoxWeapon11" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon9.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon9.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>                  
			
				<!--12-->
			<show  eqClassName="Weapon_FoxWeapon12" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon9.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon9.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>

			<!--灵印11-->
			<show  eqClassName="Weapon_FoxWeapon13" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon11.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon11.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>

			<!--神印11-->
			<show  eqClassName="Weapon_FoxWeapon14" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon12.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon12.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--晨光11-->
			<show  eqClassName="Weapon_FoxWeapon15" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon13.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon13.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>

			<!--神光11-->
			<show  eqClassName="Weapon_FoxWeapon16" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon13.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon13.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>			
			<!--史诗-->
			<show  eqClassName="Weapon_FoxWeapon17" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon14.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon14.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>

			<!--传说-->
			<show  eqClassName="Weapon_FoxWeapon18" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon14.swf" showClass="FoxShow_Weapon_Up"
				x_offset="0" y_offset="0" i="3" />
				<subShow swfPath="NewGameFolder/FoxSource/FoxWeapon14.swf" showClass="FoxShow_Weapon_Down"
				x_offset="0" y_offset="0" i="1" />
			</show>		
			
			<!--时装-->
			<show  eqClassName="Fashion_FeiJi" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion0.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>
			<show  eqClassName="Fashion_AoTeMan" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion1.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>
			<show  eqClassName="Fashion_HuoYing" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion2.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>
			<show  eqClassName="Fashion_NewYearFashion" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion3.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>
			
			<show eqClassName="Fashion_Milu">
			    <subShow swfPath="NewGameFolder/FoxSource/FoxFashion4.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Xueren">
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion5.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>		
			<show eqClassName="Fashion_Jiqimao">
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion6.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>			
			
				<show eqClassName="Fashion_Tubaobao">
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion7.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>			
			 
			<show eqClassName="Fashion_Weilaizhanshi">
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion8.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>		
			
			<show eqClassName="Fashion_AngelDevil">
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion9.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>	

			<show eqClassName="Fashion_Super">
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion10.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>	
			
			<!--永久新年时装-->
			<show  eqClassName="ForeverFashion_NewYearFashion" >
				<subShow swfPath="NewGameFolder/FoxSource/FoxFashion3.swf" showClass="FoxShow"
				x_offset="0" y_offset="0" />
			</show>
															
	</Fox>	
	<TieShan>
		<show  eqClassName="defaultClothes"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes0.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
				
			<!--1-->	
			<show  eqClassName="Clothes_TieShanClothes1"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes1.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />	

			<!--2-->	
			<show  eqClassName="Clothes_TieShanClothes2"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes2.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--3-->	
			<show  eqClassName="Clothes_TieShanClothes3"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes3.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--4-->	
			<show  eqClassName="Clothes_TieShanClothes4"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes4.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--5-->	
			<show  eqClassName="Clothes_TieShanClothes5"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes5.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--6-->	
			<show  eqClassName="Clothes_TieShanClothes6"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes6.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--7-->	
			<show  eqClassName="Clothes_TieShanClothes7"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes7.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--8-->	
			<show  eqClassName="Clothes_TieShanClothes8"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes7.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--9-->	
			<show  eqClassName="Clothes_TieShanClothes9"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes8.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--10-->	
			<show  eqClassName="Clothes_TieShanClothes10"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes9.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--11	 -->
			<show  eqClassName="Clothes_TieShanClothes11"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes10.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />        

			<!--12-->	
			<show  eqClassName="Clothes_TieShanClothes12"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes11.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
			
			<!--灵印13-->	
			<show  eqClassName="Clothes_TieShanClothes13"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes12.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--神印14-->	
			<show  eqClassName="Clothes_TieShanClothes14"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes13.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
		   <!--晨光15-->	
			<show  eqClassName="Clothes_TieShanClothes15"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes14.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--神光16-->	
			<show  eqClassName="Clothes_TieShanClothes16"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes15.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
		
			 <!--史诗-->	
			<show  eqClassName="Clothes_TieShanClothes17"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes16.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<!--传说-->	
			<show  eqClassName="Clothes_TieShanClothes18"
				swfPath="NewGameFolder/TieShanSource/TieShanClothes17.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
				
			
			<!--默认武器 -->
			<show  eqClassName="defaultWeapon"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon0.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />
					
			<!--1 -->
			<show  eqClassName="Weapon_TieShanWeapon1"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon0.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--2 -->
			<show  eqClassName="Weapon_TieShanWeapon2"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon1.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />
		
			<!--3 -->
			<show  eqClassName="Weapon_TieShanWeapon3"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon2.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--4 -->
			<show  eqClassName="Weapon_TieShanWeapon4"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon3.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

				<!--5 -->
			<show  eqClassName="Weapon_TieShanWeapon5"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon4.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--6 -->
			<show  eqClassName="Weapon_TieShanWeapon6"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon5.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--7 -->
			<show  eqClassName="Weapon_TieShanWeapon7"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon6.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--8 -->
			<show  eqClassName="Weapon_TieShanWeapon8"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon6.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--9 -->
			<show  eqClassName="Weapon_TieShanWeapon9"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon7.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--10 -->
			<show  eqClassName="Weapon_TieShanWeapon10"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon8.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--11  -->
			<show  eqClassName="Weapon_TieShanWeapon11"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon9.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />   

			<!--12 -->
			<show  eqClassName="Weapon_TieShanWeapon12"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon9.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--灵印11 -->
			<show  eqClassName="Weapon_TieShanWeapon13"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon11.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--神印11 -->
			<show  eqClassName="Weapon_TieShanWeapon14"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon12.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />
			
			<!--晨光11 -->
			<show  eqClassName="Weapon_TieShanWeapon15"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon13.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--神光11 -->
			<show  eqClassName="Weapon_TieShanWeapon16"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon13.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />
			<!--史诗 -->
			<show  eqClassName="Weapon_TieShanWeapon17"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon14.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />

			<!--传说 -->
			<show  eqClassName="Weapon_TieShanWeapon18"
					swfPath="NewGameFolder/TieShanSource/TieShanWeapon14.swf" showClass="TieShanShow_Weapon_Feng"
					x_offset="0" y_offset="0" />		
			<!--时装-->
			<show  eqClassName="Fashion_FeiJi"
				swfPath="NewGameFolder/TieShanSource/TieShanFashion0.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
				
				
			<show  eqClassName="Fashion_AoTeMan" 
				swfPath="NewGameFolder/TieShanSource/TieShanFashion1.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
			
			<show  eqClassName="Fashion_HuoYing" 
				swfPath="NewGameFolder/TieShanSource/TieShanFashion2.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
			
			<show  eqClassName="Fashion_NewYearFashion" 
				swfPath="NewGameFolder/TieShanSource/TieShanFashion3.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
			
			
			<show eqClassName="Fashion_Milu"
			    swfPath="NewGameFolder/TieShanSource/TieShanFashion4.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
			
			<show eqClassName="Fashion_Xueren"
				swfPath="NewGameFolder/TieShanSource/TieShanFashion5.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Fashion_Jiqimao"
				swfPath="NewGameFolder/TieShanSource/TieShanFashion6.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
					
			
				<show eqClassName="Fashion_Tubaobao"
				swfPath="NewGameFolder/TieShanSource/TieShanFashion7.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
				
			
			<show eqClassName="Fashion_Weilaizhanshi"
				 swfPath="NewGameFolder/TieShanSource/TieShanFashion8.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
			
			
			<show eqClassName="Fashion_AngelDevil"
				 swfPath="NewGameFolder/TieShanSource/TieShanFashion9.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />

			<show eqClassName="Fashion_Super"
				 swfPath="NewGameFolder/TieShanSource/TieShanFashion10.swf" showClass="TieShanShow_Body"
				x_offset="0" y_offset="0" />
		
															
	</TieShan>

	<ZiXia ver="1"> <!--i数字越大越上层 -->
			<show eqClassName="defaultClothes">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes0.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--蜜蜂装 -->
			<show eqClassName="Clothes_ZixiaiClothes_1">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes1.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--花蝶装 -->
			<show eqClassName="Clothes_ZixiaiClothes_2">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes2.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--狮鹫装 -->
			<show eqClassName="Clothes_ZixiaiClothes_3">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes3.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--龙王装 -->
			<show eqClassName="Clothes_ZixiaiClothes_4">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes4.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--凤武装 -->
			<show eqClassName="Clothes_ZixiaiClothes_5">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes5.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--大圣装 -->
			<show eqClassName="Clothes_ZixiaiClothes_6">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes6.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--白虎装 -->
			<show eqClassName="Clothes_ZixiaiClothes_8">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes7.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--真白虎装 -->
			<show eqClassName="Clothes_ZixiaiClothes_8">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes7.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--深渊套装 -->
			<show eqClassName="Clothes_ZixiaiClothes_9">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes8.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--暗黑套装 -->
			<show eqClassName="Clothes_ZixiaiClothes_10">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes9.swf" showClass="ZiXiaStand_tail_Clothe"
				x_offset="0" y_offset="0" i="0" />
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes9.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--圣域套装--> 
			<show eqClassName="Clothes_ZixiaiClothes_11">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes10.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--神域套装-->
			<show eqClassName="Clothes_ZixiaiClothes_12">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes11.swf" showClass="ZiXiaStand_tail_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes11.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="1" />
			</show>
			<!--灵印套装-->
			<show eqClassName="Clothes_ZixiaiClothes_13">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes12.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--神印套装-->
			<show eqClassName="Clothes_ZixiaiClothes_14">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes13.swf" showClass="ZiXiaStand_tail_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes13.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="1" />
				
			</show>
			<!--晨光套装-->
			<show eqClassName="Clothes_ZixiaiClothes_15">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes14.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--神光套装-->
			<show eqClassName="Clothes_ZixiaiClothes_16">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes15.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
				
			</show>
			
			<!--史诗套装-->
			<show eqClassName="Clothes_ZixiaiClothes_17">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes16.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
			</show>
			<!--传说套装-->
			<show eqClassName="Clothes_ZixiaiClothes_18">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaClothes17.swf" showClass="ZiXiaStand_ClotheShow"
				x_offset="0" y_offset="0" i="0" />
				
			</show>

			<!--默认武器 -->
			<show eqClassName="defaultWeapon" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon0.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--铁剑 -->
			<show eqClassName="Weapon_ZixiaiWeapon_1" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon0.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--羽瞳剑 -->
			<show eqClassName="Weapon_ZixiaiWeapon_2" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon1.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--玄冰剑 -->
			<show eqClassName="Weapon_ZixiaiWeapon_3" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon2.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--鬼焰 -->	
			<show eqClassName="Weapon_ZixiaiWeapon_4" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon3.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--圣翼-->		
			<show eqClassName="Weapon_ZixiaiWeapon_5" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon4.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>	
			<!--赤狱•大圣-->		
			<show eqClassName="Weapon_ZixiaiWeapon_6" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon5.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--白虎圣剑-->
			<show eqClassName="Weapon_ZixiaiWeapon_8" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon6.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>	
			<!--真白虎圣剑-->
			<show eqClassName="Weapon_ZixiaiWeapon_8" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon6.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--深渊圣剑-->
			<show eqClassName="Weapon_ZixiaiWeapon_9" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon7.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>	
			<!--暗黑圣剑-->
			<show eqClassName="Weapon_ZixiaiWeapon_10" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon8.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>	
			<!--圣域圣剑-->	
			<show eqClassName="Weapon_ZixiaiWeapon_11" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon9.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--神域圣剑-->	
			<show eqClassName="Weapon_ZixiaiWeapon_12" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon10.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--灵印圣剑-->	
			<show eqClassName="Weapon_ZixiaiWeapon_13" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon11.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--神印圣剑-->		
			<show eqClassName="Weapon_ZixiaiWeapon_14" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon12.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--晨光圣剑-->	
			<show eqClassName="Weapon_ZixiaiWeapon_15" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon13.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--神光圣剑-->	
			<show eqClassName="Weapon_ZixiaiWeapon_16" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon13.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--史诗圣剑-->	
			<show eqClassName="Weapon_ZixiaiWeapon_17" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon14.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			<!--传说圣剑-->	
			<show eqClassName="Weapon_ZixiaiWeapon_18" >
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaWeapon14.swf" showClass="ZiXiaStand_WeaponShow"
				x_offset="0" y_offset="0" i="2" />
			</show>
			
			
				
			<!-- 时装 -->
			<show eqClassName="Fashion_FeiJi">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion0.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_AoTeMan">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion1.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_HuoYing">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion2.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_NewYearFashion">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion3.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Milu">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion4.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Xueren">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion5.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Jiqimao">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion6.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Tubaobao">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion7.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Weilaizhanshi">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion8.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_AngelDevil">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion9.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
			<show eqClassName="Fashion_Super">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion10.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>


			<!--永久时装-->
			<show eqClassName="ForeverFashion_NewYearFashion">
				<subShow swfPath="NewGameFolder/ZiXiaSource/ZiXiaFashion3.swf" showClass="ZiXiaStand_ClotheShow_Show"
				x_offset="0" y_offset="0" />
			</show>
	</ZiXia>
	     <Pet>

			<show eqClassName="Pet_Tyrannosaurs_1"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetStandShow0_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_Tyrannosaurs_2"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetStandShow0_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_Tyrannosaurs_3"
				swfPath="NewGameFolder/PetSource/Pet0All.swf" showClass="PetStandShow0_3"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Pet_XiaoPiKaQiu_1"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetStandShow1_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_XiaoPiKaQiu_2"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetStandShow1_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_XiaoPiKaQiu_3"
				swfPath="NewGameFolder/PetSource/Pet1All.swf" showClass="PetStandShow1_3"
				x_offset="0" y_offset="0" />	
			
			<show eqClassName="Pet_ShiTouRen_1"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetStandShow2_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_ShiTouRen_2"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetStandShow2_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_ShiTouRen_3"
				swfPath="NewGameFolder/PetSource/Pet2All.swf" showClass="PetStandShow2_3"
				x_offset="0" y_offset="0" />
					
			<show eqClassName="Pet_HuoRen_1"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetStandShow3_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_HuoRen_2"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetStandShow3_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_HuoRen_3"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetStandShow3_3"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Pet_JiQiRen_1"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetStandShow4_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_JiQiRen_2"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetStandShow4_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_JiQiRen_3"
				swfPath="NewGameFolder/PetSource/Pet4All.swf" showClass="PetStandShow4_3"
				x_offset="0" y_offset="0" />
			
			<show eqClassName="Pet_BingLong_1"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetStandShow5_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_BingLong_2"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetStandShow5_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_BingLong_3"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetStandShow5_3"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Pet_LiHe_1"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetStandShow6_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_LiHe_2"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetStandShow6_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_LiHe_3"
				swfPath="NewGameFolder/PetSource/Pet6All.swf" showClass="PetStandShow6_3"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Pet_Jian_1"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetStandShow7_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_Jian_2"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetStandShow7_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_Jian_3"
				swfPath="NewGameFolder/PetSource/Pet7All.swf" showClass="PetStandShow7_3"
				x_offset="0" y_offset="0" />
			
			<show eqClassName="Pet_RenXingBingLong_4"
				swfPath="NewGameFolder/PetSource/Pet8All.swf" showClass="PetStandShow8_4"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Pet_JuYanZhanJiang_4"
				swfPath="NewGameFolder/PetSource/Pet9All.swf" showClass="PetStandShow9_4"
				x_offset="0" y_offset="0" />
			
			<show eqClassName="Pet_HanDiJuLong_4"
				swfPath="NewGameFolder/PetSource/Pet10All.swf" showClass="PetStandShow10_4"
				x_offset="0" y_offset="0" />

			<show eqClassName="Pet_Jian_4"
				swfPath="NewGameFolder/PetSource/Pet11All.swf" showClass="PetStandShow11_4"
				x_offset="0" y_offset="0" />
			
			<show eqClassName="Pet_Lu_1"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetStandShow12_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_Lu_2"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetStandShow12_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_Lu_3"
				swfPath="NewGameFolder/PetSource/Pet12All.swf" showClass="PetStandShow12_3"
				x_offset="0" y_offset="0" />
			
			<show eqClassName="Pet_TianShiLu_4"
				swfPath="NewGameFolder/PetSource/Pet13All.swf" showClass="PetStandShow13_4"
				x_offset="0" y_offset="0" />
			
			<show eqClassName="Pet_EMoLu_4"
				swfPath="NewGameFolder/PetSource/Pet14All.swf" showClass="PetStandShow14_4"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Pet_LiHe_4"
				swfPath="NewGameFolder/PetSource/Pet15All.swf" showClass="PetStandShow15_4"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Pet_FengHuang_1"
				swfPath="NewGameFolder/PetSource/Pet16All.swf" showClass="PetStandShow_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_FengHuang_2"
				swfPath="NewGameFolder/PetSource/Pet16All.swf" showClass="PetStandShow_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_FengHuang_3"
				swfPath="NewGameFolder/PetSource/Pet16All.swf" showClass="PetStandShow_3"
				x_offset="0" y_offset="0" />
				
			<show eqClassName="Pet_FengHuang_4"
				swfPath="NewGameFolder/PetSource/Pet17All.swf" showClass="PetStandShow"
				x_offset="0" y_offset="0" />

			<show eqClassName="Pet_ChongMing_1"
				swfPath="NewGameFolder/PetSource/Pet18All.swf" showClass="PetStandShow_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_ChongMing_2"
				swfPath="NewGameFolder/PetSource/Pet18All.swf" showClass="PetStandShow_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_ChongMing_3"
				swfPath="NewGameFolder/PetSource/Pet18All.swf" showClass="PetStandShow_3"
				x_offset="0" y_offset="0" />
			<show eqClassName="Pet_ChongMing_4"
				swfPath="NewGameFolder/PetSource/Pet19All.swf" showClass="PetStandShow"
				x_offset="0" y_offset="0" />
			<show eqClassName="Pet_MoLong_1"
				swfPath="NewGameFolder/PetSource/Pet20All.swf" showClass="PetStandShow_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_MoLong_2"
				swfPath="NewGameFolder/PetSource/Pet20All.swf" showClass="PetStandShow_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_MoLong_3"
				swfPath="NewGameFolder/PetSource/Pet20All.swf" showClass="PetStandShow_3"
				x_offset="0" y_offset="0" />
			<show eqClassName="Pet_MoLong_4"
				swfPath="NewGameFolder/PetSource/Pet21All.swf" showClass="PetStandShow"
				x_offset="0" y_offset="0" />

			<show eqClassName="Pet_ZhuLong_1"
				swfPath="NewGameFolder/PetSource/Pet22All.swf" showClass="PetStandShow_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_ZhuLong_2"
				swfPath="NewGameFolder/PetSource/Pet22All.swf" showClass="PetStandShow_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_ZhuLong_3"
				swfPath="NewGameFolder/PetSource/Pet22All.swf" showClass="PetStandShow_3"
				x_offset="0" y_offset="0" />
			<show eqClassName="Pet_ZhuLong_4"
				swfPath="NewGameFolder/PetSource/Pet23All.swf" showClass="PetStandShow"
				x_offset="0" y_offset="0" />

			<show eqClassName="Pet_QiongQi_1"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetStandShow_1"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_QiongQi_2"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetStandShow_2"
				x_offset="0" y_offset="0" />
			<show  eqClassName="Pet_QiongQi_3"
				swfPath="NewGameFolder/PetSource/Pet24All.swf" showClass="PetStandShow_3"
				x_offset="0" y_offset="0" />
			<show eqClassName="Pet_QiongQi_4"
				swfPath="NewGameFolder/PetSource/Pet25All.swf" showClass="PetStandShow"
				x_offset="0" y_offset="0" />
	</Pet>
        </entityShowData>
		<PetInitData>
			<data eqClassName="Pet_Tyrannosaurs_1" petId="pet0" petXMLPath="NewGameFolder/Pets/pet0DuPiLong.xml" />
			<data  eqClassName="Pet_Tyrannosaurs_2"  petId="pet0" petXMLPath="NewGameFolder/Pets/pet0DuPiLong.xml" />
			<data  eqClassName="Pet_Tyrannosaurs_3"  petId="pet0" petXMLPath="NewGameFolder/Pets/pet0DuPiLong.xml"/>
				
			<data eqClassName="Pet_XiaoPiKaQiu_1"  petId="pet1" petXMLPath="NewGameFolder/Pets/pet1PiKaQiu.xml" />
			<data  eqClassName="Pet_XiaoPiKaQiu_2"  petId="pet1" petXMLPath="NewGameFolder/Pets/pet1PiKaQiu.xml" />
			<data  eqClassName="Pet_XiaoPiKaQiu_3"  petId="pet1" petXMLPath="NewGameFolder/Pets/pet1PiKaQiu.xml"/>	
			
			<data eqClassName="Pet_ShiTouRen_1"  petId="pet2" petXMLPath="NewGameFolder/Pets/pet2ShiTouRen.xml" />
			<data  eqClassName="Pet_ShiTouRen_2"  petId="pet2" petXMLPath="NewGameFolder/Pets/pet2ShiTouRen.xml" />
			<data  eqClassName="Pet_ShiTouRen_3"  petId="pet2" petXMLPath="NewGameFolder/Pets/pet2ShiTouRen.xml" />
					
			<data eqClassName="Pet_HuoRen_1"  petId="pet3" petXMLPath="NewGameFolder/Pets/pet3XiaoHuoRen.xml" />
			<data  eqClassName="Pet_HuoRen_2"  petId="pet3" petXMLPath="NewGameFolder/Pets/pet3XiaoHuoRen.xml" />
			<data  eqClassName="Pet_HuoRen_3"   petId="pet3" petXMLPath="NewGameFolder/Pets/pet3XiaoHuoRen.xml"/>
				
			<data eqClassName="Pet_JiQiRen_1"  petId="pet4" petXMLPath="NewGameFolder/Pets/pet4JiQiRen.xml"/>
			<data  eqClassName="Pet_JiQiRen_2"  petId="pet4" petXMLPath="NewGameFolder/Pets/pet4JiQiRen.xml"/>
			<data  eqClassName="Pet_JiQiRen_3"  petId="pet4" petXMLPath="NewGameFolder/Pets/pet4JiQiRen.xml" />
			
			<data eqClassName="Pet_BingLong_1"  petId="pet5" petXMLPath="NewGameFolder/Pets/pet5BingLong.xml" />
			<data  eqClassName="Pet_BingLong_2"  petId="pet5" petXMLPath="NewGameFolder/Pets/pet5BingLong.xml"/>
			<data  eqClassName="Pet_BingLong_3"  petId="pet5" petXMLPath="NewGameFolder/Pets/pet5BingLong.xml"/>
				
			<data eqClassName="Pet_LiHe_1"  petId="pet6" petXMLPath="NewGameFolder/Pets/pet6LiHeBaoBao.xml"/>
			<data  eqClassName="Pet_LiHe_2"  petId="pet6" petXMLPath="NewGameFolder/Pets/pet6LiHeBaoBao.xml" />
			<data  eqClassName="Pet_LiHe_3"  petId="pet6" petXMLPath="NewGameFolder/Pets/pet6LiHeBaoBao.xml" />
				
			<data eqClassName="Pet_Jian_1"  petId="pet7" petXMLPath="NewGameFolder/Pets/pet7Jian.xml" />
			<data  eqClassName="Pet_Jian_2"  petId="pet7" petXMLPath="NewGameFolder/Pets/pet7Jian.xml"/>
			<data  eqClassName="Pet_Jian_3"  petId="pet7" petXMLPath="NewGameFolder/Pets/pet7Jian.xml"/>
			
			<data eqClassName="Pet_RenXingBingLong_4"  petId="pet8" petXMLPath="NewGameFolder/Pets/Pet8LongJianShi.xml" />
				
			<data eqClassName="Pet_JuYanZhanJiang_4"  petId="pet9" petXMLPath="NewGameFolder/Pets/pet9JuYanZhanJiang.xml" />
			
			<data eqClassName="Pet_HanDiJuLong_4"  petId="pet10" petXMLPath="NewGameFolder/Pets/pet10HanDiJuLong.xml" />

			<data eqClassName="Pet_Jian_4"  petId="pet11" petXMLPath="NewGameFolder/Pets/pet11MoJian4.xml" />
			
			<data eqClassName="Pet_Lu_1"  petId="pet12" petXMLPath="NewGameFolder/Pets/pet12Lu.xml"/>
			<data  eqClassName="Pet_Lu_2"  petId="pet12" petXMLPath="NewGameFolder/Pets/pet12Lu.xml"/>
			<data  eqClassName="Pet_Lu_3"  petId="pet12" petXMLPath="NewGameFolder/Pets/pet12Lu.xml"/>
			
			<data eqClassName="Pet_TianShiLu_4"  petId="pet13" petXMLPath="NewGameFolder/Pets/pet13TianShiLu.xml"/>
			
			<data eqClassName="Pet_EMoLu_4"  petId="pet14" petXMLPath="NewGameFolder/Pets/pet14EMoLu.xml"  />
			
			<data eqClassName="Pet_LiHe_4"  petId="pet15" petXMLPath="NewGameFolder/Pets/pet15XiaoChou.xml"  />
			
			<data eqClassName="Pet_FengHuang_1"  petId="pet16" petXMLPath="NewGameFolder/Pets/pet16BingjingFengHuan.xml"/>
			<data  eqClassName="Pet_FengHuang_2"  petId="pet16" petXMLPath="NewGameFolder/Pets/pet16BingjingFengHuan.xml"/>
			<data  eqClassName="Pet_FengHuang_3"  petId="pet16" petXMLPath="NewGameFolder/Pets/pet16BingjingFengHuan.xml"/>
			
			<data eqClassName="Pet_FengHuang_4"  petId="pet17" petXMLPath="NewGameFolder/Pets/pet17BingFeng.xml"  />

			<data eqClassName="Pet_ChongMing_1"  petId="pet18" petXMLPath="NewGameFolder/Pets/pet18ChongMing.xml"/>
			<data  eqClassName="Pet_ChongMing_2"  petId="pet18" petXMLPath="NewGameFolder/Pets/pet18ChongMing.xml"/>
			<data  eqClassName="Pet_ChongMing_3"  petId="pet18" petXMLPath="NewGameFolder/Pets/pet18ChongMing.xml"/>
			<data  eqClassName="Pet_ChongMing_4"  petId="pet19" petXMLPath="NewGameFolder/Pets/pet19ChongMing.xml"/>
			<data eqClassName="Pet_MoLong_1"  petId="pet20" petXMLPath="NewGameFolder/Pets/pet20MoLong.xml"/>
			<data  eqClassName="Pet_MoLong_2"  petId="pet20" petXMLPath="NewGameFolder/Pets/pet20MoLong.xml"/>
			<data  eqClassName="Pet_MoLong_3"  petId="pet20" petXMLPath="NewGameFolder/Pets/pet20MoLong.xml"/>
			<data  eqClassName="Pet_MoLong_4"  petId="pet21" petXMLPath="NewGameFolder/Pets/pet21MoLong.xml"/>
			<data eqClassName="Pet_ZhuLong_1"  petId="pet22" petXMLPath="NewGameFolder/Pets/pet22ZhuLong.xml"/>
			<data  eqClassName="Pet_ZhuLong_2"  petId="pet22" petXMLPath="NewGameFolder/Pets/pet22ZhuLong.xml"/>
			<data  eqClassName="Pet_ZhuLong_3"  petId="pet22" petXMLPath="NewGameFolder/Pets/pet22ZhuLong.xml"/>
			<data  eqClassName="Pet_ZhuLong_4"  petId="pet23" petXMLPath="NewGameFolder/Pets/pet23ZhuLong.xml"/>
			<data eqClassName="Pet_QiongQi_1"  petId="pet24" petXMLPath="NewGameFolder/Pets/pet24QiongQi.xml"/>
			<data  eqClassName="Pet_QiongQi_2"  petId="pet24" petXMLPath="NewGameFolder/Pets/pet24QiongQi.xml"/>
			<data  eqClassName="Pet_QiongQi_3"  petId="pet24" petXMLPath="NewGameFolder/Pets/pet24QiongQi.xml"/>
			<data  eqClassName="Pet_QiongQi_4"  petId="pet25" petXMLPath="NewGameFolder/Pets/pet25QiongQi.xml"/>
		</PetInitData>
		<MedalInitData>
			<data eqClassName="Medal_Medal1" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal1.swf" className="TitleSpriteAnimationV1" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord1.swf" className="TitleSpriteWordV1" />
			</data>
			<data eqClassName="Medal_Medal2" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal2.swf" className="TitleSpriteAnimationV2" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord2.swf" className="TitleSpriteWordV2" />
			</data>
			<data eqClassName="Medal_Medal3" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal3.swf" className="TitleSpriteAnimationV3" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord3.swf" className="TitleSpriteWordV3" />
			</data>
			<data eqClassName="Medal_Medal4" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal4.swf" className="TitleSpriteAnimationV4" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord4.swf" className="TitleSpriteWordV4" />
			</data>
			<data eqClassName="Medal_Medal5" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal5.swf" className="TitleSpriteAnimationV5" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord5.swf" className="TitleSpriteWordV5" />
			</data>
			<data eqClassName="Medal_Medal6" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal6.swf" className="TitleSpriteAnimationV6" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord6.swf" className="TitleSpriteWordV6" />
			</data>
			<data eqClassName="Medal_Medal7" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal7.swf" className="TitleSpriteAnimationV7" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord7.swf" className="TitleSpriteWordV7" />
			</data>
			<data eqClassName="Medal_Medal8" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal8.swf" className="TitleSpriteAnimationV8" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord8.swf" className="TitleSpriteWordV8" />
			</data>
			<data eqClassName="Medal_Medal9" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal9.swf" className="TitleSpriteAnimationV9" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord9.swf" className="TitleSpriteWordV9" />
			</data>
			<data eqClassName="Medal_Medal10" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal10.swf" className="TitleSpriteAnimationV10" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord10.swf" className="TitleSpriteWordV10" />
			</data>
			<data eqClassName="Medal_Medal11" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal11.swf" className="TitleSpriteAnimationV11" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord11.swf" className="TitleSpriteWordV11" />
			</data>
			<data eqClassName="Medal_Medal12" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal12.swf" className="TitleSpriteAnimationV12" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord12.swf" className="TitleSpriteWordV12" />
			</data>
			<data eqClassName="Medal_Medal13" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal13.swf" className="TitleSpriteAnimationV13" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord13.swf" className="TitleSpriteWordV13" />
			</data>
			<data eqClassName="Medal_Medal14" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal14.swf" className="TitleSpriteAnimationV14" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord14.swf" className="TitleSpriteWordV14" />
			</data>
			<data eqClassName="Medal_Medal15" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal15.swf" className="TitleSpriteAnimationV15" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord15.swf" className="TitleSpriteWordV15" />
			</data>
			<data eqClassName="Medal_Medal16" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal17.swf" className="TitleSpriteAnimationV17" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord17.swf" className="TitleSpriteWordV17" />
			</data>
			<data eqClassName="Medal_Medal17" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal16.swf" className="TitleSpriteAnimationV16" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord16.swf" className="TitleSpriteWordV16" />
			</data>
			<data eqClassName="Medal_Medal18" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal18.swf" className="TitleSpriteAnimationV18" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord18.swf" className="TitleSpriteWordV18" />
			</data>
			<data eqClassName="Medal_Medal19" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal19.swf" className="TitleSpriteAnimationV19" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord19.swf" className="TitleSpriteWordV19" />
			</data>
			<data eqClassName="Medal_Medal20" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal20.swf" className="TitleSpriteAnimationV20" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord20.swf" className="TitleSpriteWordV20" />
			</data>
			<data eqClassName="Medal_Medal21" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal21.swf" className="TitleSpriteAnimationV21" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord21.swf" className="TitleSpriteWordV21" />
			</data>
			<data eqClassName="Medal_Medal22" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal22.swf" className="TitleSpriteAnimationV22" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord22.swf" className="TitleSpriteWordV22" />
			</data>
			<data eqClassName="Medal_Medal23" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal23.swf" className="TitleSpriteAnimationV23" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord23.swf" className="TitleSpriteWordV23" />
			</data>
			<data eqClassName="Medal_Medal24" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal24.swf" className="TitleSpriteAnimationV24" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord24.swf" className="TitleSpriteWordV24" />
			</data>
			<data eqClassName="Medal_Medal25" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal25.swf" className="TitleSpriteAnimationV25" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord25.swf" className="TitleSpriteWordV25" />
			</data>
			<data eqClassName="Medal_Medal26" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal26.swf" className="TitleSpriteAnimationV26" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord26.swf" className="TitleSpriteWordV26" />
			</data>
			<data eqClassName="Medal_Medal27" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal27.swf" className="TitleSpriteAnimationV27" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord27.swf" className="TitleSpriteWordV27" />
			</data>
			<data eqClassName="Medal_Medal28" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal28.swf" className="TitleSpriteAnimationV28" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord28.swf" className="TitleSpriteWordV28" />
			</data>
			<data eqClassName="Medal_Medal29" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal29.swf" className="TitleSpriteAnimationV29" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord29.swf" className="TitleSpriteWordV29" />
			</data>
			<data eqClassName="Medal_Medal30" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal30.swf" className="TitleSpriteAnimationV30" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord30.swf" className="TitleSpriteWordV30" />
			</data>
			<data eqClassName="Medal_Medal31" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal31.swf" className="TitleSpriteAnimationV31" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord31.swf" className="TitleSpriteWordV31" />
			</data>
			<data eqClassName="Medal_Medal32" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal32.swf" className="TitleSpriteAnimationV32" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord32.swf" className="TitleSpriteWordV32" />
			</data>
			<data eqClassName="Medal_Medal33" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal33.swf" className="TitleSpriteAnimationV33" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord33.swf" className="TitleSpriteWordV33" />
			</data>
				<data eqClassName="Medal_Medal34" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal34.swf" className="TitleSpriteAnimationV34" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord34.swf" className="TitleSpriteWordV34" />
			</data>
			
			
				<data eqClassName="Medal_Medal35" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal35.swf" className="TitleSpriteAnimationV35" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord35.swf" className="TitleSpriteWordV35" />
			</data>
			
					<data eqClassName="Medal_Medal36" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal36.swf" className="TitleSpriteAnimationV36" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord36.swf" className="TitleSpriteWordV36" />
			</data>
			
			
			<data eqClassName="Medal_Medal37" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal37.swf" className="TitleSpriteAnimationV37" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord37.swf" className="TitleSpriteWordV37" />
			</data>
			
			
						<data eqClassName="Medal_Medal38" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal38.swf" className="TitleSpriteAnimationV38" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord38.swf" className="TitleSpriteWordV38" />
			</data>
			
						<data eqClassName="Medal_Medal39" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal39.swf" className="TitleSpriteAnimationV39" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord39.swf" className="TitleSpriteWordV39" />
			</data>
			
						<data eqClassName="Medal_Medal40" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal40.swf" className="TitleSpriteAnimationV40" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord40.swf" className="TitleSpriteWordV40" />
			</data>
			
			
						<data eqClassName="Medal_Medal41" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal41.swf" className="TitleSpriteAnimationV41" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord41.swf" className="TitleSpriteWordV41" />
			</data>
			
			
			
						<data eqClassName="Medal_Medal42" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal42.swf" className="TitleSpriteAnimationV42" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord42.swf" className="TitleSpriteWordV42" />
			</data>
			
			
						<data eqClassName="Medal_Medal43" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal43.swf" className="TitleSpriteAnimationV43" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord43.swf" className="TitleSpriteWordV43" />
			</data>
			
			
						<data eqClassName="Medal_Medal44" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal44.swf" className="TitleSpriteAnimationV44" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord44.swf" className="TitleSpriteWordV44" />
			</data>
			
			
						<data eqClassName="Medal_Medal45" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal45.swf" className="TitleSpriteAnimationV45" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord45.swf" className="TitleSpriteWordV45" />
			</data>
			
						<data eqClassName="Medal_Medal46" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal46.swf" className="TitleSpriteAnimationV46" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord46.swf" className="TitleSpriteWordV46" />
			</data>
			
						<data eqClassName="Medal_Medal47" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal47.swf" className="TitleSpriteAnimationV47" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord47.swf" className="TitleSpriteWordV47" />
			</data>
			
				<data eqClassName="Medal_Medal48" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal48.swf" className="TitleSpriteAnimationV48" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord48.swf" className="TitleSpriteWordV48" />
			</data>
				<data eqClassName="Medal_Medal49" >
				<dynamic swfPath="NewGameFolder/MedalSource/Medal49.swf" className="TitleSpriteAnimationV49" />
				<static swfPath="NewGameFolder/MedalSource/MedalWord49.swf" className="TitleSpriteWordV49" />
			</data>
			
		</MedalInitData>
		<testSave>
			<root>
  <Data uid="123456" name="test" nickName="%23456" idx="1" ver="810">
    <TieShan name="TieShan" playerId="playerOne" experiencePercent="1.0000" bloodPercent="1.0000" magicPercent="0.7296" level="45" money="0" sF="1" medal="0">
      <Package>
        <item id="11000004" num="6"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
      </Package>
      <Storage>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
      </Storage>
      <Medals/>
      <InformationPanel>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
        <item id="0"/>
      </InformationPanel>
      <PetPanel/>
      <DMPanel>
        <attack/>
        <defence/>
      </DMPanel>
      <ShiTu xLUsedNum="0" xLDate="">
        <xiuLian id="attackXL_sF_0" xLValue="0"/>
        <xiuLian id="defenceXL_sF_0" xLValue="0"/>
        <xiuLian id="bloodXL_sF_0" xLValue="0"/>
        <xiuLian id="dodgeXL_sF_0" xLValue="0"/>
        <xiuLian id="hitXL_sF_0" xLValue="0"/>
        <xiuLian id="criticalRateXL_sF_0" xLValue="0"/>
        <xiuLian id="riotXL_sF_0" xLValue="0"/>
      </ShiTu>
      <Hatch/>
      <Skill>
        <item id="10107100" currentCDTime="0"/>
        <item id="10107101" currentCDTime="0"/>
        <item id="10107102" currentCDTime="0"/>
        <item id="10107103" currentCDTime="0"/>
        <item id="10107104" currentCDTime="0"/>
        <item id="10107105" currentCDTime="0"/>
        <item id="10107106" currentCDTime="0"/>
        <item id="10107107" currentCDTime="0"/>
      </Skill>
      <EqMagicT/>
    </TieShan>
    <PublicStorage>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
      <item id="0"/>
    </PublicStorage>
    <TaskGoals/>
    <Task reCount="0" reDate="2016-01-27"/>
    <Farm sunValue="0">
      <land id="100100" state="0"/>
      <other id="1000" hv="-6_-3"/>
      <other id="1001" hv="-1_9"/>
      <other id="1002" hv="-4_10"/>
    </Farm>
    <LDF id="100100" state="0"/>
    <Buff/>
    <Protect gD="2013-01-01 00:00:00"/>
    <VIP oldDateGetGift=""/>
    <EGD gGN=""/>
    <Mirage mirageDate="" mirageNum="0"/>
    <PK allMatch="5" winMatch="1" failMatch="4" pkPoint="15" allMatch2="2" winMatch2="1" failMatch2="1" matchD2="2016-01-27 16:59:24" isReseted="0" winMonth="1" isSignUp2="0" isSignUp="0" monthTime="2016-01-27 13:58:26" mingrenTwoPlayer="0"/>
    <Sign num="0" signD="" gAD=""/>
    <RG rGNum="0" rGDate="2016-01-27" buyNum="0"/>
    <ExE haveNum="0" exeNum="0"/>
    <NNameData/>
    <OnLineGiftBag remainTime="0" id="gB1" dayTime="2016-01-27 13:52:39"/>
    <MainLineTask phase="phase_1"/>
    <WorldBoss p="NaN" pT="2016-01-27 13:58:26"/>
    <Society>
      <sign sT="" sN="0" gT=""/>
      <contri eN="0" mN="0" tN="0" time=""/>
      <shop/>
    </Society>
    <Boss t="2016-01-27 13:52:39"/>
    <PetBoss t="2016-01-27 13:52:39"/>
    <PK2>
      <onePK d="2016-01-27 14:55:22">
        <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
        <player pkI="1" uid="test123" sI="1" pkS="2" p1T="Fox"/>
        <player pkI="2" uid="test123" sI="1" pkS="2" p1T="Fox"/>
        <player pkI="3" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      </onePK>
      <twoPK d="2016-01-27 15:00:18">
        <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      </twoPK>
    </PK2>
    <GYP t="2016-01-27 13:52:39"/>
    <XM t="2016-01-27 14:58:15">
      <one id="haiDiXunBao" n="1"/>
      <one id="daZhanLongWan" n="0"/>
      <one id="jingLongZhenShen" n="0"/>
    </XM>
    <endlessone p="160" r="0" t="0" nd="0" on="0" tw="0" os="0" ts="0" oms="0" tms="0" omrms="0" tmrms="0" nw="1584" ln=""/>
    <firstPay isGetOnce="0" isGetAwardToday="0" isUseLongwang="0" isUseDuowen="0" isUseDabai="0" everydayTimeStr="2016-01-27 13:58:26"/>
    <newSign signedArr="" currentGiftType="0" isGetGiftTime0="0" isGetGiftTime1="0" isGetGiftTime2="0" isGetGiftTime3="0" isGetGiftTime4="0" isGetGiftTime5="0" timeStr="2016-01-27 13:58:26"/>
    <AutoPets/>
    <CollectTime/>
    <SmallAssistant>
      <ActiveTask t="2016-01-27 13:52:39">
        <task id="active1"/>
        <task id="active7"/>
        <task id="active10"/>
        <task id="active2"/>
        <task id="active3"/>
        <task id="active4"/>
        <task id="active5"/>
        <task id="active9">
          <taskGoal id="activeTaskGoal6" num="1"/>
        </task>
        <task id="active6">
          <taskGoal id="activeTaskGoal4" num="1"/>
        </task>
        <task id="active8"/>
      </ActiveTask>
      <LevelTask/>
    </SmallAssistant>
    <PK21 rT="2016-01-27 15:05:18" allRN="5" failRN="5" pkNT="2016-01-27 15:05:18" pkN="5">
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
      <player pkI="0" uid="test123" sI="1" pkS="2" p1T="Fox"/>
    </PK21>
    <svA hounianCountDownTime20160122="85505"/>
    <Ada t="2016-01-27 13:52:41" rt="-832458" pm="90"/>
    <dL/>
    <Mounts/>
    <Other unAbleBuyEqs="">
      <cheatData/>
    </Other>
    <SantaClaus killNum="0"/>
  </Data>
</root>
		</testSave>
	</UIData>
