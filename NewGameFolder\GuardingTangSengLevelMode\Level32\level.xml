<?xml version="1.0" encoding="utf-8" ?>
<data id="Level32"
	swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/level.swf"
	className="LevelMap32" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
		swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf"
		className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
	<Waves totalWaveNum="21">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="0" duration="5000" num="7" isFallDown="0" />
			

		</Wave>
		<Wave waveCount="2" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="15000" duration="5000" num="8"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="30000" duration="10000" num="7"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="45000" duration="50000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="5" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="60000" duration="10000" num="7"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="6" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="75000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="7" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="90000" duration="10000" num="7"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="8" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="105000" duration="5000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill"
				xmlPath="boss" startTime="105000" duration="1000" num="1" />
		</Wave>
		<Wave waveCount="9" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="120000" duration="5000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="10" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="135000" duration="10000" num="7"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="11" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="150000" duration="10000" num="8"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="12" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="165000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="13" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="180000" duration="10000" num="6"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="14" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="195000" duration="10000" num="8"
				isFallDown="1" />
		</Wave>
		<Wave waveCount="15" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="210000" duration="5000" num="7"
				isFallDown="0" />
			
		</Wave>
		<Wave waveCount="16" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="225000" duration="10000" num="10"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill"
				xmlPath="boss" startTime="225000" duration="1000" num="1" />
		</Wave>
		<Wave waveCount="17" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="240000" duration="10000" num="5"
				isFallDown="1" />
			
		</Wave>
		<Wave waveCount="18" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="255000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="19" totalEnemyNum="20" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="270000" duration="10000" num="7"
				isFallDown="1" />
		</Wave>

		<Wave waveCount="20" totalEnemyNum="20" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="285000" duration="10000" num="8"
				isFallDown="0" />
		
		</Wave>
		<Wave waveCount="21" totalEnemyNum="10" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_BombOfDeath"
				xmlPath="xiaoBing" startTime="300000" duration="10000" num="10"
				isFallDown="1" />


		</Wave>
	</Waves>
	<EqDrop>
		<xiaoBing noDropProWeight="300">
			<!--proWeight 概率权重 -->

	
			<!-- 昼夜玄石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Zhouyexuanshi_S"
				proWeight="1" />
			
			<!-- 暴击4级 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_CriticalRate4_S"
				proWeight="1" />
			<!-- 闪避4级 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Dodge4_S"
				proWeight="1" />
			
			
			
			<!-- 魔龙骨 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Molonggu_S"
				proWeight="1" />
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_ChongMing_S"
				proWeight="1" />	
			<!-- 蓝宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp3_S"
				proWeight="8" />
			<!-- 人品宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp3_S"
				proWeight="8" />
			<!-- 攻击宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack3_S"
				proWeight="8" />
			<!-- 生命宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp3_S"
				proWeight="11" />
			<!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence3_S"
				proWeight="12" />
			<!-- 开孔灵符 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S"
				proWeight="12" />
			<!-- 碎石锤 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S"
				proWeight="6" />

			<!-- 红药 -->
			<item dropClassName="Item_HpUp" proWeight="100" />
			<!-- 蓝药 -->
			<item dropClassName="Item_MpUp" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_10000" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_50000" proWeight="50" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_100000" proWeight="5" />
			<!-- 灵兽石 -->
			<item dropClassName="Item_StrengthenNum_10" proWeight="5" />

			<!-- 圣灵精华 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S"
				proWeight="15" />
			<!-- 一级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S"
				proWeight="13" />
			<!-- 二级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_TwoFire_S"
				proWeight="8" />
			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="8" />
			<!-- 幸运宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S"
				proWeight="18" />
			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="6" />

		</xiaoBing>
		<boss noDropProWeight="150">
			<!--proWeight 概率权重 -->
			<dropNumData>
				<smallDropNumData proWeight="10">
					<numData num="1" proWeight="5" />
					<numData num="2" proweight="5" />
				</smallDropNumData>
				<bigDropNumData proWeight="1">
					<numData num="4" proWeight="8" />
					<numData num="5" proWeight="2" />
				</bigDropNumData>
			</dropNumData>
<!-- 烛龙果模具-->
			<item dropClassName="UI.Equipments.SceneEquipments.Scroll_Zhulongguo_S"
				proWeight="1" />

		    <!-- 魔龙-->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_Molong_S"
				proWeight="2" />
		    <item dropClassName="UI.Equipments.SceneEquipments.Egg_ChongMing_S"
				proWeight="1" />	
			<!-- 洗灵丹 -->
			<item dropClassName="UI.Equipments.StackEquipments.Material_XiLiDan_S"
				proWeight="2" />
			<!-- 开运灵石 -->
			<item dropClassName="UI.Equipments.StackEquipments.Potion_Kaiyunlingshi_S"
				proWeight="4" />
			<!-- 天王碎片 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_HongSe_S"
				proWeight="80" />
			<!-- 攻击石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack4_S"
				proWeight="100" />

			
			<!-- 凤凰蛋 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_FengHuang_S"
				proWeight="10" />


			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="20" />

			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="40" />
				
			<!-- 暴击模具 -->
			<item dropClassName="UI.Equipments.Scroll_InsetGem116_S"
				proWeight="4" />	


		</boss>

	</EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>

	</sharedAnimationDefinitions>

	<xiaoBing>
		<!--敌人数据 -->
		<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
			<animationDefinition id="xiaoBingFallDownShow1"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</fallDownEffect>
		<bombInfo speed="-30" time="5" normalDead="true" />
		<enemyData>

			<!-- totalHp=血量 attack=攻击 expOfDieThisEnemy=经验 defence=防御 dogdeRate=闪避 
				criticalRate=暴击率 criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中 -->
			<data att="totalHp" value="1800000" />
			<data att="attack" value="13000" />
			<data att="expOfDieThisEnemy" value="60000" />
			<data att="defence" value="10000" />
			<data att="dogdeRate" value="0.08" />
			<data att="criticalRate" value="0.5" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="1.5" />
			<data att="hitRate" value="0.09" />
		</enemyData>
		<!--移动速度以秒为单位 -->
		<animal id="enemy31" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="60" bodyHeight="140" walkSpeed="30"
			runSpeed="0">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


         <notBePushed>true</notBePushed><!--不能被推 -->



			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-30" z="-1" xRange="100" yRange="100"
				zRange="100" />

			<idle defId="walk_enemy32" />
			<walk defId="walk_enemy32" />
			<run defId="walk_enemy32" />
			<attack defId="attack_enemy32" />
			<hurt defId="hurt_enemy32" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_enemy32" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow1" />

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>

				<animationDefinition id="walk_enemy32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Enemy.swf"
						showClass="Walk_Monster_32" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="hurt_enemy32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Enemy.swf"
						showClass="BeAttack_Monster_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Enemy.swf"
						showClass="Attack_Monster_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Enemy.swf"
						showClass="Dead_Monster_32" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="change" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Enemy.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>



				<animationDefinition id="enemyFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="xiaoBingFallDownShow1"
					rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="FallDownEffect" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>

			<shows>
				<show defId="walk_enemy32" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Enemy.swf"
					showClass="Walk_Monster_InSuper_32" x_offset="0" y_offset="0" />
				<show defId="attack_enemy32" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Enemy.swf"
					showClass="Attack_Monster_InSuper_32" x_offset="0" y_offset="0" />
				<show defId="die_enemy32" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Enemy.swf"
					showClass="Dead_Monster_InSuper_32" x_offset="0" y_offset="0" />
			</shows>

		</animal>
	</xiaoBing>


	<boss>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="2800000" />
			<data att="attack" value="15000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="12000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>5</skillCd>
		
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		
		<animal id="boss32" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="12"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->

			<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->
			
			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="90" yRange="100"
				zRange="100" />

			<idle defId="idle_boss32" />
			<walk defId="walk_boss32" />
			<run defId="run_boss32" />
			<attack defId="attack_boss32" />
			<hurt defId="hurt_boss32" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss32" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<!-- <skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao" -->
			<!-- x="-30" y="-40" z="-1" xRange="900" yRange="400" zRange="150" -->
			<!-- bodyDefId="skillAnimation" hurtDuration="3500" -->
			<!-- bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^" -->
			<!-- effectAddtoTargetId="tunshi" everyEntityAddShowIsFrontOfBody="0"> -->
			<!-- <shakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf" 
				className="ShakeView" /> -->
			<!-- <animationDefinition id="tunshi" rows="1" cols="1" -->
			<!-- walkable="false" overlap="false" frameInterval="1" -->
			<!-- defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> -->
			<!-- <show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf" -->
			<!-- showClass="tunshi" x_offset="0" y_offset="0" /> -->
			<!-- </animationDefinition> -->
			<!-- </skill> -->

			<skill id="BossDaZhao" className="YJFY.BossMode.Boss1.BossDaZhao1"
				bodyDefId="daZhaoBodyShow" dropShowDefId="daZhaoDropShow">
				<dropAttackRange x="-50" y="-50" z="-1" xRange="100"
					yRange="100" zRange="100" />
				<animationDefinition id="daZhaoDropShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="ItemDropEffect" x_offset="0" y_offset="0" />
				</animationDefinition>

			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Walk_Boss_32" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Walk_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss29" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_29" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="BeAttack_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Attack_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Dead_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="daZhaoBodyShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="daZhaoDropShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf" showClass="ItemDropEffect"
						x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</boss>
</data>
