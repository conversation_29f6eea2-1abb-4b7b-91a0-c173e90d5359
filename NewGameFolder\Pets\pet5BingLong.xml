<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet5" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet5Idle" />
		<walk defId="pet5Walk" />
		<run defId="pet5Run" />
		<attack defId="pet5Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet5Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet5Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	    <skill id="Skill_Pet5Skill" className="YJFY.Skill.PetSkills.Skill_Pet5Skill" x="-480"
			y="-280" z="-1" xRange="960" yRange="480" zRange="100" bodyDefId="pet5SkillBodyShow" bodyAttackReachFrameLabel="down" 
			bodySkillEndFrameLabel="end^stop^"  everyEntityAddShowDefId="pet5SkillBinExplose" 
			everyEntityAddShowIsFrontOfBody="1"   
			fronzeFrameLabel="blow" fronzeShowId="pet5SkillFronze">
			<animationDefinition id="pet5SkillFronze" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet5Effect.swf"
					showClass="PetSkill5Effect_BeAtkStart" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet5SkillBinExplose" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet5Effect.swf"
					showClass="PetSkill5Effect_BeAtkEnd" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet5Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet5Body.swf"
					showClass="PetStand5_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet5Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet5Body.swf"
					showClass="PetWalk5_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet5SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet5Body.swf"
					showClass="PetSkill5Attack_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet5SkillFrontShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet5Body.swf"
					showClass="PetSkill5Effect_Word_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			<animationDefinition id="pet5SkillFronze" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet5Effect.swf"
					showClass="PetSkill5Effect_BeAtkStart" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet5SkillBinExplose" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet5Effect.swf"
					showClass="PetSkill5Effect_BeAtkEnd" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet5Idle" eqClassName="Pet_BingLong_1"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetStand5_1"
				x_offset="0" y_offset="0" />
			<show defId="pet5Walk" eqClassName="Pet_BingLong_1"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetWalk5_1"
				x_offset="0" y_offset="0" />
           <show defId="pet5SkillBodyShow" eqClassName="Pet_BingLong_1"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetSkill5Attack_1"
				x_offset="0" y_offset="0" />
			<show defId="pet5SkillFrontShow" eqClassName="Pet_BingLong_1"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetSkill5Effect_Word_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet5Idle" eqClassName="Pet_BingLong_2"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetStand5_2"
				x_offset="0" y_offset="0" />
			<show defId="pet5Walk" eqClassName="Pet_BingLong_2"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetWalk5_2"
				x_offset="0" y_offset="0" />
           <show defId="pet5SkillBodyShow" eqClassName="Pet_BingLong_2"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetSkill5Attack_2"
				x_offset="0" y_offset="0" />
			<show defId="pet5SkillFrontShow" eqClassName="Pet_BingLong_2"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetSkill5Effect_Word_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet5Idle" eqClassName="Pet_BingLong_3"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetStand5_3"
				x_offset="0" y_offset="0" />
			<show defId="pet5Walk" eqClassName="Pet_BingLong_3"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetWalk5_3"
				x_offset="0" y_offset="0" />
           <show defId="pet5SkillBodyShow" eqClassName="Pet_BingLong_3"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetSkill5Attack_3"
				x_offset="0" y_offset="0" />
			<show defId="pet5SkillFrontShow" eqClassName="Pet_BingLong_3"
				swfPath="NewGameFolder/PetSource/Pet5Body.swf" showClass="PetSkill5Effect_Word_3"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
