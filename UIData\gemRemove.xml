<?xml version="1.0" encoding="utf-8" ?>
<data>
	<defau>  <!--当没有相应装备的配置时，所要使用的默认配置-->
		<defau>   <!--当没有相应的宝石时，所要使用的默认配置-->
			<hole1  successRate="0.9" needMoney="100000"> <!--当没有其它孔的配置是，默认为孔1的配置-->
				<material id="10500031" num="1" />
				<material id="10500205" num="1" /><!--目前用于灵符-->
			</hole1>
			<hole2  successRate="0.8" needMoney="150000"> <!--当没有其它孔的配置是，默认为孔1的配置-->
				<material id="10500031" num="2" />
				<material id="10500205" num="2" /><!--目前用于灵符-->
			</hole2>
			<hole3  successRate="0.7" needMoney="250000"> <!--当没有其它孔的配置是，默认为孔1的配置-->
				<material id="10500031" num="3" />
				<material id="10500205" num="3" /><!--目前用于灵符-->
			</hole3>
			
			<hole4  successRate="0.6" needMoney="350000"> <!--当没有其它孔的配置是，默认为孔1的配置-->
				<material id="10500031" num="5" />
				<material id="10500205" num="5" /><!--目前用于灵符-->
			</hole4>
			<hole5  successRate="0.5" needMoney="450000"> <!--当没有其它孔的配置是，默认为孔1的配置-->
				<material id="10500031" num="8" />
				<material id="10500205" num="8" /><!--目前用于灵符-->
			</hole5>
		</defau>
		<gem num="1" id1="">
			<hole1 />
		</gem>
	</defau>
	<equipment num="1" id1="" >
		<defau>   <!--当没有相应的宝石时，所要使用的默认配置-->
			<hole1 successRate="1" needMoney="1000" >
				<material id="10500031" num="1" />
			</hole1>
		</defau>
		<gem num="1" id1="">
			<hole1 />
		</gem>
	</equipment>
</data>