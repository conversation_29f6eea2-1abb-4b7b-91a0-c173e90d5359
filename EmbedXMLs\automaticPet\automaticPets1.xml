<?xml version="1.0" encoding="utf-8" ?>
<automaticPets >
    <!--经验小虾米 -->
  <automaticPet id="smallShrimpKing" name="经验小虾米" logicPartPetXMLPath="NewGameFolder/Pets2/smallShrimp.xml" 
  attackSkillId="smallShrimpAttack" showSwfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf" showClassName="ShowOfSmallShrimp">
	 <initSkillsData >
		 <activeSkill id="smallShrimpAttack" level="1"  />
		 <!--<activeSkill id="" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" />-->
	 </initSkillsData>
     <pingJieData defaultId="d" showShadowSwfPath="NewGameFolder/AutomaticPetSource/Shadow.swf">
		 <pingJie id="d" name="D" proValue="0" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"  x_offset="" y_offset=""/>
		 <pingJie id="c" name="C" proValue="0.1" showShadowClassName="ShowShadowOfAutomaticPet2" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="b" name="B" proValue="0.15" showShadowClassName="ShowShadowOfAutomaticPet3" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="a" name="A" proValue="0.20" showShadowClassName="ShowShadowOfAutomaticPet4" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="s" name="S" proValue="0.25" showShadowClassName="ShowShadowOfAutomaticPet5" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <!--<pingJie id="ss" name="SS" proValue="0.30" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>
		 <pingJie id="sss" name="SSS" proValue="0.40" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>-->
	 </pingJieData>
	 
	 
	 <!-- 等级对应属性-->

     <levelData level="1" >
		<data att="totalHp" value="500" /><!-- 血量 -->
		<data att="totalMp" value="10" /><!-- 魔法 -->
		<data att="attack" value="100" /><!-- 攻击 -->
		<data att="defence" value="50" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0201" /><!-- 闪避 -->
		<data att="criticalRate" value="0.05" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.06" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0101" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="20" /> <!--升级所需经验-->
		<data att="devourExp" value="1000" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
  </automaticPet>   
    <!-- 虾兵 -->
  <automaticPet id="smallShrimp" name="虾兵" logicPartPetXMLPath="NewGameFolder/Pets2/smallShrimp.xml" 
  attackSkillId="smallShrimpAttack" showSwfPath="NewGameFolder/AutomaticPetSource/SmallShrimp.swf" showClassName="ShowOfSmallShrimp">
	 <initSkillsData >
		 <activeSkill id="smallShrimpAttack" level="1"  />
		 <!--<activeSkill id="" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" />-->
	 </initSkillsData>
     <pingJieData defaultId="d" showShadowSwfPath="NewGameFolder/AutomaticPetSource/Shadow.swf">
		 <pingJie id="d" name="D" proValue="0" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="c" name="C" proValue="0.08" showShadowClassName="ShowShadowOfAutomaticPet2" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="b" name="B" proValue="0.12" showShadowClassName="ShowShadowOfAutomaticPet3" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="a" name="A" proValue="0.17" showShadowClassName="ShowShadowOfAutomaticPet4" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="s" name="S" proValue="0.23" showShadowClassName="ShowShadowOfAutomaticPet5" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <!--<pingJie id="ss" name="SS" proValue="0.30" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>
		 <pingJie id="sss" name="SSS" proValue="0.40" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>-->
	 </pingJieData>
	 
	 
	 <!-- 等级对应属性-->

     <levelData level="1" >
		<data att="totalHp" value="3000" /><!-- 血量 -->
		<data att="totalMp" value="10" /><!-- 魔法 -->
		<data att="attack" value="100" /><!-- 攻击 -->
		<data att="defence" value="50" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0201" /><!-- 闪避 -->
		<data att="criticalRate" value="0.05" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.06" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0101" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="20" /> <!--升级所需经验-->
		<data att="devourExp" value="100" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="2" >
		<data att="totalHp" value="3050" /><!-- 血量 -->
		<data att="totalMp" value="11" /><!-- 魔法 -->
		<data att="attack" value="102" /><!-- 攻击 -->
		<data att="defence" value="51" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0202" /><!-- 闪避 -->
		<data att="criticalRate" value="0.052" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.062" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0102" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="23" /> <!--升级所需经验-->
		<data att="devourExp" value="103" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="3" >
		<data att="totalHp" value="3200" /><!-- 血量 -->
		<data att="totalMp" value="12" /><!-- 魔法 -->
		<data att="attack" value="106" /><!-- 攻击 -->
		<data att="defence" value="53" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0203" /><!-- 闪避 -->
		<data att="criticalRate" value="0.054" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.064" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0103" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="32" /> <!--升级所需经验-->
		<data att="devourExp" value="112" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="4" >
		<data att="totalHp" value="3450" /><!-- 血量 -->
		<data att="totalMp" value="13" /><!-- 魔法 -->
		<data att="attack" value="114" /><!-- 攻击 -->
		<data att="defence" value="57" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0204" /><!-- 闪避 -->
		<data att="criticalRate" value="0.056" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.066" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0104" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="47" /> <!--升级所需经验-->
		<data att="devourExp" value="127" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="5" >
		<data att="totalHp" value="3800" /><!-- 血量 -->
		<data att="totalMp" value="14" /><!-- 魔法 -->
		<data att="attack" value="124" /><!-- 攻击 -->
		<data att="defence" value="63" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0205" /><!-- 闪避 -->
		<data att="criticalRate" value="0.058" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.068" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0105" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="68" /> <!--升级所需经验-->
		<data att="devourExp" value="148" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="6" >
		<data att="totalHp" value="4250" /><!-- 血量 -->
		<data att="totalMp" value="15" /><!-- 魔法 -->
		<data att="attack" value="138" /><!-- 攻击 -->
		<data att="defence" value="70" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0206" /><!-- 闪避 -->
		<data att="criticalRate" value="0.06" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.07" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0106" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="95" /> <!--升级所需经验-->
		<data att="devourExp" value="175" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="7" >
		<data att="totalHp" value="4800" /><!-- 血量 -->
		<data att="totalMp" value="16" /><!-- 魔法 -->
		<data att="attack" value="154" /><!-- 攻击 -->
		<data att="defence" value="79" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0207" /><!-- 闪避 -->
		<data att="criticalRate" value="0.062" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.072" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0107" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="128" /> <!--升级所需经验-->
		<data att="devourExp" value="208" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="8" >
		<data att="totalHp" value="5450" /><!-- 血量 -->
		<data att="totalMp" value="17" /><!-- 魔法 -->
		<data att="attack" value="174" /><!-- 攻击 -->
		<data att="defence" value="89" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0208" /><!-- 闪避 -->
		<data att="criticalRate" value="0.064" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.074" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0108" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="167" /> <!--升级所需经验-->
		<data att="devourExp" value="247" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="9" >
		<data att="totalHp" value="6200" /><!-- 血量 -->
		<data att="totalMp" value="18" /><!-- 魔法 -->
		<data att="attack" value="196" /><!-- 攻击 -->
		<data att="defence" value="101" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0209" /><!-- 闪避 -->
		<data att="criticalRate" value="0.066" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.076" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0109" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="212" /> <!--升级所需经验-->
		<data att="devourExp" value="292" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="10" >
		<data att="totalHp" value="7050" /><!-- 血量 -->
		<data att="totalMp" value="19" /><!-- 魔法 -->
		<data att="attack" value="222" /><!-- 攻击 -->
		<data att="defence" value="115" /><!-- 防御 -->
		<data att="dogdeRate" value="0.021" /><!-- 闪避 -->
		<data att="criticalRate" value="0.068" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.078" /><!-- 防爆率 -->
		<data att="hitRate" value="0.011" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="263" /> <!--升级所需经验-->
		<data att="devourExp" value="343" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="11" >
		<data att="totalHp" value="8000" /><!-- 血量 -->
		<data att="totalMp" value="20" /><!-- 魔法 -->
		<data att="attack" value="250" /><!-- 攻击 -->
		<data att="defence" value="130" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0211" /><!-- 闪避 -->
		<data att="criticalRate" value="0.07" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.08" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0111" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="320" /> <!--升级所需经验-->
		<data att="devourExp" value="400" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="12" >
		<data att="totalHp" value="9050" /><!-- 血量 -->
		<data att="totalMp" value="21" /><!-- 魔法 -->
		<data att="attack" value="282" /><!-- 攻击 -->
		<data att="defence" value="147" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0212" /><!-- 闪避 -->
		<data att="criticalRate" value="0.072" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.082" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0112" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="383" /> <!--升级所需经验-->
		<data att="devourExp" value="463" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="13" >
		<data att="totalHp" value="10200" /><!-- 血量 -->
		<data att="totalMp" value="22" /><!-- 魔法 -->
		<data att="attack" value="316" /><!-- 攻击 -->
		<data att="defence" value="165" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0213" /><!-- 闪避 -->
		<data att="criticalRate" value="0.074" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.084" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0113" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="452" /> <!--升级所需经验-->
		<data att="devourExp" value="532" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="14" >
		<data att="totalHp" value="11450" /><!-- 血量 -->
		<data att="totalMp" value="23" /><!-- 魔法 -->
		<data att="attack" value="354" /><!-- 攻击 -->
		<data att="defence" value="185" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0214" /><!-- 闪避 -->
		<data att="criticalRate" value="0.076" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.086" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0114" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="527" /> <!--升级所需经验-->
		<data att="devourExp" value="607" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="15" >
		<data att="totalHp" value="12800" /><!-- 血量 -->
		<data att="totalMp" value="24" /><!-- 魔法 -->
		<data att="attack" value="394" /><!-- 攻击 -->
		<data att="defence" value="207" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0215" /><!-- 闪避 -->
		<data att="criticalRate" value="0.078" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.088" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0115" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="608" /> <!--升级所需经验-->
		<data att="devourExp" value="688" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="16" >
		<data att="totalHp" value="14250" /><!-- 血量 -->
		<data att="totalMp" value="25" /><!-- 魔法 -->
		<data att="attack" value="438" /><!-- 攻击 -->
		<data att="defence" value="230" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0216" /><!-- 闪避 -->
		<data att="criticalRate" value="0.08" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.09" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0116" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="695" /> <!--升级所需经验-->
		<data att="devourExp" value="775" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="17" >
		<data att="totalHp" value="15800" /><!-- 血量 -->
		<data att="totalMp" value="26" /><!-- 魔法 -->
		<data att="attack" value="484" /><!-- 攻击 -->
		<data att="defence" value="255" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0217" /><!-- 闪避 -->
		<data att="criticalRate" value="0.082" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.092" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0117" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="788" /> <!--升级所需经验-->
		<data att="devourExp" value="868" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="18" >
		<data att="totalHp" value="17450" /><!-- 血量 -->
		<data att="totalMp" value="27" /><!-- 魔法 -->
		<data att="attack" value="534" /><!-- 攻击 -->
		<data att="defence" value="281" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0218" /><!-- 闪避 -->
		<data att="criticalRate" value="0.084" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.094" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0118" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="887" /> <!--升级所需经验-->
		<data att="devourExp" value="967" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="19" >
		<data att="totalHp" value="19200" /><!-- 血量 -->
		<data att="totalMp" value="28" /><!-- 魔法 -->
		<data att="attack" value="586" /><!-- 攻击 -->
		<data att="defence" value="309" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0219" /><!-- 闪避 -->
		<data att="criticalRate" value="0.086" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.096" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0119" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="992" /> <!--升级所需经验-->
		<data att="devourExp" value="1072" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="20" >
		<data att="totalHp" value="21050" /><!-- 血量 -->
		<data att="totalMp" value="29" /><!-- 魔法 -->
		<data att="attack" value="642" /><!-- 攻击 -->
		<data att="defence" value="339" /><!-- 防御 -->
		<data att="dogdeRate" value="0.022" /><!-- 闪避 -->
		<data att="criticalRate" value="0.088" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.098" /><!-- 防爆率 -->
		<data att="hitRate" value="0.012" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1103" /> <!--升级所需经验-->
		<data att="devourExp" value="1072" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="21" >
		<data att="totalHp" value="23000" /><!-- 血量 -->
		<data att="totalMp" value="30" /><!-- 魔法 -->
		<data att="attack" value="700" /><!-- 攻击 -->
		<data att="defence" value="370" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0221" /><!-- 闪避 -->
		<data att="criticalRate" value="0.09" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.1" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0121" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1220" /> <!--升级所需经验-->
		<data att="devourExp" value="1300" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="22" >
		<data att="totalHp" value="25050" /><!-- 血量 -->
		<data att="totalMp" value="31" /><!-- 魔法 -->
		<data att="attack" value="762" /><!-- 攻击 -->
		<data att="defence" value="403" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0222" /><!-- 闪避 -->
		<data att="criticalRate" value="0.092" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.102" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0122" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1343" /> <!--升级所需经验-->
		<data att="devourExp" value="1423" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="23" >
		<data att="totalHp" value="27000" /><!-- 血量 -->
		<data att="totalMp" value="32" /><!-- 魔法 -->
		<data att="attack" value="826" /><!-- 攻击 -->
		<data att="defence" value="437" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0223" /><!-- 闪避 -->
		<data att="criticalRate" value="0.094" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.104" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0123" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1472" /> <!--升级所需经验-->
		<data att="devourExp" value="1552" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="24" >
		<data att="totalHp" value="28000" /><!-- 血量 -->
		<data att="totalMp" value="33" /><!-- 魔法 -->
		<data att="attack" value="894" /><!-- 攻击 -->
		<data att="defence" value="473" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0224" /><!-- 闪避 -->
		<data att="criticalRate" value="0.096" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.106" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0124" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1607" /> <!--升级所需经验-->
		<data att="devourExp" value="1552" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="25" >
		<data att="totalHp" value="30000" /><!-- 血量 -->
		<data att="totalMp" value="34" /><!-- 魔法 -->
		<data att="attack" value="964" /><!-- 攻击 -->
		<data att="defence" value="511" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0225" /><!-- 闪避 -->
		<data att="criticalRate" value="0.098" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.108" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0125" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1748" /> <!--升级所需经验-->
		<data att="devourExp" value="1828" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="26" >
		<data att="totalHp" value="33000" /><!-- 血量 -->
		<data att="totalMp" value="35" /><!-- 魔法 -->
		<data att="attack" value="1038" /><!-- 攻击 -->
		<data att="defence" value="550" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0226" /><!-- 闪避 -->
		<data att="criticalRate" value="0.1" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.11" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0126" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1895" /> <!--升级所需经验-->
		<data att="devourExp" value="1975" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="27" >
		<data att="totalHp" value="36000" /><!-- 血量 -->
		<data att="totalMp" value="36" /><!-- 魔法 -->
		<data att="attack" value="1114" /><!-- 攻击 -->
		<data att="defence" value="591" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0227" /><!-- 闪避 -->
		<data att="criticalRate" value="0.102" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.112" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0127" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2048" /> <!--升级所需经验-->
		<data att="devourExp" value="2128" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="28" >
		<data att="totalHp" value="39000" /><!-- 血量 -->
		<data att="totalMp" value="37" /><!-- 魔法 -->
		<data att="attack" value="1194" /><!-- 攻击 -->
		<data att="defence" value="633" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0228" /><!-- 闪避 -->
		<data att="criticalRate" value="0.104" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.114" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0128" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2207" /> <!--升级所需经验-->
		<data att="devourExp" value="2287" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="29" >
		<data att="totalHp" value="42000" /><!-- 血量 -->
		<data att="totalMp" value="38" /><!-- 魔法 -->
		<data att="attack" value="1276" /><!-- 攻击 -->
		<data att="defence" value="677" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0229" /><!-- 闪避 -->
		<data att="criticalRate" value="0.106" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.116" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0129" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2372" /> <!--升级所需经验-->
		<data att="devourExp" value="2452" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="30" >
		<data att="totalHp" value="45050" /><!-- 血量 -->
		<data att="totalMp" value="39" /><!-- 魔法 -->
		<data att="attack" value="1362" /><!-- 攻击 -->
		<data att="defence" value="723" /><!-- 防御 -->
		<data att="dogdeRate" value="0.023" /><!-- 闪避 -->
		<data att="criticalRate" value="0.108" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.118" /><!-- 防爆率 -->
		<data att="hitRate" value="0.013" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2543" /> <!--升级所需经验-->
		<data att="devourExp" value="2623" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
  
  
  
 </automaticPet>
 
   <!-- 龟丞相-->
 <automaticPet id="tortoise" name="龟丞相" logicPartPetXMLPath="NewGameFolder/Pets2/tortoise.xml" 
  attackSkillId="tortoiseAttack" showSwfPath="NewGameFolder/AutomaticPetSource/Tortoise.swf" showClassName="ShowOfTortoise">
	 <initSkillsData >
		 <activeSkill id="tortoiseAttack" level="1"  />
		 <!--<activeSkill id="" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" />-->
	 </initSkillsData>
     <pingJieData defaultId="d" showShadowSwfPath="NewGameFolder/AutomaticPetSource/Shadow.swf">
		 <pingJie id="d" name="D" proValue="0" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1.3" y_scale="1.3" x_offset="" y_offset="-5"/>
		 <pingJie id="c" name="C" proValue="0.1" showShadowClassName="ShowShadowOfAutomaticPet2" x_scale="1.3" y_scale="1.3" x_offset="" y_offset="-5" />
		 <pingJie id="b" name="B" proValue="0.15" showShadowClassName="ShowShadowOfAutomaticPet3" x_scale="1.3" y_scale="1.3" x_offset="" y_offset="-5" />
		 <pingJie id="a" name="A" proValue="0.20" showShadowClassName="ShowShadowOfAutomaticPet4" x_scale="1.3" y_scale="1.3" x_offset="" y_offset="-5" />
		 <pingJie id="s" name="S" proValue="0.25" showShadowClassName="ShowShadowOfAutomaticPet5" x_scale="1.3" y_scale="1.3" x_offset="" y_offset="-5" />
		 <!--<pingJie id="ss" name="SS" proValue="0.30" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>
		 <pingJie id="sss" name="SSS" proValue="0.40" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>-->
	 </pingJieData>
	 
	 
	 <!-- 等级对应属性-->
     <levelData level="1" >
		<data att="totalHp" value="2000" /><!-- 血量 -->
		<data att="totalMp" value="10" /><!-- 魔法 -->
		<data att="attack" value="150" /><!-- 攻击 -->
		<data att="defence" value="20" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0201" /><!-- 闪避 -->
		<data att="criticalRate" value="0.05" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.06" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0101" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="20" /> <!--升级所需经验-->
		<data att="devourExp" value="100" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="2" >
		<data att="totalHp" value="2050" /><!-- 血量 -->
		<data att="totalMp" value="11" /><!-- 魔法 -->
		<data att="attack" value="152" /><!-- 攻击 -->
		<data att="defence" value="21" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0202" /><!-- 闪避 -->
		<data att="criticalRate" value="0.052" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.062" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0102" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="23" /> <!--升级所需经验-->
		<data att="devourExp" value="103" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="3" >
		<data att="totalHp" value="2200" /><!-- 血量 -->
		<data att="totalMp" value="12" /><!-- 魔法 -->
		<data att="attack" value="158" /><!-- 攻击 -->
		<data att="defence" value="22" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0203" /><!-- 闪避 -->
		<data att="criticalRate" value="0.054" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.064" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0103" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="32" /> <!--升级所需经验-->
		<data att="devourExp" value="112" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="4" >
		<data att="totalHp" value="2450" /><!-- 血量 -->
		<data att="totalMp" value="13" /><!-- 魔法 -->
		<data att="attack" value="168" /><!-- 攻击 -->
		<data att="defence" value="25" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0204" /><!-- 闪避 -->
		<data att="criticalRate" value="0.056" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.066" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0104" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="47" /> <!--升级所需经验-->
		<data att="devourExp" value="127" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="5" >
		<data att="totalHp" value="2800" /><!-- 血量 -->
		<data att="totalMp" value="14" /><!-- 魔法 -->
		<data att="attack" value="182" /><!-- 攻击 -->
		<data att="defence" value="30" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0205" /><!-- 闪避 -->
		<data att="criticalRate" value="0.058" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.068" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0105" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="68" /> <!--升级所需经验-->
		<data att="devourExp" value="148" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="6" >
		<data att="totalHp" value="3250" /><!-- 血量 -->
		<data att="totalMp" value="15" /><!-- 魔法 -->
		<data att="attack" value="200" /><!-- 攻击 -->
		<data att="defence" value="35" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0206" /><!-- 闪避 -->
		<data att="criticalRate" value="0.06" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.07" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0106" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="95" /> <!--升级所需经验-->
		<data att="devourExp" value="175" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="7" >
		<data att="totalHp" value="3800" /><!-- 血量 -->
		<data att="totalMp" value="16" /><!-- 魔法 -->
		<data att="attack" value="222" /><!-- 攻击 -->
		<data att="defence" value="42" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0207" /><!-- 闪避 -->
		<data att="criticalRate" value="0.062" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.072" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0107" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="128" /> <!--升级所需经验-->
		<data att="devourExp" value="208" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="8" >
		<data att="totalHp" value="4450" /><!-- 血量 -->
		<data att="totalMp" value="17" /><!-- 魔法 -->
		<data att="attack" value="248" /><!-- 攻击 -->
		<data att="defence" value="49" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0208" /><!-- 闪避 -->
		<data att="criticalRate" value="0.064" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.074" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0108" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="167" /> <!--升级所需经验-->
		<data att="devourExp" value="247" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="9" >
		<data att="totalHp" value="5200" /><!-- 血量 -->
		<data att="totalMp" value="18" /><!-- 魔法 -->
		<data att="attack" value="278" /><!-- 攻击 -->
		<data att="defence" value="58" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0209" /><!-- 闪避 -->
		<data att="criticalRate" value="0.066" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.076" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0109" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="212" /> <!--升级所需经验-->
		<data att="devourExp" value="292" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="10" >
		<data att="totalHp" value="6050" /><!-- 血量 -->
		<data att="totalMp" value="19" /><!-- 魔法 -->
		<data att="attack" value="312" /><!-- 攻击 -->
		<data att="defence" value="69" /><!-- 防御 -->
		<data att="dogdeRate" value="0.021" /><!-- 闪避 -->
		<data att="criticalRate" value="0.068" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.078" /><!-- 防爆率 -->
		<data att="hitRate" value="0.011" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="263" /> <!--升级所需经验-->
		<data att="devourExp" value="343" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="11" >
		<data att="totalHp" value="7000" /><!-- 血量 -->
		<data att="totalMp" value="20" /><!-- 魔法 -->
		<data att="attack" value="350" /><!-- 攻击 -->
		<data att="defence" value="80" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0211" /><!-- 闪避 -->
		<data att="criticalRate" value="0.07" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.08" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0111" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="320" /> <!--升级所需经验-->
		<data att="devourExp" value="400" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="12" >
		<data att="totalHp" value="8050" /><!-- 血量 -->
		<data att="totalMp" value="21" /><!-- 魔法 -->
		<data att="attack" value="392" /><!-- 攻击 -->
		<data att="defence" value="93" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0212" /><!-- 闪避 -->
		<data att="criticalRate" value="0.072" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.082" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0112" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="383" /> <!--升级所需经验-->
		<data att="devourExp" value="463" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="13" >
		<data att="totalHp" value="9200" /><!-- 血量 -->
		<data att="totalMp" value="22" /><!-- 魔法 -->
		<data att="attack" value="438" /><!-- 攻击 -->
		<data att="defence" value="106" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0213" /><!-- 闪避 -->
		<data att="criticalRate" value="0.074" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.084" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0113" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="452" /> <!--升级所需经验-->
		<data att="devourExp" value="532" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="14" >
		<data att="totalHp" value="10450" /><!-- 血量 -->
		<data att="totalMp" value="23" /><!-- 魔法 -->
		<data att="attack" value="488" /><!-- 攻击 -->
		<data att="defence" value="121" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0214" /><!-- 闪避 -->
		<data att="criticalRate" value="0.076" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.086" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0114" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="527" /> <!--升级所需经验-->
		<data att="devourExp" value="607" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="15" >
		<data att="totalHp" value="11800" /><!-- 血量 -->
		<data att="totalMp" value="24" /><!-- 魔法 -->
		<data att="attack" value="542" /><!-- 攻击 -->
		<data att="defence" value="138" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0215" /><!-- 闪避 -->
		<data att="criticalRate" value="0.078" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.088" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0115" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="608" /> <!--升级所需经验-->
		<data att="devourExp" value="688" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="16" >
		<data att="totalHp" value="13250" /><!-- 血量 -->
		<data att="totalMp" value="25" /><!-- 魔法 -->
		<data att="attack" value="600" /><!-- 攻击 -->
		<data att="defence" value="155" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0216" /><!-- 闪避 -->
		<data att="criticalRate" value="0.08" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.09" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0116" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="695" /> <!--升级所需经验-->
		<data att="devourExp" value="775" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="17" >
		<data att="totalHp" value="14800" /><!-- 血量 -->
		<data att="totalMp" value="26" /><!-- 魔法 -->
		<data att="attack" value="662" /><!-- 攻击 -->
		<data att="defence" value="174" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0217" /><!-- 闪避 -->
		<data att="criticalRate" value="0.082" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.092" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0117" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="788" /> <!--升级所需经验-->
		<data att="devourExp" value="868" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="18" >
		<data att="totalHp" value="16450" /><!-- 血量 -->
		<data att="totalMp" value="27" /><!-- 魔法 -->
		<data att="attack" value="728" /><!-- 攻击 -->
		<data att="defence" value="193" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0218" /><!-- 闪避 -->
		<data att="criticalRate" value="0.084" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.094" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0118" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="887" /> <!--升级所需经验-->
		<data att="devourExp" value="967" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="19" >
		<data att="totalHp" value="18200" /><!-- 血量 -->
		<data att="totalMp" value="28" /><!-- 魔法 -->
		<data att="attack" value="798" /><!-- 攻击 -->
		<data att="defence" value="224" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0219" /><!-- 闪避 -->
		<data att="criticalRate" value="0.086" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.096" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0119" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="992" /> <!--升级所需经验-->
		<data att="devourExp" value="1072" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="20" >
		<data att="totalHp" value="20050" /><!-- 血量 -->
		<data att="totalMp" value="29" /><!-- 魔法 -->
		<data att="attack" value="872" /><!-- 攻击 -->
		<data att="defence" value="237" /><!-- 防御 -->
		<data att="dogdeRate" value="0.022" /><!-- 闪避 -->
		<data att="criticalRate" value="0.088" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.098" /><!-- 防爆率 -->
		<data att="hitRate" value="0.012" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1103" /> <!--升级所需经验-->
		<data att="devourExp" value="1072" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="21" >
		<data att="totalHp" value="22000" /><!-- 血量 -->
		<data att="totalMp" value="30" /><!-- 魔法 -->
		<data att="attack" value="950" /><!-- 攻击 -->
		<data att="defence" value="260" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0221" /><!-- 闪避 -->
		<data att="criticalRate" value="0.09" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.1" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0121" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1220" /> <!--升级所需经验-->
		<data att="devourExp" value="1300" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="22" >
		<data att="totalHp" value="24050" /><!-- 血量 -->
		<data att="totalMp" value="31" /><!-- 魔法 -->
		<data att="attack" value="1032" /><!-- 攻击 -->
		<data att="defence" value="285" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0222" /><!-- 闪避 -->
		<data att="criticalRate" value="0.092" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.102" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0122" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1343" /> <!--升级所需经验-->
		<data att="devourExp" value="1423" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="23" >
		<data att="totalHp" value="26200" /><!-- 血量 -->
		<data att="totalMp" value="32" /><!-- 魔法 -->
		<data att="attack" value="1118" /><!-- 攻击 -->
		<data att="defence" value="310" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0223" /><!-- 闪避 -->
		<data att="criticalRate" value="0.094" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.104" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0123" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1472" /> <!--升级所需经验-->
		<data att="devourExp" value="1552" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="24" >
		<data att="totalHp" value="28450" /><!-- 血量 -->
		<data att="totalMp" value="33" /><!-- 魔法 -->
		<data att="attack" value="1208" /><!-- 攻击 -->
		<data att="defence" value="337" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0224" /><!-- 闪避 -->
		<data att="criticalRate" value="0.096" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.106" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0124" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1607" /> <!--升级所需经验-->
		<data att="devourExp" value="1552" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="25" >
		<data att="totalHp" value="30800" /><!-- 血量 -->
		<data att="totalMp" value="34" /><!-- 魔法 -->
		<data att="attack" value="1302" /><!-- 攻击 -->
		<data att="defence" value="366" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0225" /><!-- 闪避 -->
		<data att="criticalRate" value="0.098" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.108" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0125" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1748" /> <!--升级所需经验-->
		<data att="devourExp" value="1828" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="26" >
		<data att="totalHp" value="33250" /><!-- 血量 -->
		<data att="totalMp" value="35" /><!-- 魔法 -->
		<data att="attack" value="1402" /><!-- 攻击 -->
		<data att="defence" value="395" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0226" /><!-- 闪避 -->
		<data att="criticalRate" value="0.1" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.11" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0126" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1895" /> <!--升级所需经验-->
		<data att="devourExp" value="1975" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="27" >
		<data att="totalHp" value="35800" /><!-- 血量 -->
		<data att="totalMp" value="36" /><!-- 魔法 -->
		<data att="attack" value="1502" /><!-- 攻击 -->
		<data att="defence" value="426" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0227" /><!-- 闪避 -->
		<data att="criticalRate" value="0.102" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.112" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0127" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2048" /> <!--升级所需经验-->
		<data att="devourExp" value="2128" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="28" >
		<data att="totalHp" value="38450" /><!-- 血量 -->
		<data att="totalMp" value="37" /><!-- 魔法 -->
		<data att="attack" value="1608" /><!-- 攻击 -->
		<data att="defence" value="457" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0228" /><!-- 闪避 -->
		<data att="criticalRate" value="0.104" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.114" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0128" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2207" /> <!--升级所需经验-->
		<data att="devourExp" value="2287" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="29" >
		<data att="totalHp" value="41200" /><!-- 血量 -->
		<data att="totalMp" value="38" /><!-- 魔法 -->
		<data att="attack" value="1718" /><!-- 攻击 -->
		<data att="defence" value="490" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0229" /><!-- 闪避 -->
		<data att="criticalRate" value="0.106" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.116" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0129" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2372" /> <!--升级所需经验-->
		<data att="devourExp" value="2452" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="30" >
		<data att="totalHp" value="44050" /><!-- 血量 -->
		<data att="totalMp" value="39" /><!-- 魔法 -->
		<data att="attack" value="1832" /><!-- 攻击 -->
		<data att="defence" value="525" /><!-- 防御 -->
		<data att="dogdeRate" value="0.023" /><!-- 闪避 -->
		<data att="criticalRate" value="0.108" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.118" /><!-- 防爆率 -->
		<data att="hitRate" value="0.013" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2543" /> <!--升级所需经验-->
		<data att="devourExp" value="2623" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
   
 </automaticPet>
 
  <!-- 鲛人-->
 <automaticPet id="jiaoRen" name="鲛人将军" logicPartPetXMLPath="NewGameFolder/Pets2/jiaoRen.xml" 
  attackSkillId="jiaoRenAttack" showSwfPath="NewGameFolder/AutomaticPetSource/JiaoRen.swf" showClassName="ShowOfJiaoRen">
	 <initSkillsData >
		 <activeSkill id="jiaoRenAttack" level="1" />
		 <activeSkill id="jiaoRenSkill1" level="1"  />
		 <!--<activeSkill id="" className="YJFY.AutomaticPet.AutomaticSkillVO.AutomaticPetAttackSkillVO" />-->
	 </initSkillsData>
     <pingJieData defaultId="d" showShadowSwfPath="NewGameFolder/AutomaticPetSource/Shadow.swf">
		 <pingJie id="d" name="D" proValue="0" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"  x_offset="" y_offset=""/>
		 <pingJie id="c" name="C" proValue="0.1" showShadowClassName="ShowShadowOfAutomaticPet2" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="b" name="B" proValue="0.15" showShadowClassName="ShowShadowOfAutomaticPet3" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="a" name="A" proValue="0.20" showShadowClassName="ShowShadowOfAutomaticPet4" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		 <pingJie id="s" name="S" proValue="0.25" showShadowClassName="ShowShadowOfAutomaticPet5" x_scale="1" y_scale="1" x_offset="" y_offset=""/>
		<!-- <pingJie id="ss" name="SS" proValue="0.30" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>
		 <pingJie id="sss" name="SSS" proValue="0.40" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>-->
	 </pingJieData>
	 
	 <!-- 等级对应属性-->
	 <levelData level="1" >
		<data att="totalHp" value="5000" /><!-- 血量 -->
		<data att="totalMp" value="20" /><!-- 魔法 -->
		<data att="attack" value="100" /><!-- 攻击 -->
		<data att="defence" value="50" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0301" /><!-- 闪避 -->
		<data att="criticalRate" value="0.1" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.08" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0201" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="30" /> <!--升级所需经验-->
		<data att="devourExp" value="100" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="2" >
		<data att="totalHp" value="5100" /><!-- 血量 -->
		<data att="totalMp" value="21" /><!-- 魔法 -->
		<data att="attack" value="102" /><!-- 攻击 -->
		<data att="defence" value="52" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0302" /><!-- 闪避 -->
		<data att="criticalRate" value="0.12" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.1" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0202" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="34" /> <!--升级所需经验-->
		<data att="devourExp" value="104" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="3" >
		<data att="totalHp" value="5400" /><!-- 血量 -->
		<data att="totalMp" value="22" /><!-- 魔法 -->
		<data att="attack" value="108" /><!-- 攻击 -->
		<data att="defence" value="56" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0303" /><!-- 闪避 -->
		<data att="criticalRate" value="0.14" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.12" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0203" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="44" /> <!--升级所需经验-->
		<data att="devourExp" value="116" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="4" >
		<data att="totalHp" value="5900" /><!-- 血量 -->
		<data att="totalMp" value="23" /><!-- 魔法 -->
		<data att="attack" value="118" /><!-- 攻击 -->
		<data att="defence" value="64" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0304" /><!-- 闪避 -->
		<data att="criticalRate" value="0.16" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.14" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0204" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="62" /> <!--升级所需经验-->
		<data att="devourExp" value="136" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="5" >
		<data att="totalHp" value="6600" /><!-- 血量 -->
		<data att="totalMp" value="24" /><!-- 魔法 -->
		<data att="attack" value="132" /><!-- 攻击 -->
		<data att="defence" value="74" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0305" /><!-- 闪避 -->
		<data att="criticalRate" value="0.18" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.16" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0205" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="86" /> <!--升级所需经验-->
		<data att="devourExp" value="164" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="6" >
		<data att="totalHp" value="7500" /><!-- 血量 -->
		<data att="totalMp" value="25" /><!-- 魔法 -->
		<data att="attack" value="150" /><!-- 攻击 -->
		<data att="defence" value="88" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0306" /><!-- 闪避 -->
		<data att="criticalRate" value="0.2" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.18" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0206" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="118" /> <!--升级所需经验-->
		<data att="devourExp" value="200" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="7" >
		<data att="totalHp" value="8600" /><!-- 血量 -->
		<data att="totalMp" value="26" /><!-- 魔法 -->
		<data att="attack" value="172" /><!-- 攻击 -->
		<data att="defence" value="104" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0307" /><!-- 闪避 -->
		<data att="criticalRate" value="0.22" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.2" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0207" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="156" /> <!--升级所需经验-->
		<data att="devourExp" value="244" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="8" >
		<data att="totalHp" value="9900" /><!-- 血量 -->
		<data att="totalMp" value="27" /><!-- 魔法 -->
		<data att="attack" value="198" /><!-- 攻击 -->
		<data att="defence" value="124" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0308" /><!-- 闪避 -->
		<data att="criticalRate" value="0.24" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.22" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0208" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="202" /> <!--升级所需经验-->
		<data att="devourExp" value="296" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="9" >
		<data att="totalHp" value="11400" /><!-- 血量 -->
		<data att="totalMp" value="28" /><!-- 魔法 -->
		<data att="attack" value="228" /><!-- 攻击 -->
		<data att="defence" value="146" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0309" /><!-- 闪避 -->
		<data att="criticalRate" value="0.26" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.24" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0209" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="254" /> <!--升级所需经验-->
		<data att="devourExp" value="256" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="10" >
		<data att="totalHp" value="13100" /><!-- 血量 -->
		<data att="totalMp" value="29" /><!-- 魔法 -->
		<data att="attack" value="262" /><!-- 攻击 -->
		<data att="defence" value="172" /><!-- 防御 -->
		<data att="dogdeRate" value="0.031" /><!-- 闪避 -->
		<data att="criticalRate" value="0.28" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.26" /><!-- 防爆率 -->
		<data att="hitRate" value="0.021" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="314" /> <!--升级所需经验-->
		<data att="devourExp" value="424" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="11" >
		<data att="totalHp" value="15000" /><!-- 血量 -->
		<data att="totalMp" value="30" /><!-- 魔法 -->
		<data att="attack" value="300" /><!-- 攻击 -->
		<data att="defence" value="200" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0311" /><!-- 闪避 -->
		<data att="criticalRate" value="0.3" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.28" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0211" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="380" /> <!--升级所需经验-->
		<data att="devourExp" value="500" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="12" >
		<data att="totalHp" value="17100" /><!-- 血量 -->
		<data att="totalMp" value="31" /><!-- 魔法 -->
		<data att="attack" value="342" /><!-- 攻击 -->
		<data att="defence" value="232" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0312" /><!-- 闪避 -->
		<data att="criticalRate" value="0.32" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.3" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0212" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="454" /> <!--升级所需经验-->
		<data att="devourExp" value="584" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="13" >
		<data att="totalHp" value="19400" /><!-- 血量 -->
		<data att="totalMp" value="32" /><!-- 魔法 -->
		<data att="attack" value="388" /><!-- 攻击 -->
		<data att="defence" value="266" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0313" /><!-- 闪避 -->
		<data att="criticalRate" value="0.34" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.32" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0213" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="534" /> <!--升级所需经验-->
		<data att="devourExp" value="676" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="14" >
		<data att="totalHp" value="21900" /><!-- 血量 -->
		<data att="totalMp" value="33" /><!-- 魔法 -->
		<data att="attack" value="438" /><!-- 攻击 -->
		<data att="defence" value="304" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0314" /><!-- 闪避 -->
		<data att="criticalRate" value="0.36" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.34" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0214" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="622" /> <!--升级所需经验-->
		<data att="devourExp" value="776" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="15" >
		<data att="totalHp" value="24600" /><!-- 血量 -->
		<data att="totalMp" value="34" /><!-- 魔法 -->
		<data att="attack" value="492" /><!-- 攻击 -->
		<data att="defence" value="344" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0315" /><!-- 闪避 -->
		<data att="criticalRate" value="0.38" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.36" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0215" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="716" /> <!--升级所需经验-->
		<data att="devourExp" value="884" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="16" >
		<data att="totalHp" value="27500" /><!-- 血量 -->
		<data att="totalMp" value="35" /><!-- 魔法 -->
		<data att="attack" value="550" /><!-- 攻击 -->
		<data att="defence" value="388" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0316" /><!-- 闪避 -->
		<data att="criticalRate" value="0.4" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.38" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0216" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="818" /> <!--升级所需经验-->
		<data att="devourExp" value="1000" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="17" >
		<data att="totalHp" value="30600" /><!-- 血量 -->
		<data att="totalMp" value="36" /><!-- 魔法 -->
		<data att="attack" value="612" /><!-- 攻击 -->
		<data att="defence" value="434" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0317" /><!-- 闪避 -->
		<data att="criticalRate" value="0.42" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.4" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0217" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="926" /> <!--升级所需经验-->
		<data att="devourExp" value="1124" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="18" >
		<data att="totalHp" value="33900" /><!-- 血量 -->
		<data att="totalMp" value="37" /><!-- 魔法 -->
		<data att="attack" value="678" /><!-- 攻击 -->
		<data att="defence" value="434" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0318" /><!-- 闪避 -->
		<data att="criticalRate" value="0.44" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.42" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0218" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1042" /> <!--升级所需经验-->
		<data att="devourExp" value="1256" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="19" >
		<data att="totalHp" value="37400" /><!-- 血量 -->
		<data att="totalMp" value="38" /><!-- 魔法 -->
		<data att="attack" value="748" /><!-- 攻击 -->
		<data att="defence" value="536" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0319" /><!-- 闪避 -->
		<data att="criticalRate" value="0.46" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.44" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0219" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1162" /> <!--升级所需经验-->
		<data att="devourExp" value="1396" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="20" >
		<data att="totalHp" value="41100" /><!-- 血量 -->
		<data att="totalMp" value="39" /><!-- 魔法 -->
		<data att="attack" value="822" /><!-- 攻击 -->
		<data att="defence" value="592" /><!-- 防御 -->
		<data att="dogdeRate" value="0.032" /><!-- 闪避 -->
		<data att="criticalRate" value="0.48" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.46" /><!-- 防爆率 -->
		<data att="hitRate" value="0.022" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1294" /> <!--升级所需经验-->
		<data att="devourExp" value="1544" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="21" >
		<data att="totalHp" value="45000" /><!-- 血量 -->
		<data att="totalMp" value="40" /><!-- 魔法 -->
		<data att="attack" value="900" /><!-- 攻击 -->
		<data att="defence" value="650" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0321" /><!-- 闪避 -->
		<data att="criticalRate" value="0.5" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.48" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0221" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1430" /> <!--升级所需经验-->
		<data att="devourExp" value="1700" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	
	<levelData level="22" >
		<data att="totalHp" value="49100" /><!-- 血量 -->
		<data att="totalMp" value="41" /><!-- 魔法 -->
		<data att="attack" value="982" /><!-- 攻击 -->
		<data att="defence" value="712" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0322" /><!-- 闪避 -->
		<data att="criticalRate" value="0.52" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.5" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0222" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1574" /> <!--升级所需经验-->
		<data att="devourExp" value="1864" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="23" >
		<data att="totalHp" value="53400" /><!-- 血量 -->
		<data att="totalMp" value="42" /><!-- 魔法 -->
		<data att="attack" value="1068" /><!-- 攻击 -->
		<data att="defence" value="776" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0323" /><!-- 闪避 -->
		<data att="criticalRate" value="0.54" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.52" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0223" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1724" /> <!--升级所需经验-->
		<data att="devourExp" value="2036" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="24" >
		<data att="totalHp" value="57900" /><!-- 血量 -->
		<data att="totalMp" value="43" /><!-- 魔法 -->
		<data att="attack" value="1158" /><!-- 攻击 -->
		<data att="defence" value="844" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0324" /><!-- 闪避 -->
		<data att="criticalRate" value="0.56" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.54" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0224" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1882" /> <!--升级所需经验-->
		<data att="devourExp" value="2216" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="25" >
		<data att="totalHp" value="62600" /><!-- 血量 -->
		<data att="totalMp" value="44" /><!-- 魔法 -->
		<data att="attack" value="1252" /><!-- 攻击 -->
		<data att="defence" value="914" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0325" /><!-- 闪避 -->
		<data att="criticalRate" value="0.58" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.56" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0225" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2046" /> <!--升级所需经验-->
		<data att="devourExp" value="2404" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="26" >
		<data att="totalHp" value="67500" /><!-- 血量 -->
		<data att="totalMp" value="45" /><!-- 魔法 -->
		<data att="attack" value="1350" /><!-- 攻击 -->
		<data att="defence" value="988" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0326" /><!-- 闪避 -->
		<data att="criticalRate" value="0.6" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.58" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0226" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2218" /> <!--升级所需经验-->
		<data att="devourExp" value="2600" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="27" >
		<data att="totalHp" value="72600" /><!-- 血量 -->
		<data att="totalMp" value="46" /><!-- 魔法 -->
		<data att="attack" value="1452" /><!-- 攻击 -->
		<data att="defence" value="1064" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0327" /><!-- 闪避 -->
		<data att="criticalRate" value="0.62" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.6" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0227" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2396" /> <!--升级所需经验-->
		<data att="devourExp" value="2804" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="28" >
		<data att="totalHp" value="77900" /><!-- 血量 -->
		<data att="totalMp" value="47" /><!-- 魔法 -->
		<data att="attack" value="1558" /><!-- 攻击 -->
		<data att="defence" value="1144" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0328" /><!-- 闪避 -->
		<data att="criticalRate" value="0.64" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.62" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0228" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2582" /> <!--升级所需经验-->
		<data att="devourExp" value="3016" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="29" >
		<data att="totalHp" value="83400" /><!-- 血量 -->
		<data att="totalMp" value="48" /><!-- 魔法 -->
		<data att="attack" value="1668" /><!-- 攻击 -->
		<data att="defence" value="1226" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0329" /><!-- 闪避 -->
		<data att="criticalRate" value="0.66" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.64" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0229" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2774" /> <!--升级所需经验-->
		<data att="devourExp" value="3236" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="30" >
		<data att="totalHp" value="89100" /><!-- 血量 -->
		<data att="totalMp" value="49" /><!-- 魔法 -->
		<data att="attack" value="1782" /><!-- 攻击 -->
		<data att="defence" value="1312" /><!-- 防御 -->
		<data att="dogdeRate" value="0.033" /><!-- 闪避 -->
		<data att="criticalRate" value="0.68" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.66" /><!-- 防爆率 -->
		<data att="hitRate" value="0.023" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="3" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2974" /> <!--升级所需经验-->
		<data att="devourExp" value="3464" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
     
   
   
   
 </automaticPet>
 
  <!-- 龙王-->
 <automaticPet id="dragonKingPet" name="龙王" logicPartPetXMLPath="NewGameFolder/Pets2/dragonKing.xml" 
  attackSkillId="dragonKingAttack" showSwfPath="NewGameFolder/AutomaticPetSource/DragonKing.swf" showClassName="ShowOfDragonKing">
	 <initSkillsData >
		 <activeSkill id="dragonKingAttack" level="1"  />
		 <activeSkill id="dragonKingSkill1" level="1"  />
		 <activeSkill id="dragonKingSkill2" level="1"  />
	 </initSkillsData>
     <pingJieData defaultId="d" showShadowSwfPath="NewGameFolder/AutomaticPetSource/Shadow.swf">
		 <pingJie id="d" name="D" proValue="0" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1.5" y_scale="1.5"  x_offset="-10" y_offset="-5"/>
		 <pingJie id="c" name="C" proValue="0.1" showShadowClassName="ShowShadowOfAutomaticPet2" x_scale="1.5" y_scale="1.5" x_offset="-10" y_offset="-5"/>
		 <pingJie id="b" name="B" proValue="0.15" showShadowClassName="ShowShadowOfAutomaticPet3" x_scale="1.5" y_scale="1.5" x_offset="-10" y_offset="-5"/>
		 <pingJie id="a" name="A" proValue="0.20" showShadowClassName="ShowShadowOfAutomaticPet4" x_scale="1.5" y_scale="1.5" x_offset="-10" y_offset="-5"/>
		 <pingJie id="s" name="S" proValue="0.25" showShadowClassName="ShowShadowOfAutomaticPet5" x_scale="1.5" y_scale="1.5" x_offset="-10" y_offset="-5"/>
		<!-- <pingJie id="ss" name="SS" proValue="0.30" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>
		 <pingJie id="sss" name="SSS" proValue="0.40" showShadowClassName="ShowShadowOfAutomaticPet1" x_scale="1" y_scale="1"/>-->
	 </pingJieData>
	 
	 <!-- 等级对应属性-->
    <levelData level="1" >
		<data att="totalHp" value="4000" /><!-- 血量 -->
		<data att="totalMp" value="20" /><!-- 魔法 -->
		<data att="attack" value="300" /><!-- 攻击 -->
		<data att="defence" value="50" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0101" /><!-- 闪避 -->
		<data att="criticalRate" value="0.1" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.08" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0101" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="100" /> <!--升级所需经验-->
		<data att="devourExp" value="200" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="2" >
		<data att="totalHp" value="4090" /><!-- 血量 -->
		<data att="totalMp" value="22" /><!-- 魔法 -->
		<data att="attack" value="303" /><!-- 攻击 -->
		<data att="defence" value="51" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0102" /><!-- 闪避 -->
		<data att="criticalRate" value="0.12" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.1" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0102" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="104" /> <!--升级所需经验-->
		<data att="devourExp" value="206" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="3" >
		<data att="totalHp" value="4360" /><!-- 血量 -->
		<data att="totalMp" value="24" /><!-- 魔法 -->
		<data att="attack" value="312" /><!-- 攻击 -->
		<data att="defence" value="54" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0103" /><!-- 闪避 -->
		<data att="criticalRate" value="0.14" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.12" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0103" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="116" /> <!--升级所需经验-->
		<data att="devourExp" value="224" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="4" >
		<data att="totalHp" value="4810" /><!-- 血量 -->
		<data att="totalMp" value="26" /><!-- 魔法 -->
		<data att="attack" value="327" /><!-- 攻击 -->
		<data att="defence" value="64" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0104" /><!-- 闪避 -->
		<data att="criticalRate" value="0.16" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.14" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0104" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="136" /> <!--升级所需经验-->
		<data att="devourExp" value="254" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="5" >
		<data att="totalHp" value="5440" /><!-- 血量 -->
		<data att="totalMp" value="28" /><!-- 魔法 -->
		<data att="attack" value="348" /><!-- 攻击 -->
		<data att="defence" value="66" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0105" /><!-- 闪避 -->
		<data att="criticalRate" value="0.18" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.16" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0105" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="164" /> <!--升级所需经验-->
		<data att="devourExp" value="296" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="6" >
		<data att="totalHp" value="6250" /><!-- 血量 -->
		<data att="totalMp" value="30" /><!-- 魔法 -->
		<data att="attack" value="375" /><!-- 攻击 -->
		<data att="defence" value="75" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0106" /><!-- 闪避 -->
		<data att="criticalRate" value="0.2" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.18" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0106" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="200" /> <!--升级所需经验-->
		<data att="devourExp" value="350" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="7" >
		<data att="totalHp" value="7240" /><!-- 血量 -->
		<data att="totalMp" value="32" /><!-- 魔法 -->
		<data att="attack" value="408" /><!-- 攻击 -->
		<data att="defence" value="86" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0107" /><!-- 闪避 -->
		<data att="criticalRate" value="0.22" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.2" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0107" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="244" /> <!--升级所需经验-->
		<data att="devourExp" value="416" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="8" >
		<data att="totalHp" value="8410" /><!-- 血量 -->
		<data att="totalMp" value="34" /><!-- 魔法 -->
		<data att="attack" value="447" /><!-- 攻击 -->
		<data att="defence" value="99" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0108" /><!-- 闪避 -->
		<data att="criticalRate" value="0.24" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.22" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0108" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="296" /> <!--升级所需经验-->
		<data att="devourExp" value="494" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="9" >
		<data att="totalHp" value="9760" /><!-- 血量 -->
		<data att="totalMp" value="36" /><!-- 魔法 -->
		<data att="attack" value="492" /><!-- 攻击 -->
		<data att="defence" value="114" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0109" /><!-- 闪避 -->
		<data att="criticalRate" value="0.26" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.24" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0109" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="356" /> <!--升级所需经验-->
		<data att="devourExp" value="584" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="10" >
		<data att="totalHp" value="11290" /><!-- 血量 -->
		<data att="totalMp" value="38" /><!-- 魔法 -->
		<data att="attack" value="543" /><!-- 攻击 -->
		<data att="defence" value="131" /><!-- 防御 -->
		<data att="dogdeRate" value="0.011" /><!-- 闪避 -->
		<data att="criticalRate" value="0.28" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.26" /><!-- 防爆率 -->
		<data att="hitRate" value="0.011" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="424" /> <!--升级所需经验-->
		<data att="devourExp" value="686" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="11" >
		<data att="totalHp" value="13000" /><!-- 血量 -->
		<data att="totalMp" value="40" /><!-- 魔法 -->
		<data att="attack" value="600" /><!-- 攻击 -->
		<data att="defence" value="150" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0111" /><!-- 闪避 -->
		<data att="criticalRate" value="0.3" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.28" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0111" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="500" /> <!--升级所需经验-->
		<data att="devourExp" value="800" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="12" >
		<data att="totalHp" value="14890" /><!-- 血量 -->
		<data att="totalMp" value="42" /><!-- 魔法 -->
		<data att="attack" value="663" /><!-- 攻击 -->
		<data att="defence" value="171" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0112" /><!-- 闪避 -->
		<data att="criticalRate" value="0.32" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.3" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0112" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="584" /> <!--升级所需经验-->
		<data att="devourExp" value="926" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="13" >
		<data att="totalHp" value="16960" /><!-- 血量 -->
		<data att="totalMp" value="44" /><!-- 魔法 -->
		<data att="attack" value="732" /><!-- 攻击 -->
		<data att="defence" value="194" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0113" /><!-- 闪避 -->
		<data att="criticalRate" value="0.34" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.32" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0113" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="676" /> <!--升级所需经验-->
		<data att="devourExp" value="1064" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="14" >
		<data att="totalHp" value="19210" /><!-- 血量 -->
		<data att="totalMp" value="46" /><!-- 魔法 -->
		<data att="attack" value="807" /><!-- 攻击 -->
		<data att="defence" value="219" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0114" /><!-- 闪避 -->
		<data att="criticalRate" value="0.36" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.34" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0114" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="776" /> <!--升级所需经验-->
		<data att="devourExp" value="1214" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="15" >
		<data att="totalHp" value="21640" /><!-- 血量 -->
		<data att="totalMp" value="48" /><!-- 魔法 -->
		<data att="attack" value="888" /><!-- 攻击 -->
		<data att="defence" value="246" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0115" /><!-- 闪避 -->
		<data att="criticalRate" value="0.38" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.36" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0115" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="884" /> <!--升级所需经验-->
		<data att="devourExp" value="1376" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="16" >
		<data att="totalHp" value="24250" /><!-- 血量 -->
		<data att="totalMp" value="50" /><!-- 魔法 -->
		<data att="attack" value="975" /><!-- 攻击 -->
		<data att="defence" value="275" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0116" /><!-- 闪避 -->
		<data att="criticalRate" value="0.4" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.38" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0116" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="2" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1000" /> <!--升级所需经验-->
		<data att="devourExp" value="1550" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="17" >
		<data att="totalHp" value="27040" /><!-- 血量 -->
		<data att="totalMp" value="52" /><!-- 魔法 -->
		<data att="attack" value="1068" /><!-- 攻击 -->
		<data att="defence" value="306" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0117" /><!-- 闪避 -->
		<data att="criticalRate" value="0.42" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.4" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0117" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1124" /> <!--升级所需经验-->
		<data att="devourExp" value="1736" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="18" >
		<data att="totalHp" value="30010" /><!-- 血量 -->
		<data att="totalMp" value="54" /><!-- 魔法 -->
		<data att="attack" value="1167" /><!-- 攻击 -->
		<data att="defence" value="339" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0118" /><!-- 闪避 -->
		<data att="criticalRate" value="0.44" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.42" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0118" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1256" /> <!--升级所需经验-->
		<data att="devourExp" value="1934" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="19" >
		<data att="totalHp" value="33160" /><!-- 血量 -->
		<data att="totalMp" value="56" /><!-- 魔法 -->
		<data att="attack" value="1272" /><!-- 攻击 -->
		<data att="defence" value="374" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0119" /><!-- 闪避 -->
		<data att="criticalRate" value="0.46" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.44" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0119" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1396" /> <!--升级所需经验-->
		<data att="devourExp" value="2144" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="20" >
		<data att="totalHp" value="36490" /><!-- 血量 -->
		<data att="totalMp" value="58" /><!-- 魔法 -->
		<data att="attack" value="1383" /><!-- 攻击 -->
		<data att="defence" value="411" /><!-- 防御 -->
		<data att="dogdeRate" value="0.012" /><!-- 闪避 -->
		<data att="criticalRate" value="0.48" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.46" /><!-- 防爆率 -->
		<data att="hitRate" value="0.012" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1544" /> <!--升级所需经验-->
		<data att="devourExp" value="2366" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="21" >
		<data att="totalHp" value="40000" /><!-- 血量 -->
		<data att="totalMp" value="60" /><!-- 魔法 -->
		<data att="attack" value="1500" /><!-- 攻击 -->
		<data att="defence" value="450" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0121" /><!-- 闪避 -->
		<data att="criticalRate" value="0.5" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.48" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0121" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1700" /> <!--升级所需经验-->
		<data att="devourExp" value="2600" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="22" >
		<data att="totalHp" value="43690" /><!-- 血量 -->
		<data att="totalMp" value="62" /><!-- 魔法 -->
		<data att="attack" value="1623" /><!-- 攻击 -->
		<data att="defence" value="491" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0122" /><!-- 闪避 -->
		<data att="criticalRate" value="0.52" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.5" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0122" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="1864" /> <!--升级所需经验-->
		<data att="devourExp" value="2846" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="23" >
		<data att="totalHp" value="47560" /><!-- 血量 -->
		<data att="totalMp" value="64" /><!-- 魔法 -->
		<data att="attack" value="1752" /><!-- 攻击 -->
		<data att="defence" value="534" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0123" /><!-- 闪避 -->
		<data att="criticalRate" value="0.54" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.52" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0123" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2036" /> <!--升级所需经验-->
		<data att="devourExp" value="3104" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="24" >
		<data att="totalHp" value="51610" /><!-- 血量 -->
		<data att="totalMp" value="66" /><!-- 魔法 -->
		<data att="attack" value="1887" /><!-- 攻击 -->
		<data att="defence" value="579" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0124" /><!-- 闪避 -->
		<data att="criticalRate" value="0.56" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.54" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0124" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2216" /> <!--升级所需经验-->
		<data att="devourExp" value="3374" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="25" >
		<data att="totalHp" value="55840" /><!-- 血量 -->
		<data att="totalMp" value="68" /><!-- 魔法 -->
		<data att="attack" value="2028" /><!-- 攻击 -->
		<data att="defence" value="626" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0125" /><!-- 闪避 -->
		<data att="criticalRate" value="0.58" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.56" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0125" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2404" /> <!--升级所需经验-->
		<data att="devourExp" value="3656" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="26" >
		<data att="totalHp" value="60250" /><!-- 血量 -->
		<data att="totalMp" value="70" /><!-- 魔法 -->
		<data att="attack" value="2175" /><!-- 攻击 -->
		<data att="defence" value="675" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0126" /><!-- 闪避 -->
		<data att="criticalRate" value="0.6" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.58" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0126" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2600" /> <!--升级所需经验-->
		<data att="devourExp" value="3950" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="27" >
		<data att="totalHp" value="64840" /><!-- 血量 -->
		<data att="totalMp" value="72" /><!-- 魔法 -->
		<data att="attack" value="2328" /><!-- 攻击 -->
		<data att="defence" value="726" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0127" /><!-- 闪避 -->
		<data att="criticalRate" value="0.62" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.6" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0127" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="2804" /> <!--升级所需经验-->
		<data att="devourExp" value="4256" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="28" >
		<data att="totalHp" value="69610" /><!-- 血量 -->
		<data att="totalMp" value="74" /><!-- 魔法 -->
		<data att="attack" value="2487" /><!-- 攻击 -->
		<data att="defence" value="779" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0128" /><!-- 闪避 -->
		<data att="criticalRate" value="0.64" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.62" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0128" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="3016" /> <!--升级所需经验-->
		<data att="devourExp" value="4574" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="29" >
		<data att="totalHp" value="74560" /><!-- 血量 -->
		<data att="totalMp" value="76" /><!-- 魔法 -->
		<data att="attack" value="2652" /><!-- 攻击 -->
		<data att="defence" value="834" /><!-- 防御 -->
		<data att="dogdeRate" value="0.0129" /><!-- 闪避 -->
		<data att="criticalRate" value="0.66" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.64" /><!-- 防爆率 -->
		<data att="hitRate" value="0.0129" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="3236" /> <!--升级所需经验-->
		<data att="devourExp" value="4904" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
	<levelData level="30" >
		<data att="totalHp" value="79690" /><!-- 血量 -->
		<data att="totalMp" value="78" /><!-- 魔法 -->
		<data att="attack" value="2823" /><!-- 攻击 -->
		<data att="defence" value="891" /><!-- 防御 -->
		<data att="dogdeRate" value="0.013" /><!-- 闪避 -->
		<data att="criticalRate" value="0.68" /><!-- 暴击率 -->
		<data att="criticalMuti" value="" /><!-- 暴击伤害 -->
		<data att="deCriticalRate" value="0.66" /><!-- 防爆率 -->
		<data att="hitRate" value="0.013" /><!-- 命中率 -->
		<data att="regHpPerS" value="0" /><!-- 每秒回血 -->
		<data att="regMpPerS" value="5" /><!-- 每秒回魔 -->
		<data att="upLevelTotalExp" value="3464" /> <!--升级所需经验-->
		<data att="devourExp" value="5246" />   <!--吞噬经验（吞噬该等级的该宠物会获得的经验）-->
    </levelData>
 </automaticPet>
</automaticPets>
