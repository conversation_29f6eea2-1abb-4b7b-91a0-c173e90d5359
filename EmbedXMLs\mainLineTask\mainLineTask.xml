<?xml version="1.0" encoding="utf-8" ?>
<data>
    <task id="taskTest1" name="测试任务" isNew="1" descriptionId="taskDescription1"
		taskLevel="1">



		<taskGoals>
			<taskGoal id="taskGoalTest1" num="1" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大魔法药水×1">
				<equipment id="11000007" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward><!-- <taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO" 
				description=""> <equipments> <equipment id="10500000" num="5" /> </equipments> 
				<equipments> <equipment id="10500001" num="5" /> </equipments> <equipments> 
				<equipment id="10500002" num="5" /> </equipments> </taskReward> -->
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="40000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="30000" />
		</taskRewards>

		
		<!--可以同时使用多个探测器-->
		
		<!--人物探测器，其中eventStr为所有探测器通用的，当任务完成时会发送的任务目标时间字符串
		而<player playerID="playerOne" playerLevel="50" />是该探测器特有的结果，其中playerID玩家1还是玩家2（玩家1写“playerOne” 徒弟1写“tuDiOne”), playerLevel该玩家要求的等级-->
		<!--属性为空，将忽略该要求--> <!--条件可以多个，只有满足所有条件才会触发任务事件-->
		<!--<taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_Player" eventStr="player1_50_player2_50">
			<player playerID="playerOne" playerLevel="50" playerAttack="" />
		</taskDetector>-->
		
		
		
		<!--pk胜场探测器   其中pkType("pkOne"表示单人pk， “pkTwo”表示双人pk), winNum为要求的胜场-->
        <!--<taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_PKWinNum" eventStr="PKOneWinNum_100">
			<pk pkType="pkOne" winNum="100" />
		</taskDetector>-->
		
		<!--拥有装备探测器， 其中equipmetType为装备类型， suitName为套装名，如果非套装则可省略为空， equipmentName为装备名，equipmentLevel
		为装备等级， holeNum为装备孔数，如果该装备不能打孔，可省略。可以嵌套多个装备条件，只有满足所有条件才会触发任务事件-->
		<!--属性为空，则忽略该属性要求-->
		<!--<taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_HaveEq" eventStr="haveOneEq_5Level">
			
			<equipment equipmentType="egg" suitName="" equipmentName="宠物蛋（肚皮龙）" equipmentLevel="" holeNum="" />
		</taskDetector>-->
		
		
	</task>
	
	
	
	<task id="task1" name="第一次打僵尸" isNew="1" descriptionId="taskDescription1"
		taskLevel="1" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_1" />
		</gototask>

		<taskGoals>
			<taskGoal id="taskGoal1" num="20" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大魔法药水×1">
				<equipment id="11000007" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward><!-- <taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO" 
				description=""> <equipments> <equipment id="10500000" num="5" /> </equipments> 
				<equipments> <equipment id="10500001" num="5" /> </equipments> <equipments> 
				<equipment id="10500002" num="5" /> </equipments> </taskReward> -->
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="40000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="30000" />
		</taskRewards>


	</task>

	<task id="task2" name="通关双叉岭" isNew="1" descriptionId="taskDescription2"
		taskLevel="1" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_1" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal2" num="2" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大生命药水×1">
				<equipment id="11000008" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="80000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="60000" />
		</taskRewards>


	</task>
	<task id="task3" name="扫荡黑风洞僵尸" isNew="1" descriptionId="taskDescription3"
		taskLevel="1" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_2" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal3" num="30" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="皮料×3">
				<equipment id="10500009" num="3" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="120000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="90000" />
		</taskRewards>


	</task>
	<task id="task4" name="通关黑风洞" isNew="1" descriptionId="taskDescription4"
		taskLevel="1" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_2" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal4" num="2" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="黑曜铁块×3">
				<equipment id="10500004" num="3" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="160000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="120000" />
		</taskRewards>


	</task>
	<task id="task5" name="扫荡通天河僵尸" isNew="1" descriptionId="taskDescription5"
		taskLevel="1.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_3" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal5" num="40" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="针线×3">
				<equipment id="10500001" num="3" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="200000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="150000" />
		</taskRewards>


	</task>
	<task id="task6" name="通关通天河" isNew="1" descriptionId="taskDescription6"
		taskLevel="1.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_3" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal6" num="3" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×3">
				<equipment id="10500008" num="3" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="240000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="180000" />
		</taskRewards>


	</task>
	<task id="task7" name="扫荡火云洞僵尸" isNew="1" descriptionId="taskDescription7"
		taskLevel="1.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_4" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal7" num="40" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="青竹葫芦×1">
				<equipment id="10200001" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="280000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="210000" />
		</taskRewards>


	</task>
	<task id="task8" name="通关火云洞" isNew="1" descriptionId="taskDescription8"
		taskLevel="1.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_4" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal8" num="3" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="宠物蛋（肚皮龙）×1_宠物蛋（雷精）×1_宠物蛋（小石怪）×1">
				<equipments>
					<equipment id="10800000" num="1" />
				</equipments>
				<equipments>
					<equipment id="10800001" num="1" />
				</equipments>
				<equipments>
					<equipment id="10800002" num="1" />
				</equipments>


			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="320000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="240000" />
		</taskRewards>


	</task>
	<task id="task9" name="扫荡白虎岭僵尸" isNew="1" descriptionId="taskDescription9"
		taskLevel="2" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_5" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal9" num="40" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大魔法药水×2">
				<equipment id="11000016" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="360000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="270000" />
		</taskRewards>


	</task>
	<task id="task10" name="通关白虎岭" isNew="1" descriptionId="taskDescription10"
		taskLevel="2" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_5" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal10" num="3" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大生命药水×2">
				<equipment id="11000017" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>


	</task>
	<task id="task11" name="扫荡平顶山——银角僵尸" isNew="1" descriptionId="taskDescription11"
		taskLevel="2" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_6" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal11" num="50" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="皮料×6">
				<equipment id="10500009" num="6" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="440000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="330000" />
		</taskRewards>


	</task>
	<task id="task12" name="通关平顶山——银角" isNew="1" descriptionId="taskDescription12"
		taskLevel="2" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_6" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal12" num="5" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="黑曜铁块×6">
				<equipment id="10500004" num="6" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="480000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="360000" />
		</taskRewards>


	</task>
	<task id="task13" name="扫荡平顶山——金角僵尸" isNew="1" descriptionId="taskDescription13"
		taskLevel="2" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_7" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal13" num="50" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="针线×6">
				<equipment id="10500001" num="6" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="520000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="390000" />
		</taskRewards>


	</task>
	<task id="task14" name="通关平顶山——金角" isNew="1" descriptionId="taskDescription14"
		taskLevel="2" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_7" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal14" num="5" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×5">
				<equipment id="10500008" num="5" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="560000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="420000" />
		</taskRewards>


	</task>
	<task id="task15" name="扫荡狮驼岭僵尸" isNew="1" descriptionId="taskDescription15"
		taskLevel="2.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_8" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal15" num="50" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×5">
				<equipment id="10500008" num="5" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="600000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="450000" />
		</taskRewards>


	</task>
	<task id="task16" name="通关狮驼岭" isNew="1" descriptionId="taskDescription16"
		taskLevel="2.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_8" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal16" num="5" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="开孔灵符×1">
				<equipment id="10500032" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="640000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="480000" />
		</taskRewards>


	</task>
	<task id="task17" name="扫荡盘丝洞僵尸" isNew="1" descriptionId="taskDescription17"
		taskLevel="2.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_9" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal17" num="50" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×3">
				<equipment id="10500000" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="680000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="510000" />
		</taskRewards>


	</task>
	<task id="task18" name="通关盘丝洞" isNew="1" descriptionId="taskDescription18"
		taskLevel="2.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_9" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal18" num="5" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="境界突破丹×1">
				<equipment id="11000004" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="720000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="540000" />
		</taskRewards>


	</task>
	<task id="task19" name="扫荡车迟国——羊力僵尸" isNew="1" descriptionId="taskDescription19"
		taskLevel="3" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_10" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal19" num="60" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="宠物被动重置药剂×1">
				<equipment id="10500008" num="3" />
				<equipment id="10500000" num="1" />
		
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="760000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="570000" />
		</taskRewards>


	</task>
	<task id="task20" name="通关车迟国——羊力" isNew="1" descriptionId="taskDescription20"
		taskLevel="3" isgoto="1">
		<gototask type="1" >
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_10" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal20" num="6" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="宠物被动重置药剂×1">
				<equipment id="11000018" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="800000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="600000" />
		</taskRewards>


	</task>
	<task id="task21" name="扫荡车迟国——鹿力僵尸" isNew="1" descriptionId="taskDescription21"
		taskLevel="3" isgoto="1">
		<gototask type="1" >
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_11" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal21" num="70" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="宠物被动重置药剂×1">
				<equipment id="10500008" num="5" />
				<equipment id="10500000" num="2" />
		
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="840000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="630000" />
		</taskRewards>


	</task>
	<task id="task22" name="通关车迟国——鹿力" isNew="1" descriptionId="taskDescription22"
		taskLevel="3" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_11" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal22" num="7" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="洗髓丹×1">
				<equipment id="11000010" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="880000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="660000" />
		</taskRewards>


	</task>
	<task id="task23" name="扫荡车迟国——虎力僵尸" isNew="1" descriptionId="taskDescription23"
		taskLevel="3" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_12" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal23" num="80" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="材料">
				
				
				<equipments>
					<equipment id="10500225" num="1" />
				</equipments>
				
				<equipments>
					<equipment id="10500163" num="1" />
				</equipments>
				<equipments>
					<equipment id="10500122" num="1" />
				</equipments>
				<equipments>
					<equipment id="10500070" num="1" />
				</equipments>
				<equipments>
					<equipment id="10500026" num="1" />
				</equipments>
				<equipments>
					<equipment id="10500021" num="1" />
				</equipments>
				<equipments>
					<equipment id="10500005" num="1" />
				</equipments>
				<equipments>
					<equipment id="10500007" num="1" />
				</equipments>
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="920000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="690000" />
		</taskRewards>


	</task>
	<task id="task24" name="通关车迟国——虎力" isNew="1" descriptionId="taskDescription24"
		taskLevel="3" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_12" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal24" num="8" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="宠物训练卡×1">
				<equipment id="11000009" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="960000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="720000" />
		</taskRewards>


	</task>
	<task id="task25" name="扫荡火焰山僵尸" isNew="1" descriptionId="taskDescription25"
		taskLevel="3.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_13" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal25" num="100" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×10_幸运宝石×5">
				<equipment id="10500008" num="10" />
				<equipment id="11000013" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1000000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="750000" />
		</taskRewards>


	</task>
	<task id="task26" name="通关火焰山" isNew="1" descriptionId="taskDescription26"
		taskLevel="3.5" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_13" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal26" num="20" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="天赋强化丹X1">
				<equipment id="11000011" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1040000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="780000" />
		</taskRewards>


	</task>
	<task id="task27" name="扫荡花果山幽暗密林僵尸" isNew="1" descriptionId="taskDescription27"
		taskLevel="4" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_14" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal27" num="150" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×10_幸运宝石×5">
				<equipment id="10500008" num="10" />
				<equipment id="10500000" num="5" /> 
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1080000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="800000" />
		</taskRewards>


	</task>
	<task id="task28" name="通关花果山幽暗密林" isNew="1" descriptionId="taskDescription28"
		taskLevel="4" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_14" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal28" num="20" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="仓库钥匙×3">
				<equipment id="11100003" num="3" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1120000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="840000" />
		</taskRewards>


	</task>
	<task id="task29" name="扫荡花果山死亡密道僵尸" isNew="1" descriptionId="taskDescription29"
		taskLevel="4" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_15" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal29" num="160" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×10_幸运宝石×5">
				<equipment id="10500008" num="10" />
			    <equipment id="10500000" num="5" /> 
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1160000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="800000" />
		</taskRewards>


	</task>
	<task id="task30" name="通关花果山死亡密道" isNew="1" descriptionId="taskDescription30"
		taskLevel="4" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_15" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal30" num="20" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="背吧钥匙×3">
				<equipment id="11100002" num="3" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1200000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="900000" />
		</taskRewards>


	</task>
	<task id="task31" name="扫荡花果山决战之巅僵尸" isNew="1" descriptionId="taskDescription31"
		taskLevel="4" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_16" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal31" num="1000" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="高级宝石×5_幸运宝石×10">
				<equipment id="10500022" num="5" />
				<equipment id="10500000" num="10" /> 
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1240000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="850000" />
		</taskRewards>


	</task>
	<task id="task32" name="通关花果山决战之巅" isNew="1" descriptionId="taskDescription32"
		taskLevel="4" isgoto="1">
		<gototask type="1">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap1.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap1.xml" />
			<classname value="RouteMap1" />
			<gotoname value="levelMaptBtn_16" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal32" num="50" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大圣翎×2">
				<equipment id="10500023" num="2" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1280000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="900000" />
		</taskRewards>


	</task>
	<task id="task33" name="扫荡远古海域大目鱼妖" isNew="1" descriptionId="taskDescription33"
		taskLevel="4" isgoto="1">
		<gototask type="2">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
			<classname value="RouteMap2" />
			<gotoname value="levelMaptBtn_1" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal33" num="1200" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="开孔灵符×3">
				<equipment id="10500032" num="3" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1320000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="990000" />
		</taskRewards>


	</task>
	<task id="task34" name="通关远古海域" isNew="1" descriptionId="taskDescription34"
		taskLevel="4" isgoto="1">
		<gototask type="2">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
			<classname value="RouteMap2" />
			<gotoname value="levelMaptBtn_1" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal34" num="60" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="3级生命宝石×1_3攻击宝石级×1_3级防御宝石×1">
				<equipments>
					<equipment id="11900002" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900012" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900017" num="1" />
				</equipments>
				
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1360000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="1120000" />
		</taskRewards>

	</task>
	<task id="task35" name="扫荡清风寨山贼" isNew="1" descriptionId="taskDescription35"
		taskLevel="4" isgoto="1">
		<gototask type="2">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
			<classname value="RouteMap2" />
			<gotoname value="levelMaptBtn_2" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal35" num="1400" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="摘石锤×3">
				<equipment id="10500031" num="2" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="1150000" />
		</taskRewards>


	</task>
	<task id="task36" name="通关清风寨" isNew="1" descriptionId="taskDescription36"
		taskLevel="4" isgoto="1">
		<gototask type="2">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
			<classname value="RouteMap2" />
			<gotoname value="levelMaptBtn_2" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal36" num="70" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="金袋×2">
				<equipment id="11100000" num="2" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1440000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="1180000" />
		</taskRewards>


	</task>
	<task id="task37" name="扫荡地狱之门僵尸恶鬼" isNew="1" descriptionId="taskDescription37"
		taskLevel="5" isgoto="1">
		<gototask type="2">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
			<classname value="RouteMap2" />
			<gotoname value="levelMaptBtn_4" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal37" num="1500" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="超进化仙果×1">
				<equipment id="10500032" num="3" />
				<equipment id="10500031" num="2" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1480000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="1210000" />
		</taskRewards>


	</task>
	<task id="task38" name="通关千年僵尸王" isNew="1" descriptionId="taskDescription38"
		taskLevel="5" isgoto="1">
		<gototask type="2">
			<swfpath value="NewGameFolder/GuardingTangSengLevelMode/RouteMap2.swf" />
			<xmlpath value="NewGameFolder/GuardingTangSengLevelMode/routeMap2.xml" />
			<classname value="RouteMap2" />
			<gotoname value="levelMaptBtn_4" />
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal38" num="50" />
			<!--<taskGoal id="taskGoal1" num="3" /> -->
		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="4级生命宝石×1_4攻击宝石级×1_4级防御宝石×1">
				<equipments>
					<equipment id="11900003" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900013" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900018" num="1" />
				</equipments>
				
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1800000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="1500000" />
			</taskRewards>

	</task>


	<task id="task39" name="第一次打造装备" isNew="1" descriptionId="taskDescription39"
		taskLevel="0.5" isgoto="1">
		<gototask type="3">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal39" num="1" />

		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="1级生命宝石×1_1攻击宝石级×1_1级防御宝石×1">
				
				<equipments>
					<equipment id="10709004" num="1" />
				</equipments>
				
				<equipments>
					<equipment id="10708004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10707004" num="1" />
				</equipments>
				
				<equipments>
					<equipment id="10706004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10705004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10702004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10704004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10701004" num="1" />
				</equipments>
				
				
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="50000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="20000" />
		</taskRewards>


	</task>
	<task id="task40" name="第一次强化装备" isNew="1" descriptionId="taskDescription40"
		taskLevel="0.5">
		<gototask type="4">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal40" num="1" />

		</taskGoals>

			<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="1级生命宝石×1_1攻击宝石级×1_1级防御宝石×1">
				
					<equipments>
					<equipment id="10109004" num="1" />
				</equipments>
				
				<equipments>
					<equipment id="10108004" num="1" />
				</equipments>
				
				<equipments>
					<equipment id="10107004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10106004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10105004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10102004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10104004" num="1" />
				</equipments>
				<equipments>
					<equipment id="10101004" num="1" />
				</equipments>
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="50000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="20000" />
		</taskRewards>



	</task>
	<task id="task41" name="第一次普通孵化宠物" isNew="1" descriptionId="taskDescription41"
		taskLevel="1">
		<gototask type="5">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal43" num="1" />

		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="1级生命宝石×1_1攻击宝石级×1_1级防御宝石×1">
				<equipments>
					<equipment id="10800000" num="1" />
				</equipments>
				<equipments>
					<equipment id="10800001" num="1" />
				</equipments>
				<equipments>
					<equipment id="10800002" num="1" />
				</equipments>
				<equipments>
					<equipment id="10800004" num="1" />
				</equipments>
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="150000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="100000" />
		</taskRewards>


	</task>
	<task id="task42" name="第一次签到" isNew="1" descriptionId="taskDescription42"
		taskLevel="1">
		<gototask type="6">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal47" num="1" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×5">
				<equipment id="10500008" num="5" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="50000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="20000" />
		</taskRewards>



	</task>
	<task id="task43" name="打造能手" isNew="1" descriptionId="taskDescription43"
		taskLevel="1.5">
		<gototask type="3">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal39" num="5" />

		</taskGoals>
		<taskRewards>

		<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×10_幸运宝石X5">
				<equipment id="10500008" num="10" />
                <equipment id="10500000" num="5" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="150000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="100000" />
		</taskRewards>


	</task>
	<task id="task44" name="强化能手" isNew="1" descriptionId="taskDescription44"
		taskLevel="1.5">
		<gototask type="4">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal41" num="1" />

		</taskGoals>
		<taskRewards>
		<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×20_幸运宝石X5">
				<equipment id="10500008" num="10" />
				<equipment id="10500000" num="5" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="250000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="200000" />
		</taskRewards>



	</task>
	<task id="task45" name="孵化能手" isNew="1" descriptionId="taskDescription45"
		taskLevel="1.5">
		<gototask type="5">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal43" num="5" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="超进化仙果×1_大经验丹(宠物)X1">
				<equipment id="10500030" num="1" />
				<equipment id="11000005" num="1" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="150000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="100000" />
		</taskRewards>



	</task>
	<task id="task46" name="第一次幻化" isNew="1" descriptionId="taskDescription46"
		taskLevel="1">
		<gototask type="7">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal45" num="1" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="魔剑蛋×1_大经验丹(宠物)X1">
				<equipment id="10800007" num="1" />
				<equipment id="11000006" num="1" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="150000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="100000" />
		</taskRewards>



	</task>
	<task id="task47" name="在线达人" isNew="1" descriptionId="taskDescription47"
		taskLevel="2">

		<taskGoals>
			<taskGoal id="taskGoal48" num="1" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="开孔灵符×1">
				<equipment id="10500032" num="1" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="150000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="100000" />
		</taskRewards>



	</task>
	<task id="task48" name="打造高手" isNew="1" descriptionId="taskDescription48"
		taskLevel="2">
		<gototask type="3">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal39" num="10" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="开孔灵符×1">
				<equipment id="10500023" num="1" />
	
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>


	</task>
	<task id="task49" name="强化高手" isNew="1" descriptionId="taskDescription49"
		taskLevel="2">
		<gototask type="4">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal50" num="1" />

		</taskGoals>
		<taskRewards>
		<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="升级宝石×5_幸运宝石X5">
				<equipment id="10500022" num="1" />
				<equipment id="10500000" num="5" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>



	</task>
	<task id="task50" name="孵化高手" isNew="1" descriptionId="taskDescription50"
		taskLevel="2">
		<gototask type="5">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal44" num="5" />

		</taskGoals>
	<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大经验丹(宠物)X3">
				<equipment id="10500030" num="1" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>



	</task>
	<task id="task51" name="在线王者" isNew="1" descriptionId="taskDescription51"
		taskLevel="2.5">

		<taskGoals>
			<taskGoal id="taskGoal49" num="1" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="开孔灵符×2">
				<equipment id="10500032" num="2" />
				<equipment id="10500031" num="2" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="300000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="200000" />
		</taskRewards>



	</task>
	<task id="task52" name="幻化能手" isNew="1" descriptionId="taskDescription52"
		taskLevel="3">
		<gototask type="7">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal45" num="3" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大经验丹(宠物)X3">
				<equipment id="11000006" num="3" />
				<equipment id="11000018" num="1" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="300000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="200000" />
		</taskRewards>



	</task>
	<task id="task53" name="第一次装备开孔" isNew="1" descriptionId="taskDescription53"
		taskLevel="1">
		<gototask type="3">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal42" num="1" />

		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="开孔灵符×1">
				<equipment id="10500032" num="1" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="3000000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="200000" />
		</taskRewards>


	</task>
	<task id="task54" name="第一次超进化" isNew="1" descriptionId="taskDescription54"
		taskLevel="1">
		<gototask type="8">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal46" num="1" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大经验丹（宠物）×3">
				<equipment id="11000006" num="1" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="300000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="200000" />
		</taskRewards>



	</task>
	<task id="task55" name="打造大师" isNew="1" descriptionId="taskDescription55"
		taskLevel="3.5">
		<gototask type="3">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal39" num="20" />

		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大圣翎×1">
				<equipment id="10500023" num="1" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="500000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="400000" />
		</taskRewards>


	</task>
	<task id="task56" name="强化大师" isNew="1" descriptionId="taskDescription56"
		taskLevel="3.5">
		<gototask type="4">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal40" num="20" />

		</taskGoals>
		<taskRewards>
	    <taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="高级宝石×5_幸运宝石X5">
				<equipment id="10500022" num="5" />
		        <equipment id="10500000" num="5" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="500000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="400000" />
		</taskRewards>



	</task>
	<task id="task57" name="孵化大师" isNew="1" descriptionId="taskDescription57"
		taskLevel="3.5">
		<gototask type="5">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal44" num="5" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="被动×1_大经验丹(宠物)X3">
				<equipment id="11000018" num="1" />
				<equipment id="11000005" num="3" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="500000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="400000" />
		</taskRewards>



	</task>
	<task id="task58" name="幻化达人" isNew="1" descriptionId="taskDescription58"
		taskLevel="3.5">
		<gototask type="7">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal45" num="5" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="超进化仙果×1_大经验丹(宠物)X3">
				<equipment id="10500030" num="1" />
				<equipment id="11000005" num="3" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>



	</task>
	<task id="task59" name="超进化达人" isNew="1" descriptionId="taskDescription59"
		taskLevel="3.5">
		<gototask type="8">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal46" num="3" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大经验丹（宠物）×3">
				<equipment id="11000006" num="3" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>



	</task>
	<task id="task60" name="开孔达人" isNew="1" descriptionId="taskDescription60"
		taskLevel="3">
		<gototask type="3">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal42" num="5" />

		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="1级生命宝石×1_1攻击宝石级×1_1级防御宝石×1">
				<equipments>
					<equipment id="11900000" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900010" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900015" num="1" />
				</equipments>
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>


	</task>
	<task id="task61" name="打造大神" isNew="1" descriptionId="taskDescription61"
		taskLevel="4">
		<gototask type="3">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal39" num="20" />

		</taskGoals>

		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="8种强化石头选择一种">
				
				
				<equipments>
					<equipment id="10500158" num="3" />
				</equipments>
				
				<equipments>
					<equipment id="10500119" num="3" />
				</equipments>
				
				<equipments>
					<equipment id="10500071" num="3" />
				</equipments>
				<equipments>
					<equipment id="10500036" num="3" />
				</equipments>
				<equipments>
					<equipment id="10500037" num="3" />
				</equipments>
				<equipments>
					<equipment id="10500038" num="3" />
				</equipments>
				<equipments>
					<equipment id="10500039" num="3" />
				</equipments>	
					<equipments>
					<equipment id="10500220" num="3" />
				</equipments>	
				
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="600000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="500000" />
		</taskRewards>

	</task>
	<task id="task62" name="强化大神" isNew="1" descriptionId="taskDescription62"
		taskLevel="4">
		<gototask type="4">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal40" num="50" />

		</taskGoals>
		<taskRewards>
	        <taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="高级宝石×5_幸运宝石X10">
				<equipment id="10500022" num="5" />
				<equipment id="10500000" num="10" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="6000000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="500000" />
		</taskRewards>



	</task>
	<task id="task63" name="孵化大神" isNew="1" descriptionId="taskDescription63"
		taskLevel="4">
		<gototask type="5">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal44" num="15" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="宠物训练卡_超级宠物训练卡">
				<equipment id="11000009" num="1" />
				<equipment id="11000021" num="1" />

			</taskReward>>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="600000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="500000" />
		</taskRewards>



	</task>
	<task id="task64" name="幻化高手" isNew="1" descriptionId="taskDescription64"
		taskLevel="4">
		<gototask type="7">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal45" num="10" />

		</taskGoals>
		
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="超进化仙果×1_大经验丹(宠物)X5">
				<equipment id="10500030" num="1" />
				<equipment id="11000005" num="5" />

			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="500000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="400000" />
		</taskRewards>



	</task>
	<task id="task65" name="超进化高手" isNew="1" descriptionId="taskDescription65"
		taskLevel="4.5">
		<gototask type="8">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal46" num="5" />

		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="4级生命宝石×1_4攻击宝石级×1_4级防御宝石×1">
				<equipments>
					<equipment id="11800004" num="1" />
				</equipments>
				<equipments>
					<equipment id="11800005" num="1" />
				</equipments>
				<equipments>
					<equipment id="11800006" num="1" />
				</equipments>
				<equipments>
					<equipment id="11800007" num="1" />
				</equipments>
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="500000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="400000" />
		</taskRewards>



	</task>
	<task id="task66" name="开孔高手" isNew="1" descriptionId="taskDescription66"
		taskLevel="4.5">
		<gototask type="3">
		</gototask>
		<taskGoals>
			<taskGoal id="taskGoal42" num="12" />

		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="4级生命宝石×1_4攻击宝石级×1_4级防御宝石×1">
				<equipments>
					<equipment id="11900004" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900014" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900019" num="1" />
				</equipments>
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="600000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="500000" />
		</taskRewards>


	</task>
		<task id="task67" name="60级僵尸达人" isNew="1" descriptionId="taskDescription67"
		taskLevel="4.5">
		
         <taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_Player" eventStr="player1_60_player2_60">
			<player playerID="playerOne" playerLevel="60" playerAttack="" />
		</taskDetector>
		
		<taskGoals>
			<taskGoal id="taskGoal50" num="1" />

		</taskGoals>
		<taskRewards>

	         <taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="超进化仙果×1">
				<equipment id="10500022" num="5" />
				<equipment id="10500000" num="5" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="500000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="400000" />
		</taskRewards>
	</task>
	<task id="task68" name="单人PK胜利300场" isNew="1" descriptionId="taskDescription68"
		taskLevel="4.5">
		<gototask type="9">
		</gototask>
		<taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_PKWinNum" eventStr="PKOneWinNum_300">
			<pk pkType="pkOne" winNum="300" />
		</taskDetector>

		<taskGoals>
			<taskGoal id="taskGoal51" num="1" />

		</taskGoals>
		<taskRewards>
	         <taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="开孔灵符、幸运宝石">
				<equipment id="10500032" num="5" />
				<equipment id="10500000" num="5" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="600000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="500000" />
		</taskRewards>


	</task>
	<task id="task69" name="人物攻击力超过1500" isNew="1" descriptionId="taskDescription69"
		taskLevel="4.5">
		
		
		<taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_Player" eventStr="playerAttack_1500">
			<player playerID="playerOne" playerLevel="" playerAttack="1500" />
		</taskDetector>
		<taskGoals>
			<taskGoal id="taskGoal52" num="1" />

		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.ChoiceEquipmentRewardVO"
				description="4级生命宝石×1_4攻击宝石级×1_4级防御宝石×1">
				<equipments>
					<equipment id="11900002" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900012" num="1" />
				</equipments>
				<equipments>
					<equipment id="11900017" num="1" />
				</equipments>
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="600000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="400000" />
		</taskRewards>


	</task>
	<task id="task70" name="大圣装达人" isNew="1" descriptionId="taskDescription70"
		taskLevel="4.5">
		
         <taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_HaveEq" eventStr="haveOneEq_Level9_1">
			
			<equipment equipmentType="" suitName="大圣套装" equipmentName="" equipmentLevel="9" holeNum="" />
	
		</taskDetector>
		<taskGoals>
			<taskGoal id="taskGoal53" num="1" />

		</taskGoals>
		<taskRewards>

			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="大圣">
				<equipment id="10500023" num="1" />
	
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="500000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>


	</task>
	<task id="task71" name="精通师徒玩法" isNew="1" descriptionId="taskDescription71"
		taskLevel="4.5">
		
         <taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_Player" eventStr="tuDiOne_5">
			<player playerID="tuDiOne" playerLevel="5" playerAttack="" />
		</taskDetector>
		
		<taskGoals>
			<taskGoal id="taskGoal54" num="1" />

		</taskGoals>
		<taskRewards>

	         <taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="境界突破丹×1、幸运宝石×5">
				<equipment id="11000013" num="1" />
				<equipment id="10500000" num="5" />
				<!--<equipment id="10500000" num="5" /> -->
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="400000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="300000" />
		</taskRewards>


	</task>
	<!--可以同时使用多个探测器-->
		
		<!--人物探测器，其中eventStr为所有探测器通用的，当任务完成时会发送的任务目标时间字符串
		而<player playerID="playerOne" playerLevel="50" />是该探测器特有的结果，其中playerID玩家1还是玩家2（玩家1写“playerOne” 徒弟1写“tuDiOne”), playerLevel该玩家要求的等级-->
		<!--属性为空，将忽略该要求--> <!--条件可以多个，只有满足所有条件才会触发任务事件-->
		<!--<taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_Player" eventStr="player1_50_player2_50">
			<player playerID="playerOne" playerLevel="50" playerAttack="" />
		</taskDetector>-->
		
		
		
		<!--pk胜场探测器   其中pkType("pkOne"表示单人pk， “pkTwo”表示双人pk), winNum为要求的胜场-->
        <!--<taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_PKWinNum" eventStr="PKOneWinNum_100">
			<pk pkType="pkOne" winNum="100" />
		</taskDetector>-->
		
		<!--拥有装备探测器， 其中equipmetType为装备类型， suitName为套装名，如果非套装则可省略为空， equipmentName为装备名，equipmentLevel
		为装备等级， holeNum为装备孔数，如果该装备不能打孔，可省略。可以嵌套多个装备条件，只有满足所有条件才会触发任务事件-->
		<!--属性为空，则忽略该属性要求-->
		<!--<taskDetector   className="UI.MainLineTask.TaskDetectors.TaskDetector_HaveEq" eventStr="haveOneEq_5Level">
			
			<equipment equipmentType="" suitName="大圣套装" equipmentName="" equipmentLevel="" holeNum="" />
		</taskDetector>-->
	<!--

	<task id="task2" name="任务2" isNew="1" descriptionId="taskDescription1"
		taskLevel="4.5">
		<taskGoals>
			<taskGoal id="taskGoal1" num="5" />
			<taskGoal id="taskGoal1" num="3" />
		</taskGoals>
		<taskRewards>
			<taskReward className="UI.MainLineTask.TaskRewardVO.EquipmentRewardVO"
				description="双倍经验药水X1_无敌药水X3">
				<equipment id="11400000" num="1" />
				<equipment id="11000000" num="3" />
				<equipment id="10500000" num="5" />
			</taskReward>
			<taskReward className="UI.MainLineTask.TaskRewardVO.ExperienceRewardVO"
				description="经验" value="1000000" />
			<taskReward className="UI.MainLineTask.TaskRewardVO.MoneyRewardVO"
				description="金币" value="200000" />
		</taskRewards>
	</task> 

-->
<!--活跃任务-->
	<task id="active1" name="完成1次每日签到" isNew="1" descriptionId=""
		taskLevel="1">
		<taskGoals>
			<taskGoal id="taskGoal47" num="1" />
		</taskGoals>
	</task>
	<task id="active2" name="完成1次日常任务" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals>
			<taskGoal id="activeTaskGoal1" num="1" />
		</taskGoals>
	</task>
	<task id="active3" name="完成1次宠物普通孵化" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals>
			<taskGoal id="taskGoal43" num="1" />
		</taskGoals>
	</task>
	<task id="active4" name="完成1次农场种植" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals>
			<taskGoal id="activeTaskGoal2" num="1" />
		</taskGoals>
	</task>
	
	<task id="active5" name="完成1次世界BOSS挑战" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals>
			<taskGoal id="activeTaskGoal3" num="1" />
		</taskGoals>
	</task>
	<task id="active6" name="完成1次PK竞技" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals>
			<taskGoal id="activeTaskGoal4" num="1" />
		</taskGoals>
	</task>
	<task id="active7" name="完成1次任意关卡" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals>
			<taskGoal id="activeTaskGoal5" num="1" />
		</taskGoals>
	</task>
	<task id="active8" name="完成1次天宫BOSS" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals>
			<taskGoal id="activeTaskGoal7" num="1" />
		</taskGoals>
	</task>
	<task id="active9" name="点券购买1次任意物品" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals> 
			<taskGoal id="activeTaskGoal6" num="1" />
		</taskGoals>
	</task>
	<task id="active10" name="完成1次装备强化" isNew="1" descriptionId=""
		taskLevel="1">

		<taskGoals>
			<taskGoal id="taskGoal40" num="1" />
		</taskGoals>
	</task>

</data>