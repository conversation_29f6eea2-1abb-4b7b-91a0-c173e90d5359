<?xml version="1.0" encoding="utf-8" ?>
<data>
    <animal id="mount2" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="100" runSpeed="400">
		<attackRange x="0" y="-50" z="-1" xRange="200" yRange="100" zRange="100" />
        <idle   defId="idleOfMount" />
		<walk   defId="walkOfMount" />
		<run    defId="runOfMount" />
		<attack defId="attackOfMount" />
		<!--<hurt defId="autoAttackPet1Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>
		<hurt2 defId="autoAttackPet1Hurt2" />

		<die defId="autoAttackPet1Die" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>-->
		<shadow defId="shakeOfMount" />

		<sound>
			<sound id="beAttackSound1" 
			       name="beAttackSound1" 
				   swfPath="NewGameFolder/SharedSound.swf"
				   className="BeAttackSound1" />
			<sound id="enemyDeadSound1" 
			       name="enemyDeadSound1"
				   swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				   className="EnemyDeadSound" />
		</sound>




		<animationDefinitions>

			<animationDefinition id="idleOfMount" 
			                     rows="1"
				                 cols="1" 
								 walkable="false" 
								 overlap="false" 
								 frameInterval="1"
				                 defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				                 <show swfPath="Resources/Mount/Mount2.swf"
					                   showClass="IdleOfMount" 
									   x_offset="0" 
									   y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walkOfMount" 
			                     rows="1"
				                 cols="1" 
								 walkable="false" 
								 overlap="false" 
								 frameInterval="1"
				                 defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				                 <show swfPath="Resources/Mount/Mount2.swf"
					                   showClass="WalkOfMount" 
									   x_offset="0" 
									   y_offset="0" />
			</animationDefinition>
            <animationDefinition id="runOfMount" 
			                     rows="1"
				                 cols="1" 
								 walkable="false" 
								 overlap="false" 
								 frameInterval="1"
				                 defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				                 <show swfPath="Resources/Mount/Mount2.swf"
					                   showClass="RunOfMount" 
									   x_offset="0" 
									   y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attackOfMount" 
			                     rows="1"
				                 cols="1" 
								 walkable="false" 
								 overlap="false" 
								 frameInterval="1"
				                 defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				                 <show swfPath="Resources/Mount/Mount2.swf"
					                   showClass="AttackOfMount" 
									   x_offset="0" 
									   y_offset="0" />
			</animationDefinition>
			<animationDefinition id="upToMount" 
			                     rows="1"
				                 cols="1" 
								 walkable="false" 
								 overlap="false" 
								 frameInterval="1"
				                 defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				                 <show swfPath="Resources/Mount/MountEffect1.swf"
					                   showClass="UpToMountEffect" 
									   x_offset="0" 
									   y_offset="0" />
			</animationDefinition>
			<animationDefinition id="downFromMount" 
			                     rows="1"
				                 cols="1" 
								 walkable="false" 
								 overlap="false" 
								 frameInterval="1"
				                 defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				                 <show swfPath="Resources/Mount/MountEffect1.swf"
					                   showClass="DownFromMountEffect" 
									   x_offset="0" 
									   y_offset="0" />
			</animationDefinition>
			<animationDefinition id="effectInOwner" 
			                     rows="1"
				                 cols="1" 
								 walkable="false" 
								 overlap="false" 
								 frameInterval="3"
				                 defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				                 <show swfPath="Resources/Mount/Mount2.swf"
					                   showClass="EffectInOwner" 
									   x_offset="0" 
									   y_offset="0" />
			</animationDefinition>
			<animationDefinition id="shakeOfMount" 
			                     rows="1"
				                 cols="1" 
								 walkable="false" 
								 overlap="false" 
								 frameInterval="5"
				                 defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				                 <show swfPath="Resources/Mount/Mount2.swf"
					                   showClass="ShadeOfMount" 
									   x_offset="0" 
									   y_offset="0"  />
			</animationDefinition>
		</animationDefinitions>

	</animal>
</data>