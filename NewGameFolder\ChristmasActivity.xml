<?xml version="1.0" encoding="utf-8" ?>
<data>
<!--beAttackEscape 被打后多久没有再次被打执行逃跑策略 时间间隔单位是秒-->
<!--randomRunRect 被打后移动的随机范围-->
<!--hpEscape 血量多少的时候执行逃跑-->
<appear proWeight="20" startDate="2015-12-23 00:00:00" endDate="2018-01-04 00:00:00" startTime="09:00:00" endTime="22:00:00"
		beAttackEscape="5" randomRunRect="50,50,850,350" hpEscape="25"> 
	<normal drop0="1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|24|25|26|27"
			drop1="28|29|30|31|32|34|35|36|37|38"> <!--普通关卡1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16| -->
	</normal>
	<dreamLand 	drop0=""
				drop1="">   <!--幻境 -->
	</dreamLand>
</appear>
	<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
	<Wave waveCount="1" totalEnemyNum="1" x="900" y="300" xRange="50" yRange="50">
		<Enemy enemyClass="YJFY.ActivityMode.SantaClaus" xmlPath="xiaoBing" startTime="500" duration="40000" num="1" isFallDown="0" />
	</Wave>

<EqDrop>
	<xiaoBing id="0" noDropProWeight="1">
		<!--proWeight 概率权重-->
  	  
		<!-- 宝箱  --> 
	   <item dropClassName="UI.Equipments.SceneEquipments.Pocket_Yindandan_S" proWeight="500" />
         <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="5" />
   	   
	
		

   	    
	</xiaoBing>
	<xiaoBing id="1" noDropProWeight="1">
		<!--proWeight 概率权重-->
  	  
		<!-- 宝箱  --> 
		
 
	 <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="5" />
<item dropClassName="UI.Equipments.SceneEquipments.Pocket_Jindandan_S" proWeight="500" />
   	    
	</xiaoBing>
</EqDrop>
<xiaoBing>
	<!--敌人数据 -->
	<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
		<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
	</fallDownEffect>
	
	
	<enemyData>
		<data att="totalHp" value="95" />
		<data att="attack" value="0" />
		<data att="expOfDieThisEnemy" value="150" />
		<data att="defence" value="10000000" />
		<data att="dogdeRate" value="0.01" />
		<data att="criticalRate" value="0.05" />
		<data att="criticalMuti" value="1" />
		<data att="deCriticalRate" value="1000" />
		<data att="hitRate" value="0" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy8" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="60" bodyHeight="80" walkSpeed="20"
		runSpeed="110|90|150|120|80">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 <notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="40" yRange="60"
			zRange="100" />
		<appear defId="" soundId="appearSound" loop="2"/>//播放出现的声音 loop 播放循环次数-1 无限
		<idle defId="walk_enemy8" />
		<walk defId="walk_enemy8" />
		<run defId="run_enemy8|Run_enemy9|Run_enemy10" />
		<attack defId="attack_enemy8" />
		<hurt defId="hurt_enemy8" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy8" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow1" />

		<sound>
			<sound id="appearSound" name="appearSound" swfPath="NewGameFolder/SharedSound.swf"
				className="jinglebells" />
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			
			
			
			<animationDefinition id="walk_enemy8" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/fuxing.swf"
					showClass="Walk_Monster_8" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="run_enemy8" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/fuxing.swf"
					showClass="Run_Monster_8" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="hurt_enemy8" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/fuxing.swf"
					showClass="BeAttack_Monster_8" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<animationDefinition id="Run_enemy9" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/fuxing.swf"
					showClass="Run_Monster_9" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="Run_enemy10" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/fuxing.swf"
					showClass="Run_Monster_10" x_offset="0" y_offset="0" />
			</animationDefinition>

			
			<animationDefinition id="attack_enemy8" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/fuxing.swf"
					showClass="Attack_Monster_8" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy8" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/fuxing.swf"
					showClass="Dead_Monster_8" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="xiaoBingFallDownShow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
		</animationDefinition>
		</animationDefinitions>



	</animal>
</xiaoBing>
</data>