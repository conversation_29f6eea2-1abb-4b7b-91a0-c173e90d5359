<?xml version="1.0" encoding="utf-8" ?>
<data id="boss8" swfPath="NewGameFolder/BossMode/Boss8.swf"
	className="BossModeMap" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound5.swf" className="SoundCC" />
    <startShow swfPath="NewGameFolder/BossMode/Boss8.swf" className="CBossStoryShow" />
	<bossHead swfPath="NewGameFolder/BossMode/Boss8.swf" className="CBossHead" />
	<lottery>
		<!-- 只能12个-->
		<item id="10800099" num="1" proWeight="15" />
        <item id="10800008" num="1" proWeight="4" />
        <item id="10500033" num="1" proWeight="20" />
        <item id="10500033" num="2" proWeight="5" />
        <item id="10500033" num="3" proWeight="20" />
        <item id="10500034" num="1" proWeight="15" />
        <item id="10500034" num="2" proWeight="5" />
        <item id="10500034" num="4" proWeight="3" />
        <item id="10500000" num="4" proWeight="3" />
        <item id="11100000" num="1" proWeight="3" />
        <item id="10500023" num="2" proWeight="1" />
        <item id="11800004" num="1" proWeight="3" />
	</lottery>
	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
   <boss type="YJFY.BossMode.Boss1.Boss1">
	<!--敌人数据 -->
    <bossData hpSegment="10000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_BossTeleport" hurtMulti="0" costMp="10" cdTime="10000" priorityForRun="0" priorityForRunInHurt="1"  
	isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="Skill_BossSkill1" hurtMulti="3" costMp="10" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
	isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="BossDaZhao" hurtMulti="3" costMp="30" cdTime="30000" priorityForRun="2" priorityForRunInHurt="0"  
	isInvincibleInRun="1" isAbleRunInHurt="1"  className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO"/> <!--cdTime 毫秒-->
		<skill skillId="RecoverHpSkill" hurtMulti="0" costMp="8" cdTime="50000" recoverHpPerS="5000" priorityForRun="3" priorityForRunInHurt="0"  
	isInvincibleInRun="1" isAbleRunInHurt="1"  className="YJFY.XydzjsData.AISkillVO.AIRecoverHpSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
	<enemyData>
		<data att="totalHp" value="189999" />
		<data att="attack" value="2200" />
		<data att="expOfDieThisEnemy" value="900000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.1" />
		<data att="criticalRate" value="0.3" />
		<data att="criticalMuti" value="1" />
		<data att="deCriticalRate" value="0.6" />
		<data att="hitRate" value="0.2" />
		
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="2" />
	</enemyData>
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="200" walkSpeed="60"
		runSpeed="120">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-40" z="-1" xRange="300" yRange="80" zRange="200" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
			
		</skill>
		<skill id="Skill_BossSkill1" className="YJFY.BossMode.Boss1.Skill_BossSkill1" x="0"
			y="-100" z="-1" xRange="470" yRange="200" zRange="100" bodyDefId="skill1Animation" skillAttackReachFrameLabel="skillReach"
			skillEndFrameLabel="skillEnd^stop^">
			
		</skill>
		
		 
	   <skill id="BossDaZhao" className="YJFY.BossMode.Boss1.BossDaZhao2" x="-480"
			y="-150" z="-1" xRange="960" yRange="300" zRange="1000" bodyDefId="daZhaoBodyShow" bodyAttackReachFrameLabel="skillAttackReach" 
			bodySkillEndFrameLabel="skillEnd^stop^"  everyEntityAddShowDefId=""  everyEntityAddShowIsFrontOfBody="0" skillTimeOfDuration="0" hurtDuration="5000"
			attackInterval="0" createRandomShowInterval="100" randomEntityShowId="daZhaoYu" addEffectOnRandomEntitysId="daZhaoYuDaoDaEffect" 
			addGroundEffectOneRandomEntityId="daZhaoGroundEffect" effectAddtoTargetId="" randomEntityReachGroundFrameLabel="shanDianreachGround">
			<animationDefinition id="daZhaoGroundEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="FlashGroundEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoYuDaoDaEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="FlashBeAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoYu" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="FlashAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<shakeView swfPath="NewGameFolder/BossMode/Boss8.swf" className="ShakeView" />
			<flashView swfPath="NewGameFolder/BossMode/Boss8.swf" className="FlashView" />
		</skill>
		<skill id="RecoverHpSkill" className="YJFY.BossMode.Boss1.Skill_BossRecoverHpSkill"  bodyDefId="recoverHpShow" skillTimeOfDuration="2000">
			
		</skill>

		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="CBossStand" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="CBossWalk" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/BossMode/Boss8.swf" 
				showClass="CBossRun" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="CBossBeAtk" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="CBossAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="CBossDead" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoGroundEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="FlashGroundEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoYuDaoDaEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="FlashBeAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="daZhaoYu" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="FlashAttack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="recoverHpShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="AddHpSkillShow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="DisappearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="AppearEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss8.swf"
					showClass="CBossChallengeBossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss>
</data>
