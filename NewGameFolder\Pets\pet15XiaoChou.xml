<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet15" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet15Idle" />
		<walk defId="pet15Walk" />
		<run defId="pet15Run" />
		<attack defId="pet15Attack"  />
		<hurt defId="pet15Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet15Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	   <skill id="Skill_Pet15Skill" className="YJFY.Skill.PetSkills.Skill_Pet15Skill" x="-480" 
			y="-280" z="-1" xRange="960" yRange="560" zRange="100" bodyDefId="pet15SkillBodyShow" hurtDuration="10000" 
			bodyAttackReachFrameLabel="down" everyEntityAddShowDefId="skillStarShow" 
			bodySkillEndFrameLabel="end^stop^" >
			<animationDefinition id="skillStarShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="SkillStarShow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="changeStart" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="ChangeStart" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="changeEnd" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="ChangeEnd" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="changeState1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="ChangeState1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="changeState2" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="ChangeState2" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet15Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="PetStand15_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet15Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="PetWalk15_4" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet15SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="PetSkill15_4" x_offset="0" y_offset="0" />
			</animationDefinition>
		
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			
			<animationDefinition id="skillStarShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="SkillStarShow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="changeStart" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="ChangeStart" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="changeEnd" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="ChangeEnd" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="changeState1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="ChangeState1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="changeState2" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet15All.swf"
					showClass="ChangeState2" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			
		</shows>

	</animal>
</data>
