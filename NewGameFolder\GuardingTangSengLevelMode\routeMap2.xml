<?xml version="1.0" encoding="utf-8" ?>
<data routeMapId="routeMap2">
    <level btnName="levelMaptBtn_1" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level17/level.xml" />
    <level btnName="levelMaptBtn_2" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level18/level.xml" />
    <level btnName="levelMaptBtn_3" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level19/level.xml" />        
    <level btnName="levelMaptBtn_4" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level20/level.xml" />                    
    <level btnName="levelMaptBtn_5" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level22/level.xml" />                    
    <level btnName="levelMaptBtn_6" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level23/level.xml" />                    
    <level btnName="levelMaptBtn_7" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level24/level.xml" />
    <level btnName="levelMaptBtn_8" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level25/level.xml" />
    <level btnName="levelMaptBtn_9" levelXMLPath="NewGameFolder/GuardingTangSengLevelMode/Level26/level.xml" />
	<level btnName="levelMaptBtn_intersection" toAnotherRouteMap="1"  routeMapXMLPath="NewGameFolder/GuardingTangSengLevelMode/routeMap4.xml" 
	       routeMapShowSwfPath="NewGameFolder/GuardingTangSengLevelMode/RouteMap4.swf" routeMapShowClassName="RouteMap4" />
</data>