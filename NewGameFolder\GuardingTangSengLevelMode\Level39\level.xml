<?xml version="1.0" encoding="utf-8" ?>
<data id="Level39"
	swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/level.swf"
	className="LevelMap39" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
		swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf"
		className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
	<Waves totalWaveNum="21">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="1000" duration="5000" num="5" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.Level39.Castellan"
				xmlPath="boss" startTime="1000" duration="1000" num="1" />
		</Wave>
		<Wave waveCount="2" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="20000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="30000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="45000" duration="50000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="5" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="60000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="6" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="75000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="7" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="90000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="8" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="105000" duration="5000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Gold"
				xmlPath="boss" startTime="105000" duration="1000" num="0" />
		</Wave>
		<Wave waveCount="9" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="120000" duration="5000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="10" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="139000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="11" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="150000" duration="10000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="12" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="165000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="13" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="180000" duration="10000" num="6"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="14" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="195000" duration="10000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="15" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="210000" duration="5000" num="7"
				isFallDown="0" />
			
		</Wave>
		<Wave waveCount="16" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="225000" duration="10000" num="10"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.Level39.Castellan"
				xmlPath="boss" startTime="225000" duration="1000" num="0" />
		</Wave>
		<Wave waveCount="17" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="240000" duration="10000" num="5"
				isFallDown="0" />
			
		</Wave>
		<Wave waveCount="18" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="255000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="19" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="270000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>

		<Wave waveCount="20" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="285000" duration="10000" num="8"
				isFallDown="0" />
		
		</Wave>
		<Wave waveCount="21" totalEnemyNum="1" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.Level39.StoneXiaobing"
				xmlPath="xiaoBing" startTime="300000" duration="10000" num="10"
				isFallDown="0" />


		</Wave>
	</Waves>
	<EqDrop>
		<xiaoBing noDropProWeight="200">
			<!--proWeight 概率权重 -->

		   <!-- 神彩石-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Shencaishi_S"
				proWeight="2" />
			<!-- 众神之印-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Zhongshenzhiyin_S"
				proWeight="3" />
			
			<!-- 命中宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hit5_S"	
				proWeight="3" />
				<!-- 防爆宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Riot5"	
				proWeight="3" />
			<!-- 神印碎片-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYinsuipian_S"
				proWeight="3" />
			<!-- 神域冥火 -->	
			<item dropClassName="UI.Equipments.StackEquipments.Material_Shenyuminghuo_S"
				proWeight="7" />
			<!-- 蓝宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp5_S"
				proWeight="12" />
			<!-- 人品宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp5_S"
				proWeight="10" />
			<!-- 攻击宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack5_S"
				proWeight="3" />
			<!-- 生命宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp5_S"
				proWeight="11" />
			
			<!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence5_S"
				proWeight="12" />
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence4_S"
				proWeight="2" />
			<!-- 开孔灵符 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S"
				proWeight="12" /> 
			<!-- 碎石锤 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S"
				proWeight="6" />

			<!-- 红药 -->
			<item dropClassName="Item_HpUp" proWeight="100" />
			<!-- 蓝药 -->
			<item dropClassName="Item_MpUp" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_10000" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_50000" proWeight="50" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_100000" proWeight="5" />
			<!-- 灵兽石 -->
			<item dropClassName="Item_StrengthenNum_10" proWeight="5" />

			<!-- 圣灵精华 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S"
				proWeight="15" />
			<!-- 一级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S"
				proWeight="13" />
			<!-- 二级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_TwoFire_S"
				proWeight="8" />
			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="8" />
			<!-- 幸运宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S"
				proWeight="18" />
			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="6" />


		</xiaoBing>
		<boss noDropProWeight="100">
			<!--proWeight 概率权重 -->
			<dropNumData>
				<smallDropNumData proWeight="10">
					<numData num="1" proWeight="5" />
					<numData num="2" proweight="5" />
				</smallDropNumData>
				<bigDropNumData proWeight="1">
					<numData num="4" proWeight="8" />
					<numData num="5" proWeight="2" />
				</bigDropNumData>
			</dropNumData>

			<!-- 神彩石-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Shencaishi_S"
				proWeight="3" />
			<!-- 命中宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hit5_S"	
				proWeight="3" />
				<!-- 防爆宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Riot5"	
				proWeight="3" />
			 <!-- 众神之印-->
			<item dropClassName="UI.Equipments.SceneEquipments.Scroll_Yuhuafeisheng_S"
				proWeight="2" />
			<!-- 升灵丸模具-->
			<item dropClassName="UI.Equipments.SceneEquipments.Scroll_Yuhuafeisheng_S"
				proWeight="1" />
		<!-- 魔龙-->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_Molong_S"
				proWeight="2" />
			<!-- 哪吒模具 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Scroll_Nezha3_S"
				proWeight="1" />
            <!-- 圣域冥火 -->
			<item dropClassName="UI.Equipments.StackEquipments.Material_Shenyuminghuo_S"
				proWeight="120" />
			<!-- 多闻碎片 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Duowen3_S"
				proWeight="1" />
			<!-- 攻击石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack6_S"
				proWeight="60" />
	      <!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence6_S"
				proWeight="50" />

			<!-- 凤凰蛋 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_FengHuang_S"
				proWeight="10" />
            	<!--生命宝石 -->
				<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp6_S"
				proWeight="40" />	

			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="20" />

			<!--防爆宝石模具 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Scroll_InsetGem124_S"
				proWeight="5" />


		</boss>

	</EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>

	</sharedAnimationDefinitions>
	<xiaoBing>
		<!--敌人数据 -->
		<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
			<animationDefinition id="xiaoBingFallDownShow1"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</fallDownEffect>
		<bombInfo speed="-30" time="5" normalDead="true" />
		<enemyData>
			<data att="totalHp" value="7600000" />
			<data att="attack" value="26000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="10000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="3.0" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.6" />
			<data att="hitRate" value="1.18" />
		</enemyData>
		<value>880000</value>   <!-- 石甲拥有的生命值 -->
		<skillCd1>3</skillCd1> <!-- 有石甲时技能冷却时间 5秒 -->
		<skillCd2>3</skillCd2> <!-- 没有石甲时技能冷却时间 2秒-->
		<!--移动速度以秒为单位 -->
		<animal id="enemy39" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="60" bodyHeight="110" walkSpeed="40"
			runSpeed="0">
		<isFly>false</isFly><!-- 是否是天上飞的怪物 -->
        <notBePushed>true</notBePushed><!--不能被推 -->
        <notBePushedBySkill3>true</notBePushedBySkill3><!--不能被悟空的技能3推 -->
			<attackRange x="0" y="-30" z="-1" xRange="70" yRange="60"
				zRange="100" />
			<idle defId="walk_enemy39" />
			<walk defId="walk_enemy39" />
			<run defId="walk_enemy39" />
			<attack defId="attack_enemy39" />
			<hurt defId="hurt_enemy39" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>
			<die defId="die_enemy39" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow1" />
			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<skill id="xiaobingskill1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="-30" z="-1" xRange="200" yRange="100" zRange="50" attackInterval="500"
				bodyDefId="Enemy39_1_skill1" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skill1End^stop^"
				 bodySkillEndFrameLabel="skill1End^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="xiaobingskill2" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="-30" z="-1" xRange="200" yRange="100" zRange="50" attackInterval="500"
				bodyDefId="Enemy39_2_skill1" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skill2End^stop^"
				 bodySkillEndFrameLabel="skill2End^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			 <skill id="Change" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="-1" xRange="0" yRange="0" zRange="0" attackInterval="0"
				bodyDefId="Change" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="changeEnd^stop^"
				 bodySkillEndFrameLabel="changeEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<animationDefinitions>
				<animationDefinition id="walk_enemy39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
						showClass="Enemy39_1_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_enemy39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
						showClass="Enemy39_1_hurt" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
						showClass="Enemy39_1_attack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
						showClass="Enemy39_1_die" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="Enemy39_1_skill1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
						showClass="Enemy39_1_skill1" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="Enemy39_2_skill1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
						showClass="Enemy39_2_skill1" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="Change" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="enemyFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="xiaoBingFallDownShow1"
					rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="FallDownEffect" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
			<shows>
				<show defId="walk_enemy39" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
					showClass="Enemy39_2_walk" x_offset="0" y_offset="0" />
				<show defId="hurt_enemy39" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
					showClass="Enemy39_2_hurt" x_offset="0" y_offset="0" />
				<show defId="die_enemy39" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
					showClass="Enemy39_2_die" x_offset="0" y_offset="0" />
				<show defId="attack_enemy39" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
					showClass="Enemy39_2_attack" x_offset="0" y_offset="0" />
				<show defId="Enemy39_2_skill1" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Enemy.swf"
					showClass="Enemy39_2_skill1" x_offset="0" y_offset="0" />
			</shows>
		</animal>
	</xiaoBing>


	<boss>
		<!--防止刚好到达大招停止时间帧时被迷阵干扰导致世界时间一直停止，所以进行大招检测 -->
		<screenskills>
		<screenskill>10101000</screenskill>
		<screenskill>10101100</screenskill>
		<screenskill>10101200</screenskill>
		<screenskill>10101300</screenskill>
		
		<screenskill>10102004</screenskill>
		<screenskill>10102104</screenskill>
		<screenskill>10102204</screenskill>
		<screenskill>10102304</screenskill>
		
		<screenskill>10104004</screenskill>
		<screenskill>10104104</screenskill>
		<screenskill>10104204</screenskill>
		<screenskill>10104304</screenskill>
		
		<screenskill>10105004</screenskill>
		<screenskill>10105104</screenskill>
		<screenskill>10105204</screenskill>
		<screenskill>10105304</screenskill>
		
		<screenskill>10106004</screenskill>
		<screenskill>10106104</screenskill>
		<screenskill>10106204</screenskill>
		<screenskill>10106304</screenskill>
		
		<screenskill>10107004</screenskill>
		<screenskill>10107104</screenskill>
		<screenskill>10107204</screenskill>
		<screenskill>10107304</screenskill>

		<screenskill>10109004</screenskill>
		<screenskill>10109104</screenskill>
		<screenskill>10109204</screenskill>
		<screenskill>10109304</screenskill>
	</screenskills>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="12600000" />
			<data att="attack" value="28000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="16000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="3.0" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.6" />
			<data att="hitRate" value="1.25" />
		</enemyData>

		<playerLostTime>5</playerLostTime> <!-- 人物失去控制3秒 -->
		<autoPetLostTime>5</autoPetLostTime> <!-- 妖将失去控制3秒 -->
		<petLostTime>5</petLostTime> <!-- 宠物失去控制3秒 -->
		<xiaobingAddTime>2</xiaobingAddTime> <!-- 小兵加buf时间3秒 -->
		<attack>26000</attack> <!-- 原始小怪的攻击力需和上面小怪攻击力相同 -->
        <addAttack>10000</addAttack> <!-- 增加攻击力 -->
        <addMoveSpeed>40</addMoveSpeed><!-- 增加移动速度 -->
        <vortexHurt>8000</vortexHurt><!-- 漩涡造成的伤害 直接扣血，防止因为攻击力过高导致玩家直接死亡，参数值可以小点 -->
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		
		 <!-- （boss加入世界中就默认第一次释放迷阵）指定波次序列boss释放迷阵，boss进入波次当中后不再释放，所以得小于bossWave -->
         <waves>
			<wave>4</wave> <!-- 567因为波次是直接出现，最好不填567 -->
			<wave>9</wave>
			<wave>13</wave>
			<wave>17</wave>
		</waves>
		<bossWave>19</bossWave>  <!-- boss进入波次序列当中 -->
		<animal id="boss39" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="180" bodyHeight="200" walkSpeed="50"
			runSpeed="200">
			<notShowBeattack>false</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->
			<notBePushedBySkill3>true</notBePushedBySkill3><!--不能被悟空的技能3推 -->
			<attackRange x="-30" y="-30" z="-1" xRange="180" yRange="60"
				zRange="100" />
			<idle defId="idle_boss39" />
			<walk defId="walk_boss39" />
			<run defId="walk_boss39" />
			<attack defId="attack_boss39" />
			<hurt defId="hurt_boss39" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>
			<die defId="die_boss39" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />

			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			<skill id="boss_skill1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation1" hurtDuration="0" skillAttackReachFrameLabel="skill1Reach" skillEndFrameLabel="skill1End^stop^"
				 bodySkillEndFrameLabel="skill1End^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="Skill2_Castellan" className="YJFY.LevelMode1.Level39.Skill2_Castellan" dontAttacktangsheng="true"
				x="-160" y="-100" z="0" xRange="360" yRange="200" zRange="500" attackInterval="500"
				bodyDefId="skillAnimation2" hurtDuration="0" skillAttackReachFrameLabel="skill2Reach" skillEndFrameLabel="skill2End^stop^"
				 bodySkillEndFrameLabel="skill2End^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
				 <animationDefinition id="skillAnimation2" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_skill2" x_offset="0" y_offset="0" />
				</animationDefinition>
				 <vortexNum>4</vortexNum><!-- 漩涡个数 -->
			</skill>
			<skill id="boss_skill3" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation3" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skill3End^stop^"
				 bodySkillEndFrameLabel="skill3End^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="boss_skill4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skill4End^stop^"
				 bodySkillEndFrameLabel="skill4End^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="idle_boss39" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="idle_boss39" hurtDuration="2500" skillAttackReachFrameLabel="idleReach" skillEndFrameLabel="idleEnd^stop^"
				 bodySkillEndFrameLabel="idleEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>



			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>

			<animationDefinitions>
			    <animationDefinition id="changeStart" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
					showClass="ChangeStart" x_offset="0" y_offset="0" />
			    </animationDefinition>
			    <animationDefinition id="addBuf" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
					showClass="AddBuf" x_offset="0" y_offset="0" />
			    </animationDefinition>
				<animationDefinition id="idle_boss39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_idle" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_boss39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_boss39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_hurt" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_attack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss39" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_die" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_skill1" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation2" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_skill2" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation3" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_skill3" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level39/Boss.swf"
						showClass="Boss39_skill4" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</boss>
</data>
