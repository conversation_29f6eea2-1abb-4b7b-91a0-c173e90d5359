 逆转灵符基础属性
		
        <addAttr addAttName="addrenpin"  属性="人品" 基础值="2" 最小增长值="0.5" 最大增长值="1.7" 
		<addAttr addAttName="addmagic"    属性="蓝量" 基础值="3" 最小增长值="0.3" 最大增长值="1.5" 
		<addAttr addAttName="addhp"       属性="血量" 基础值="2" 最小增长值="0.1" 最大增长值="1.4" 
		<addAttr addAttName="addfangbao"  属性="防爆" 基础值="2" 最小增长值="0.2" 最大增长值="1.1" 
		<addAttr addAttName="addshanbi"   属性="闪避" 基础值="3" 最小增长值="0.5" 最大增长值="2"   
		<addAttr addAttName="addbaoji"    属性="暴击" 基础值="4" 最小增长值="0.3" 最大增长值="1.3" 
		<addAttr addAttName="addgongji"   属性="攻击" 基础值="3" 最小增长值="1"   最大增长值="2"
        
		
		
		
	特殊属性	
		
		 


		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr 属性="doubleexpgold" 基础值="2" avgValue="0" 最小值="2" 最大值="2"  
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr 属性="increaseAttack" 基础值="20" avgValue="100" 最小值="10" 最大值="20" 
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr 属性="increaseShanbi" 基础值="1" avgValue="1000" 最小值="0.2" 最大值="1.5" 
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr 属性="dincreasehp" 基础值="1" avgValue="0" 最小值="1" 最大值="5" 
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr 属性="dincreaseDef" 基础值="50" avgValue="0" 最小值="10" 最大值="50" 
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr 属性="increaseBaoji" 基础值="1" avgValue="1000" 最小值="0.5" 最大值="1.6" 
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr 属性="changerenpin" 基础值="0" avgValue="0" 最小值="10" 最大值="10" 
		
		<!-- 基础：增加人品值10%，成长值从minupgrade和maxupgrade中随机 -->
		<addAttr addAttName="addrenpin" addAttValue="3" minupgrade="0.5" maxupgrade="1.5" weight="8" totalweight="100"/>  <!-- 人品 -->
		<addAttr addAttName="addmagic" addAttValue="5" minupgrade="0.3" maxupgrade="1.2" weight="10" totalweight="100"/>   <!-- 蓝 -->
		<addAttr addAttName="addhp" addAttValue="3" minupgrade="0.1" maxupgrade="2" weight="6" totalweight="100"/>        <!-- 血量 -->
		<addAttr addAttName="addfangbao" addAttValue="1" minupgrade="0.8" maxupgrade="1.5" weight="7" totalweight="100"/>   <!-- 防爆 -->
		<addAttr addAttName="addshanbi" addAttValue="2" minupgrade="0.5" maxupgrade="2" weight="5" totalweight="100"/>     <!-- 闪避 -->
		<addAttr addAttName="addbaoji" addAttValue="3" minupgrade="0.3" maxupgrade="1.2" weight="0" totalweight="100"/>     <!-- 暴击 -->
		<addAttr addAttName="addmingzhong" addAttValue="3" minupgrade="0.5" maxupgrade="2" weight="1" totalweight="100"/>   <!-- 命中 -->
		<addAttr addAttName="addgongji" addAttValue="2" minupgrade="1" maxupgrade="1.5" weight="1" totalweight="100"/>       <!-- 攻击 --> 

		<!-- 特殊属性随机个数的权重算法 -->
		<saddWeight value="1" weightmin="1" weightmax="100" totalweight="150"/>
		<saddWeight value="2" weightmin="101" weightmax="140" totalweight="150"/>
		<saddWeight value="3" weightmin="141" weightmax="150" totalweight="150"/>
     
		<!-- 特殊属性: 2倍经验和金钱 -->
		<sAddAttr addAttName="doubleexpgold" addAttValue="2" avgValue="0" minvalue="2" maxvalue="2" weight="5" totalweight="100"/> 
		<!-- 特殊属性: 每100个魔法值增加20个攻击点 -->
		<sAddAttr addAttName="increaseAttack" addAttValue="20" avgValue="100" minvalue="10" maxvalue="30" weight="3" totalweight="100"/>
		<!-- 特殊属性: 每1000个防御值增加1个闪避值 -->
		<sAddAttr addAttName="increaseShanbi" addAttValue="1" avgValue="1000" minvalue="0.2" maxvalue="2" weight="8" totalweight="100"/>
     
		<!-- 特殊属性: 每次进行普攻和释放技能的时候降低百分之一的血量 -->
		<sAddAttr addAttName="dincreasehp" addAttValue="1" avgValue="0" minvalue="1" maxvalue="3" weight="0" totalweight="100"/>
		<!-- 特殊属性: 防御力减半 -->
		<sAddAttr addAttName="dincreaseDef" addAttValue="50" avgValue="0" minvalue="10" maxvalue="30" weight="0" totalweight="100"/>
		<!-- 特殊属性: 每1000个攻击力增加1个暴击点 -->
		<sAddAttr addAttName="increaseBaoji" addAttValue="1" avgValue="1000" minvalue="0.5" maxvalue="2" weight="1" totalweight="100"/>
		<!-- 特殊属性: 将人品值转换为防御力 -->
		<sAddAttr addAttName="changerenpin" addAttValue="0" avgValue="0" minvalue="10" maxvalue="10" weight="8" totalweight="100"/>
		
		
		
