<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet11" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet11Idle" />
		<walk defId="pet11Walk" />
		<run defId="pet11Run" />
		<attack defId="pet11Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet11Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet11Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	   <skill id="Skill_Pet11Skill" className="YJFY.Skill.PetSkills.Skill_Pet11Skill" x="0"
			y="-100" z="-1" xRange="800" yRange="200" zRange="100" bodyDefId="pet11SkillBodyShow"  hurtDuration="2000"
			 disappearBodyId="pet11SkillBodyDisAppear" releaseSkillFrameLabel="releaseSkill^stop^"
			 skillAttackReachFrameLabel="skillAttackReach" replaceFrameLabel="replace" skillEndFrameLabel="skillEnd^stop^">
			
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet11Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet11All.swf"
					showClass="PetStand11_4" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet11Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet11All.swf"
					showClass="PetWalk11_4" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet11SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet11All.swf"
					showClass="PetSkill11AttackAnimation" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet11SkillBodyDisAppear" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet11All.swf"
					showClass="PetSkill11LinkDisapppear" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!-- 不化装-->
		    
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			
		</shows>

	</animal>
</data>
