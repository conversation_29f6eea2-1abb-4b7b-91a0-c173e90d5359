<?xml version="1.0" encoding="utf-8" ?>
<data id="level2" swfPath="NewGameFolder/LevelMode2/Level1/Level2.swf"
	className="LevelMap" x="0" y="0" z="0" xRange="1920" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf" className="SoundHY" />
	<startShow swfPath="NewGameFolder/LevelMode2/Level1/StartShow2.swf" className="StartShow" />
	<Waves totalWaveNum="5">

		<!-- 第一屏 -->
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="5000" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="5000" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="5000" num="1"  />
		</Wave>
		
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="3000" duration="5000" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="3000" duration="5000" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="3000" duration="5000" num="1"  />
		</Wave>
		
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="5000" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="5000" num="1"  />
		</Wave>		
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="5000" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="6000" duration="5000" num="1"  />
		</Wave>
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="9000" duration="5000" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="9000" duration="5000" num="1"  />
		</Wave>		
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="9000" duration="5000" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="900" y="0" xRange="100" yRange="400" cameraX="0">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="9000" duration="5000" num="1"  />
		</Wave>

		
		<!-- 第二屏 -->

		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		
		

		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="3000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="3000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="3000" duration="2500" num="1"  />
		</Wave>
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="300" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="6000" duration="2500" num="1"  />
		</Wave>	
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="9000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="9000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="9000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="9000" duration="2500" num="1"  />
		</Wave>	
		<Wave waveCount="1" totalEnemyNum="20" x="0" y="0" xRange="100" yRange="400" cameraX="360">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="boss1" startTime="9000" duration="5000" num="1"  />
		</Wave> 		
		<!-- 第三屏 -->
		

		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="0" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="0" duration="2500" num="1"  />
		</Wave>
		
		

		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="3000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="3000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="3000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="3000" duration="2500" num="1"  />
		</Wave>
		
		
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing1" startTime="6000" duration="2500" num="1"  />
		</Wave>
		<Wave waveCount="1" totalEnemyNum="20" x="960" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.XiaoBing" xmlPath="xiaoBing2" startTime="6000" duration="2500" num="1"  />
		</Wave>



		<Wave waveCount="1" totalEnemyNum="20" x="1920" y="0" xRange="100" yRange="400" cameraX="960">
			<Enemy enemyClass="YJFY.LevelMode2.Boss1" xmlPath="boss2" startTime="2000" duration="1000" num="1"  />
		</Wave> 
		
	</Waves>
 <!--抽奖配置-->
	<lottery>
		<!-- 只能12个-->
		<!-- 龙王契约碎片 -->
		<item id="10500063" num="1" proWeight="2" />
		<!-- 鲛人将军契约碎片 -->
		<item id="10500062" num="1" proWeight="5" />
		<!-- 妖将契约（虾兵） -->
        <item id="12000000" num="1" proWeight="20" />
        <!-- 妖将契约（龟丞相 ）-->
        <item id="12000001" num="1" proWeight="20" />
        <!-- 圣灵精华 -->
        <item id="10500035" num="1" proWeight="2" />
        <!-- 宠物训练卡 -->
        <item id="11000009" num="1" proWeight="2" />
       <!-- 进阶丹 -->
        <item id="10500064" num="1" proWeight="3" />
        <!-- 内丹-->
        <item id="10500065" num="1" proWeight="3" />
        <!-- 幸运宝石 -->
        <item id="10500000" num="4" proWeight="5" />
        <!--  金袋 -->
        <item id="11100000" num="1" proWeight="5" />
        <!-- 超进化仙果 -->
        <item id="10500030" num="1" proWeight="3" />
        <!-- 神秘圣诞宠物蛋-->
        <item id="10800008" num="1" proWeight="3" />


	</lottery>
    <EqDrop>
   <xiaoBing noDropProWeight="500">
		   <!--proWeight 概率权重-->
	      <!-- 鲛人碎片-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LanSe_S" proWeight="5" />	
 		  <!-- 龙王碎片-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ZiSe_S" proWeight="2" />			   
		  <!-- 无敌药水-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="2" />		 
		  <!-- 火剑蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Jian_S" proWeight="2" />		 
		  <!-- 龙蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Tyrannosaurs_S" proWeight="1" />		 	
		  <!-- 石头蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_ShiTouRen_S" proWeight="1" />		 	
   	       
		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp1_S" proWeight="10" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp1_S" proWeight="10" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack1_S" proWeight="10" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp1_S" proWeight="10" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence1_S" proWeight="10" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="10" />   
		   
		  <!-- 地狱灵芝 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LingZhi_S" proWeight="2" />		 
		  <!-- 瑶池圣莲 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Flower_S" proWeight="2" />		 
   	      <!-- 龙胆果 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_GuoZi_S" proWeight="1" />

  	      <!--  红药 -->   
   	      <item dropClassName="Item_HpUp" proWeight="50" />
   	      <!--  金币 -->  
   	      <item dropClassName="Item_MoneyUp" proWeight="50" />
   	      <!--  蓝药 -->  
   	      <item dropClassName="Item_MpUp" proWeight="100" /> 	      	        


   	      
 

     

   	      <!-- 羽毛 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_SuperLu_1_S" proWeight="2" />
   	      <!-- 圣灵精华 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S" proWeight="2" />
   	      <!-- 一级火 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S" proWeight="2" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="2" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="1" />
   	      <!-- 高级升级宝石模具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="1" />
   	          
	  </xiaoBing>
	   <boss noDropProWeight="20">
		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="5" />
				   <numData num="2" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="4" proWeight="8" />
				   <numData num="5" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>		  
	      <!-- 鲛人碎片-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_LanSe_S" proWeight="5" />	
 		  <!-- 龙王碎片-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ZiSe_S" proWeight="2" />	
   	      
		  <!-- 无敌药水-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Potion_WuDiYaoShui_S" proWeight="2" />		 
		  <!-- 幸运蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_CaiDan_S" proWeight="2" />		 
		  <!-- 麋鹿蛋 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Egg_Lu_S" proWeight="1" />		 	
   	       
		  <!-- 蓝宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp1_S" proWeight="10" />		 
   	      <!-- 人品宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp1_S" proWeight="10" />
   	      <!-- 攻击宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack1_S" proWeight="10" />   
   	      <!-- 生命宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp1_S" proWeight="10" />   
   	      <!-- 防御宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence1_S" proWeight="10" />   
   	      <!-- 开孔灵符 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S" proWeight="10" /> 
   	      <!-- 碎石锤 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S" proWeight="10" /> 
   	      
   	        
   	      <!-- 高级防御书 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.PetSkillBook_6" proWeight="1" />   
		   

   	      	         	      
<!--   	       红药   
   	      <item dropClassName="Item_HpUp" proWeight="100" />
   	       金币  
   	      <item dropClassName="Item_MoneyUp" proWeight="300" />
   	       蓝药  
   	      <item dropClassName="Item_MpUp" proWeight="100" /> 
  -->

     

   	      <!-- 黑羽毛 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_SuperLu_1_S" proWeight="10" />
   	      <!-- 圣灵精华 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S" proWeight="2" />
   	      <!-- 一级火 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_OneFire_S" proWeight="10" />
   	      <!-- 升级宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.UpgradeGem_S" proWeight="10" />
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="10" />
   	      <!-- 高级升级宝石模具 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_UpgradeGem2_S" proWeight="1" />
	   </boss>
	   
   </EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
         
	</sharedAnimationDefinitions>
	
   <xiaoBing1>
	<!--敌人数据 -->
	  <hpShowData frameLabel="smallShrimp" />
	  <hurtAnimation2 defId="hurt2_enemy" playFrameLabel="1" recoverFrameLabel="recover^stop^" />
	  <enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="3000" />
		<data att="unableAttackMaxInterval" value="6000" />
	</enemyAttackData>
	<enemyData>
	     
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
		<data att="totalHp" value="60000" />
		<data att="attack" value="1400" />
		<data att="expOfDieThisEnemy" value="65000" />
		<data att="defence" value="30" />
		<data att="dogdeRate" value="0.3" />
		<data att="criticalRate" value="0.45" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0.45" />
		<data att="hitRate" value="0.12" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy1" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="60" bodyHeight="80" walkSpeed="50"
		runSpeed="100">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-25" z="-1" xRange="50" yRange="50"
			zRange="100" />

		<idle defId="idle_enemy" />
		<walk defId="walk_enemy" />
		<run defId="run_enemy" />
		<attack defId="attack_enemy" />
		<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>
		<hurt2 defId="hurt2_enemy" />

		<die defId="die_enemy" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="idle_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="IdleOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="WalkOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
            <animationDefinition id="run_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="RunOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="Hurt1OfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="Hurt2OfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="AttackOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="DieOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="ShadowOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
		</animationDefinitions>



	</animal>
</xiaoBing1>
   <xiaoBing2>
	<!--敌人数据 -->
	<hpShowData frameLabel="tortoise" />
	<hurtAnimation2 defId="hurt2_enemy" playFrameLabel="1" recoverFrameLabel="recover^stop^"/>
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="3000" />
		<data att="unableAttackMaxInterval" value="6000" />
	</enemyAttackData>
	<enemyData>
	      
	<!--
	totalHp=血量  attack=攻击  expOfDieThisEnemy=经验  defence=防御  dogdeRate=闪避  criticalRate=暴击率  criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中
		-->
		<data att="totalHp" value="65000" />
		<data att="attack" value="1500" />
		<data att="expOfDieThisEnemy" value="65000" />
		<data att="defence" value="30" />
	    <data att="dogdeRate" value="0.3" />
		<data att="criticalRate" value="0.4" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0.4" />
		<data att="hitRate" value="0.1" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="enemy2" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="60" bodyHeight="80" walkSpeed="35"
		runSpeed="70">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->




		 

		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-50" z="-1" xRange="400" yRange="100"
			zRange="100" />

		<idle defId="idle_enemy" />
		<walk defId="walk_enemy" />
		<run defId="run_enemy" />
		<attack defId="attack_enemy" />
		<attackEffect defId="enemyAttackEffect" />
		<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_enemy" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow" />

		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>

			<animationDefinition id="idle_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="IdleOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="WalkOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
            <animationDefinition id="run_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="RunOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="Hurt1OfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="Hurt2OfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="AttackOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="DieOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>



			<animationDefinition id="enemyFootShadow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="ShadowOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="enemyAttackEffect" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/Tortoise.swf"
					showClass="AttackEffectOfTortoise" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</xiaoBing2>
<boss1 >
	<!--敌人数据 -->
	<hpShowData frameLabel="jiaoRen" />
   <bossData hpSegment="1000"> <!--用于血条显示，一条的容量-->
		<skill skillId="bossSkill1" hurtMulti="2" costMp="80" cdTime="3000"   isInvincibleInRun="1" isAbleRunInHurt="1"
		className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
	</bossData>
	<hurtAnimation2 defId="hurt2_boss" playFrameLabel="1" recoverFrameLabel="recover^stop^"/>
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="1000" />
		<data att="unableAttackMaxInterval" value="3000" />
	</enemyAttackData>
	<enemyData>
		 
		 
		<data att="totalHp" value="200000" />
		<data att="attack" value="1700" />
		<data att="expOfDieThisEnemy" value="300000" />
		<data att="defence" value="20" />
		<data att="dogdeRate" value="0.3" />
		<data att="criticalRate" value="0.2" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0.5" />
		<data att="hitRate" value="0.09" />
		
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="2" />
	</enemyData>
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="130" bodyHeight="120" walkSpeed="60"
		runSpeed="120">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-50" z="-1" xRange="200" yRange="100" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt1_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    
		<skill id="bossSkill1" className="YJFY.LevelMode2.Levels1.Skill_JiaoRenSkill" x="-300"
			y="-100" z="-1" xRange="750" yRange="200" zRange="200" bodyDefId="skill1Show" skillAttackReachFrameLabel="skillReach"
			skillEndFrameLabel="skillEnd^stop^">
			<moveData swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf" className="MoveDataOfSkill" />
		</skill>
		
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf"
					showClass="IdleOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf"
					showClass="IdleOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf" 
				showClass="RunOfJiaoRen" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt1_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf"
					showClass="Hurt1OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf"
					showClass="Hurt2OfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf"
					showClass="AttackOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf"
					showClass="DieOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<animationDefinition id="skill1Show" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf"
					showClass="SkillShowOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/JiaoRen.swf"
					showClass="ShadowOfJiaoRen" x_offset="0" y_offset="0" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss1>
<boss2 >
	<!--敌人数据 -->
	<hpShowData frameLabel="dragonKing" />
   <bossData hpSegment="1000"> <!--用于血条显示，一条的容量-->
		<skill skillId="bossSkill1" hurtMulti="0" costMp="20" cdTime="5000" priorityForRun="0" priorityForRunInHurt="1"  
		     isInvincibleInRun="1" isAbleRunInHurt="1"className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> <!--cdTime 毫秒-->
		<skill skillId="bossSkill2" hurtMulti="0" costMp="70" cdTime="13000" priorityForRun="1" priorityForRunInHurt="0" 
		     isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO"/> <!--cdTime 毫秒-->
	</bossData>
	<hurtAnimation2 defId="hurt2_boss" playFrameLabel="1" recoverFrameLabel="recover^stop^"/>
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="1000" />
		<data att="unableAttackMaxInterval" value="3000" />
	</enemyAttackData>
	<enemyData>
		 
		 
		<data att="totalHp" value="500000" />
		<data att="attack" value="1600" />
		<data att="expOfDieThisEnemy" value="800000" />
		<data att="defence" value="20" />
		<data att="dogdeRate" value="0.4" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="2" />
		<data att="deCriticalRate" value="0.6" />
		<data att="hitRate" value="0.1" />
		
		
		<data att="totalMp" value="100" />
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="5" />
	</enemyData>
	<animal id="boss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="100" bodyHeight="140" walkSpeed="70"
		runSpeed="140">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-50" z="-1" xRange="120" yRange="100" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt1_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    
		<skill id="bossSkill1" className="YJFY.LevelMode2.Levels1.Skill_DragonKingSkill1" x="-300"
			y="-100" z="-1" xRange="600" yRange="200" zRange="200" bodyDefId="skill1Show" skillAttackReachFrameLabel="skillReach"
			skillEndFrameLabel="skillEnd^stop^" skillAttackEffectDefId="bossAttackEffect">
			<moveData swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf" className="MoveDataOfSkill" />
			<!--攻击特效 -->
			<animationDefinition id="bossAttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="AttackEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
	
		<skill id="bossSkill2" className="YJFY.LevelMode2.Levels1.Skill_DragonKingSkill2" x="-300" bodyDefId="skill2Show"
			y="-300" z="-1" xRange="600" yRange="600" zRange="1000" attackInterval="500">
			
		</skill>
		
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="IdleOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="WalkOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf" 
				showClass="RunOfDragonKing" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt1_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="Hurt1OfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="Hurt2OfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="AttackOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="DieOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<animationDefinition id="skill1Show" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="Skill1ShowOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skill2Show" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="Skill2ShowOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="ShadowOfDragonKing" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<!--攻击特效 -->
			<animationDefinition id="bossAttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/DragonKing.swf"
					showClass="AttackEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
			
		</animationDefinitions>



	</animal>
</boss2>
</data>
