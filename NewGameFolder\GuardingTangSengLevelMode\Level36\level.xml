<?xml version="1.0" encoding="utf-8" ?>
<data id="Level36"
	swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/level.swf"
	className="LevelMap36" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
		swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf"
		className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
	<Waves totalWaveNum="21">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="2000" duration="5000" num="8" isFallDown="0" />

		</Wave>
		<Wave waveCount="2" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="15000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="3" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="30000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="4" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="45000" duration="50000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="5" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="60000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="6" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="75000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="7" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="90000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="8" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="105000" duration="5000" num="5"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill"
				xmlPath="boss" startTime="105000" duration="1000" num="0" />
		</Wave>
		<Wave waveCount="9" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="120000" duration="5000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="10" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="135000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="11" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="150000" duration="10000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="12" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="165000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="13" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="180000" duration="10000" num="6"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="14" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="195000" duration="10000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="15" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="210000" duration="5000" num="7"
				isFallDown="0" />
			
		</Wave>
		<Wave waveCount="16" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="225000" duration="10000" num="10"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill"
				xmlPath="boss" startTime="225000" duration="1000" num="1" />
		</Wave>
		<Wave waveCount="17" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="240000" duration="10000" num="5"
				isFallDown="0" />
			
		</Wave>
		<Wave waveCount="18" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="255000" duration="5000" num="8"
				isFallDown="0" />
		</Wave>
		<Wave waveCount="19" totalEnemyNum="1" x="500" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="270000" duration="10000" num="7"
				isFallDown="0" />
		</Wave>

		<Wave waveCount="20" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="285000" duration="10000" num="8"
				isFallDown="0" />
		
		</Wave>
		<Wave waveCount="21" totalEnemyNum="1" x="950" y="0" xRange="10"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Enemy_AddAttack"
				xmlPath="xiaoBing" startTime="300000" duration="10000" num="10"
				isFallDown="0" />


		</Wave>
	</Waves>
	<EqDrop>
		<xiaoBing noDropProWeight="500">
			<!--proWeight 概率权重 -->

		 <!-- 四方石-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Sifangshi_S"
				proWeight="2" />
				
			
				<!-- 灵印碎片-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_LingYinsuipian_S"
				proWeight="2" />

			
			
			<!-- 清晨之辉-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Qingchenzhihui_S"
				proWeight="2" />

			<!-- 蓝宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp4_S"
				proWeight="12" />
			<!-- 人品宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp4_S"
				proWeight="10" />
			<!-- 攻击宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack4_S"
				proWeight="3" />
			<!-- 生命宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp3_S"
				proWeight="11" />
			
			<!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence3_S"
				proWeight="12" />
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence4_S"
				proWeight="2" />
			<!-- 开孔灵符 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S"
				proWeight="12" /> 
			<!-- 碎石锤 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S"
				proWeight="6" />

			<!-- 红药 -->
			<item dropClassName="Item_HpUp" proWeight="100" />
			<!-- 蓝药 -->
			<item dropClassName="Item_MpUp" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_10000" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_50000" proWeight="50" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_100000" proWeight="5" />
			<!-- 灵兽石 -->
			<item dropClassName="Item_StrengthenNum_10" proWeight="5" />

			<!-- 圣灵精华 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShengLingJingHua_S"
				proWeight="15" />
			<!-- 幸运宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S"
				proWeight="18" />
			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="6" />


		</xiaoBing>
		<boss noDropProWeight="100">
			<!--proWeight 概率权重 -->
			<dropNumData>
				<smallDropNumData proWeight="10">
					<numData num="1" proWeight="5" />
					<numData num="2" proweight="5" />
				</smallDropNumData>
				<bigDropNumData proWeight="1">
					<numData num="4" proWeight="8" />
					<numData num="5" proWeight="2" />
				</bigDropNumData>
			</dropNumData>


			 <!-- 四方石-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Sifangshi_S"
				proWeight="2" />
				
		    <!-- 仙魔精魄-->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Xianmojingpo_S"
				proWeight="2" />
			
			<!-- 魔龙-->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_Molong_S"
				proWeight="2" />
			<!-- 哪吒模具 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Scroll_Nezha3_S"
				proWeight="1" />
            <!-- 圣域冥火 -->
			<item dropClassName="UI.Equipments.StackEquipments.Material_Shenyuminghuo_S"
				proWeight="120" />
			<!-- 多闻碎片 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Nezha3_S"
				proWeight="1" />
			<!-- 攻击石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack5_S"
				proWeight="60" />
	      <!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence5_S"
				proWeight="50" />

			<!-- 凤凰蛋 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Egg_FengHuang_S"
				proWeight="10" />
            	<!--生命宝石 -->
				<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp5_S"
				proWeight="40" />	

			<!-- 三级火 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ThreeFire_S"
				proWeight="20" />

			<!--深渊宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ShenYuan_S"
				proWeight="40" />


		</boss>

	</EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>

	</sharedAnimationDefinitions>

	<xiaoBing>
		<!--敌人数据 -->
		<fallDownEffect fallDownEffectId="xiaoBingFallDownShow1">
			<animationDefinition id="xiaoBingFallDownShow1"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="FallDownEffect" x_offset="0" y_offset="0" />
			</animationDefinition>
		</fallDownEffect>
		<bombInfo speed="-30" time="5" normalDead="true" />
		<enemyData>

			<!-- totalHp=血量 attack=攻击 expOfDieThisEnemy=经验 defence=防御 dogdeRate=闪避 
				criticalRate=暴击率 criticalMuti=暴击倍数 deCriticalRate=防爆 hitRate=命中 -->
			<data att="totalHp" value="4600000" />
			<data att="attack" value="20000" />
			<data att="expOfDieThisEnemy" value="60000" />
			<data att="defence" value="10000" />
			<data att="dogdeRate" value="0.08" />
			<data att="criticalRate" value="2.5" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.5" />
			<data att="hitRate" value="0.13" />
		</enemyData>
		<addXBAttack addValue="100" addMax="23000" />
		<skillCd>5</skillCd>
		<skillXBMove moveDistance="150"/>
		<!--移动速度以秒为单位 -->
		<animal id="enemy36" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="60" bodyHeight="110" walkSpeed="30"
			runSpeed="0">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->

		<isFly>false</isFly><!-- 是否是天上飞的怪物 -->
         <notBePushed>true</notBePushed><!--不能被推 -->



			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="-90" y="-30" z="-1" xRange="180" yRange="60"
				zRange="100" />

			<idle defId="walk_enemy36" />
			<walk defId="walk_enemy36" />
			<run defId="walk_enemy36" />
			<attack defId="attack_enemy36" />
			<hurt defId="hurt_enemy36" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_enemy36" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow1" />

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>

			<skill id="Skill_MonsterDaZhao" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" x="-100"
			 y="-50" z="-1" xRange="200" yRange="100" zRange="100" bodyDefId="Monster_Skill_dazhao_36"  hurtDuration="1000"  
			 randomPlaceXRange="10" randomPlaceYRange="-60" attackInterval="200"
			 skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^" >
			</skill>

			<animationDefinitions>

				<animationDefinition id="walk_enemy36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
						showClass="Walk_Monster_36" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="hurt_enemy36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
						showClass="BeAttack_Monster_36" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
						showClass="Attack_Monster_36" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
						showClass="Dead_Monster_36" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="Monster_Skill_dazhao_36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
						showClass="Monster_Skill_dazhao_36" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="Change" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="enemyFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="xiaoBingFallDownShow1"
					rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="FallDownEffect" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
			<shows>
				<show defId="walk_enemy36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
					showClass="Walk_Monster_InSuper_36" x_offset="0" y_offset="0" />
				<show defId="hurt_enemy36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
					showClass="BeAttack_Monster_InSuper_36" x_offset="0" y_offset="0" />
				<show defId="die_enemy36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
					showClass="Dead_Monster_InSuper_36" x_offset="0" y_offset="0" />
				<show defId="Monster_Skill_dazhao_36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
					showClass="Monster_Skill_dazhao_36" x_offset="0" y_offset="0" />
				<show defId="attack_enemy36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Enemy.swf"
					showClass="Monster_Skill_dazhao_36" x_offset="0" y_offset="0" />
			</shows>

		</animal>
	</xiaoBing>


	<boss>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="7200000" />
			<data att="attack" value="25000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="16000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="3.0" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.6" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>5</skillCd>
		
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		<addDefence attackvalue="150" attackmax="28000" />
		<skillBossData skillDistance="500" skillFrame="14"/>
		
		<animal id="boss36" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="200" bodyHeight="200" walkSpeed="12"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->

			<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->
			
			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="-20" y="-50" z="-1" xRange="190" yRange="200"
				zRange="100" />

			<idle defId="idle_boss36" />
			<walk defId="walk_boss36" />
			<run defId="run_boss36" />
			<attack defId="attack_boss36" />
			<hurt defId="hurt_boss36" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss36" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<skill id="Skill_BossDaZhao_1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="-250" y="-250" z="-1" xRange="500" yRange="500" zRange="50" attackInterval="500"
				bodyDefId="skillAnimation" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
		
		
		<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
						showClass="Walk_Boss_InSuper_36" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_boss36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
						showClass="Walk_Boss_36" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_boss36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
						showClass="BeAttack_Boss_36" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
						showClass="Attack_Boss_36" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss36" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
						showClass="Dead_Boss_36" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skill1Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
						showClass="Boss_Skill_dazhao_36" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="Change" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
						showClass="Boss_Skill_dazhao_36" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
			<shows>
				<show defId="idle_boss36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
					showClass="Walk_Boss_InSuper_36" x_offset="0" y_offset="0" />
				<show defId="walk_boss36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
					showClass="Walk_Boss_InSuper_36" x_offset="0" y_offset="0" />
				<show defId="hurt_boss36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
					showClass="BeAttack_Boss_InSuper_36" x_offset="0" y_offset="0" />
				<show defId="attack_boss36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
					showClass="Attack_Boss_InSuper_36" x_offset="0" y_offset="0" />
				<show defId="die_boss36" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
					showClass="Dead_Boss_InSuper_36" x_offset="0" y_offset="0" />
				<show defId="skillAnimation" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level36/Boss.swf"
					showClass="Boss_Skill_dazhao_36" x_offset="0" y_offset="0" />
			</shows>
		</animal>
	</boss>
</data>
