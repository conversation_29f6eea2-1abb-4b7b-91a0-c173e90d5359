<?xml version="1.0" encoding="utf-8" ?>
<data>
	<animal id="pet3" animalType="YJFY.Entity.AnimalEntity"
		delayDisappear="5000" bodyWidth="60" bodyHeight="80"  walkSpeed="200" >
        <!--宠物没有攻击，可以忽略-->
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="100" yRange="60" zRange="100" />

		<idle defId="pet3Idle" />
		<walk defId="pet3Walk" />
		<run defId="pet3Run" />
		<attack defId="pet3Attack"  />
		<attackEffect defId="monkeyAttackEffect" />
		<hurt defId="pet3Hurt" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="pet3Die">
			<attackSourceData entityId="" skillId="" />
		</die>
	    <!--宠物技能1-->
	    <skill id="Skill_Pet3Skill" className="YJFY.Skill.PetSkills.Skill_Pet3Skill" x="0"
			y="-50" z="-1" xRange="250" yRange="100" zRange="100" bodyDefId="pet3SkillBodyShow" bodyAttackReachFrameLabel="down" 
			bodySkillEndFrameLabel="end^stop^"    effectAddtoTargetId="pet3SkillEnemyBeAttackOnFire">
			<animationDefinition id="pet3SkillEnemyBeAttackOnFire" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet3All.swf"
					showClass="EnemyOnFire" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		<sound>
			
		</sound>




		<animationDefinitions>
			<animationDefinition id="pet3Idle" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet3All.swf"
					showClass="PetStand3_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet3Walk" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet3All.swf"
					showClass="PetWalk3_1" x_offset="0" y_offset="0" />
			</animationDefinition>
		    <!--技能显示-->
			<animationDefinition id="pet3SkillBodyShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet3All.swf"
					showClass="PetSkill3Attack_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="pet3SkillFrontShow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet3All.swf"
					showClass="PetSkill3Effect_Word_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			
			<!-- 不化装-->
		    <!--技能攻击效果-->
			<animationDefinition id="pet3SkillEnemyBeAttackOnFire" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/PetSource/Pet3All.swf"
					showClass="EnemyOnFire" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>

		<shows>
			<!--通过defId 和 eqClassName 在各自animalEntity的allAnimations中搜索更改相应的定义数据，然后更换重新初始化AnimationDefinition -->
			<!--初始装 -->
			<show defId="pet3Idle" eqClassName="Pet_HuoRen_1"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetStand3_1"
				x_offset="0" y_offset="0" />
			<show defId="pet3Walk" eqClassName="Pet_HuoRen_1"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetWalk3_1"
				x_offset="0" y_offset="0" />
           <show defId="pet3SkillBodyShow" eqClassName="Pet_HuoRen_1"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetSkill3Attack_1"
				x_offset="0" y_offset="0" />
			<show defId="pet3SkillFrontShow" eqClassName="Pet_HuoRen_1"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetSkill3Effect_Word_1"
				x_offset="0" y_offset="0" />
				
			
			<show defId="pet3Idle" eqClassName="Pet_HuoRen_2"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetStand3_2"
				x_offset="0" y_offset="0" />
			<show defId="pet3Walk" eqClassName="Pet_HuoRen_2"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetWalk3_2"
				x_offset="0" y_offset="0" />
           <show defId="pet3SkillBodyShow" eqClassName="Pet_HuoRen_2"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetSkill3Attack_2"
				x_offset="0" y_offset="0" />
			<show defId="pet3SkillFrontShow" eqClassName="Pet_HuoRen_2"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetSkill3Effect_Word_2"
				x_offset="0" y_offset="0" />
			
				
			<show defId="pet3Idle" eqClassName="Pet_HuoRen_3"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetStand3_3"
				x_offset="0" y_offset="0" />
			<show defId="pet3Walk" eqClassName="Pet_HuoRen_3"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetWalk3_3"
				x_offset="0" y_offset="0" />
           <show defId="pet3SkillBodyShow" eqClassName="Pet_HuoRen_3"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetSkill3Attack_3"
				x_offset="0" y_offset="0" />
			<show defId="pet3SkillFrontShow" eqClassName="Pet_HuoRen_3"
				swfPath="NewGameFolder/PetSource/Pet3All.swf" showClass="PetSkill3Effect_Word_3"
				x_offset="0" y_offset="0" />
		</shows>

	</animal>
</data>
