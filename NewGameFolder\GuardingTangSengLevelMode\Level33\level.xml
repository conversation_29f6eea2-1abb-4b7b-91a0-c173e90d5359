<?xml version="1.0" encoding="utf-8" ?>
<data id="Level33"
	swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/level.swf"
	className="LevelMap32" x="0" y="0" z="0" xRange="960" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!--背景 -->
	<!--<projectLayer sx="0" sy="2245" sz="500" name="backMap" /> <projectLayer 
		sx="0" sy="-220" sz="60" name="frontMap" /> -->
	<backgroundMusic id="LevelMusic" name="LevelMusic"
		swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound6.swf"
		className="SoundHY" />
	<!--totalWaveNum 用于显示波次总数 -->
	<Waves totalWaveNum="1">

		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<Wave waveCount="1" totalEnemyNum="1" x="900" y="0" xRange="100"
			yRange="400">
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Ghost"
				xmlPath="boss1" startTime="0" duration="50" num="1" />
	<!-- 		<Enemy enemyClass="YJFY.LevelMode1.Boss" xmlPath="bosspre1" startTime="0" duration="50" num="0" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_AddBlood"
				xmlPath="bosspre2" startTime="0" duration="50" num="0" isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Shitou"
				xmlPath="bosspre3" startTime="0" duration="50" num="0"
				isFallDown="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill" xmlPath="bosspre4"
				startTime="0" duration="50" num="0" />
			<Enemy enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill" xmlPath="bosspre5"
				startTime="0" duration="50" num="0" /> -->
		</Wave>
	</Waves>
	<EqDrop>
		<xiaoBing noDropProWeight="50">
			<!--proWeight 概率权重 -->

	
	<!-- 龙之泪 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Longzhilei_S"
				proWeight="1" />
				
			<!-- 暴击5级 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_CriticalRate5_S"
				proWeight="1" />
			<!-- 闪避5级 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Dodge5_S"
				proWeight="1" />
			
			
				<!-- 暗黑石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_AnHei1_S"
				proWeight="2" />
			<item dropClassName="UI.Equipments.SceneEquipments.Material_AnHei2_S"
				proWeight="2" />
			<item dropClassName="UI.Equipments.SceneEquipments.Material_AnHei3_S"
				proWeight="2" />
			<item dropClassName="UI.Equipments.SceneEquipments.Material_AnHei4_S"
				proWeight="2" />
			<item dropClassName="UI.Equipments.SceneEquipments.Material_AnHei5_S"
				proWeight="2" />
			<item dropClassName="UI.Equipments.SceneEquipments.Material_AnHei6_S"
				proWeight="2" />
			<item dropClassName="UI.Equipments.SceneEquipments.Material_AnHei7_S"
				proWeight="2" />						
			<item dropClassName="UI.Equipments.SceneEquipments.Material_AnHei8_S"
				proWeight="2" />	
				
			
			<!-- 蓝宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp5_S"
				proWeight="8" />
			<!-- 人品宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp5_S"
				proWeight="8" />
			<!-- 攻击宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack5_S"
				proWeight="8" />
			<!-- 生命宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp5_S"
				proWeight="11" />
			<!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence5_S"
				proWeight="12" />
			<!-- 开孔灵符 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_Open_S"
				proWeight="12" />
			<!-- 碎石锤 -->
			<item dropClassName="UI.Equipments.SceneEquipments.Material_ChuiZi_S"
				proWeight="6" />

			<!-- 红药 -->
			<item dropClassName="Item_HpUp" proWeight="100" />
			<!-- 蓝药 -->
			<item dropClassName="Item_MpUp" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_10000" proWeight="80" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_50000" proWeight="50" />
			<!-- 金币 -->
			<item dropClassName="Item_MoneyUp_100000" proWeight="5" />
			<!-- 灵兽石 -->
			<item dropClassName="Item_StrengthenNum_10" proWeight="5" />


		</xiaoBing>
		<boss noDropProWeight="150">
			<!--proWeight 概率权重 -->
			<dropNumData>
				<smallDropNumData proWeight="10">
					<numData num="1" proWeight="5" />
					<numData num="2" proweight="5" />
				</smallDropNumData>
				<bigDropNumData proWeight="1">
					<numData num="4" proWeight="8" />
					<numData num="5" proWeight="2" />
				</bigDropNumData>
			</dropNumData>

            <!-- 烛龙果模具-->
			<item dropClassName="UI.Equipments.SceneEquipments.Scroll_Zhulongguo_S"
				proWeight="1" />
			<!-- 幽冥宝珠（紫） -->
			<item dropClassName="UI.Equipments.StackEquipments.Material_YouMing6_S"
				proWeight="2" />
			<!-- 蓝宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Mp6_S"
				proWeight="8" />
			<!-- 人品宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Rp6_S"
				proWeight="8" />
			<!-- 攻击宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Attack6_S"
				proWeight="8" />
			<!-- 生命宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Hp6_S"
				proWeight="11" />
			<!-- 防御宝石 -->
			<item dropClassName="UI.Equipments.SceneEquipments.InsetGem_Defence6_S"
				proWeight="12" />
		
			<item dropClassName="UI.Equipments.SceneEquipments.Gourd_AnHei_S"
				proWeight="1" />	
				<item dropClassName="UI.Equipments.SceneEquipments.Gourd_ShenYuan_S"
				proWeight="2" />	
			
				<item dropClassName="UI.Equipments.SceneEquipments.Necklace_AnHei_S"
				proWeight="1" />	
			<item dropClassName="UI.Equipments.SceneEquipments.Necklace_ShenYuan_S"
				proWeight="2" />		

		</boss>

	</EqDrop>



	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>

	</sharedAnimationDefinitions>


	<boss1>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="9200000" />
			<data att="attack" value="25000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="16000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="3.0" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.6" />
			<data att="hitRate" value="0.25" />
		</enemyData>

		<skillCd>7</skillCd> <!-- 释放技能1 -->
		<skillCd2>10</skillCd2> <!-- 释放技能2 -->

		<skillCd3>15</skillCd3> <!-- 释放技能3 -->
		<summon1>12</summon1> <!-- 每8秒召唤一次 -->
		<addAttack>500</addAttack> <!-- 每次增加的攻击力 -->
		<getBossNum> <!-- 在1-50这个区间召唤一只 在51-90这个区间召唤2个 区间越大概率越大，但是不能超出150,也不能重叠 -->
			<num min="1" max="40" amount="1" />
			<num min="41" max="100" amount="2" />
			<num min="101" max="130" amount="3" />
			<num min="131" max="144" amount="4" />
			<num min="145" max="150" amount="5" />
		</getBossNum>
		<getboss>
			<num min="1" max="10" taget="boss6" />
			<num min="11" max="40" taget="boss2" />
			<num min="41" max="70" taget="boss3" />
			<num min="71" max="100" taget="boss4" />
			<num min="101" max="130" taget="boss5" />
			<num min="131" max="150" taget="boss7" />
		</getboss>
		
		<skillInvincible>false</skillInvincible><!-- 用技能的时候是否无敌 -->
		
		<animal id="boss33" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="12"
			runSpeed="100">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->
			<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->
			
			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="90" yRange="100"
				zRange="100" />

			<idle defId="idle_boss33" />
			<walk defId="walk_boss33" />
			<run defId="run_boss33" />
			<attack defId="attack_boss33" />
			<hurt defId="hurt_boss33" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss33" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<skill id="Skill_BossDaZhao_1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-250" y="-120" z="-1" xRange="900" yRange="250" zRange="50" attackInterval="500"
				bodyDefId="skillAnimation1" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<skill id="Skill_BossDaZhao_2" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-200" y="-200" z="-1" xRange="400" yRange="400" zRange="50" attackInterval="500"
				bodyDefId="skillAnimation2" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<skill id="Skill_BossDaZhao_3" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-250" y="-250" z="-1" xRange="500" yRange="500" zRange="50" attackInterval="500"
				bodyDefId="skillAnimation3" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss33" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="Walk_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_boss33" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="Walk_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss29" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_29" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss33" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="BeAttack_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss33" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="Attack_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss33" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="Dead_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="Skill_1_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation2" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="Skill_2_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation3" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="Skill_3_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level33/Boss.swf"
						showClass="Skill_4_Boss_33" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>


		<boss2 direct="1" distance="230" enemyClass="YJFY.LevelMode1.Boss">
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="3000000" />
		<data att="attack" value="10000" />
		<data att="expOfDieThisEnemy" value="110000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.25" />
	</enemyData>
	<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
	<animal id="boss21" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="idle_boss21" />
		<walk defId="walk_boss21" />
		<run defId="run_boss21" />
		<attack defId="attack_boss21" />
		<hurt defId="hurt_boss21" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss21" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>


		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>

		<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

		<animationDefinitions>
			<animationDefinition id="idle_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Walk_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Walk_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="BeAttack_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Attack_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Dead_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillAnimation4" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Show_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</boss2>


<boss3 direct="1" distance="230" enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_AddBlood">
	<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="6000000" />
			<data att="attack" value="8000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="100" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="0.5" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		<addBloodInfo cd="10" value="600000" />
		<animal id="boss27" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="30"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-30" z="-1" xRange="120" yRange="60"
				zRange="100" />

			<idle defId="idle_boss27" />
			<walk defId="walk_boss27" />
			<run defId="run_boss27" />
			<attack defId="attack_boss27" />
			<hurt defId="hurt_boss27" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss27" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />

			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>

			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<animationDefinitions>
				<animationDefinition id="idle_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Walk_Boss_27" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Walk_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss26" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_26" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="BeAttack_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Attack_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Dead_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Show_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="AddHp" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="AddHp" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>



		</animal>
</boss3>

<boss4 direct="2" distance="230" enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_Shitou">
	<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="7000000" />
			<data att="attack" value="12000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="100" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="0.5" />
			<data att="criticalMuti" value="2" />
			<data att="deCriticalRate" value="0.8" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		<addSuperInfo deCriticalRate="1.5" defence="15000" />
		<animal id="boss28" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="180" walkSpeed="25"
			runSpeed="100">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="70" y="-30" z="-1" xRange="70" yRange="60"
				zRange="100" />

			<idle defId="walk_boss28" />
			<walk defId="walk_boss28" />
			<run defId="walk_boss28" />
			<attack defId="attack_boss28" />
			<hurt defId="hurt_boss28" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss28" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />

			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>

			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<animationDefinitions>

				<animationDefinition id="walk_boss28" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Walk_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss22" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss28" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="BeAttack_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss28" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Attack_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss28" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Dead_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Show_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="change" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!--<animationDefinition id="superRotate" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="1" defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" /> </animationDefinition> -->
			</animationDefinitions>
			<shows>
				<show defId="walk_boss28" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
					showClass="Walk_Boss_InSuper_28" x_offset="0" y_offset="0" />
				<show defId="hurt_boss28" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
					showClass="BeAttack_Boss_InSuper_28" x_offset="0" y_offset="0" />
				<show defId="attack_boss28" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
					showClass="Attack_Boss_InSuper_28" x_offset="0" y_offset="0" />
				<show defId="die_boss28" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
					showClass="Dead_Boss_InSuper_28" x_offset="0" y_offset="0" />
			</shows>


		</animal>
</boss4>

<boss5 direct="3" distance="230" enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill">
	<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="8000000" />
			<data att="attack" value="10000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="5000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>10</skillCd>
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		<animal id="boss29" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="30"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="140" yRange="100"
				zRange="100" />

			<idle defId="idle_boss29" />
			<walk defId="walk_boss29" />
			<run defId="run_boss29" />
			<attack defId="attack_boss29" />
			<hurt defId="hurt_boss29" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss29" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			
			<skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao"
				x="-30" y="-40" z="-1" xRange="900" yRange="400" zRange="150"
				bodyDefId="skillAnimation" hurtDuration="3500"
				bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^"
				effectAddtoTargetId="jieBing" everyEntityAddShowIsFrontOfBody="0">
				<shakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf" className="ShakeView" />
				<animationDefinition id="jieBing" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf" 
						showClass="JieBing" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>
			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<animationDefinitions>
				<animationDefinition id="idle_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Walk_Boss_29" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Walk_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss29" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_29" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="BeAttack_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Attack_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Dead_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Skill_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Show_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="jieBing" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="JieBing" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
</boss5>

<boss6 direct="4" distance="230" enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill">
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="8000000" />
			<data att="attack" value="12000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="5000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		<skillCd>6</skillCd>
		<animal id="boss30" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="30"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="90" yRange="100"
				zRange="100" />

			<idle defId="idle_boss30" />
			<walk defId="walk_boss30" />
			<run defId="run_boss30" />
			<attack defId="attack_boss30" />
			<hurt defId="hurt_boss30" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss30" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			
			<skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao"
				x="-30" y="-40" z="-1" xRange="900" yRange="400" zRange="150"
				bodyDefId="skillAnimation" hurtDuration="3500"
				bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^"
				effectAddtoTargetId="tunshi" everyEntityAddShowIsFrontOfBody="0">
				<shakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf" className="ShakeView" />
				<animationDefinition id="tunshi" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf" 
						showClass="tunshi" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Walk_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Walk_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="BeAttack_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Attack_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Dead_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Skill_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Show_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="tunshi" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="tunshi" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</boss6>
	<boss7 direct="3" distance="100" enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill">
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="4800000" />
			<data att="attack" value="15000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="12000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>5</skillCd>
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		
		<animal id="boss32" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="12"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->

			<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->
			
			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="90" yRange="100"
				zRange="100" />

			<idle defId="idle_boss32" />
			<walk defId="walk_boss32" />
			<run defId="run_boss32" />
			<attack defId="attack_boss32" />
			<hurt defId="hurt_boss32" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss32" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<skill id="BossDaZhao" className="YJFY.BossMode.Boss1.BossDaZhao1"
				bodyDefId="daZhaoBodyShow" dropShowDefId="daZhaoDropShow">
				<dropAttackRange x="-50" y="-50" z="-1" xRange="100"
					yRange="100" zRange="100" />
				<animationDefinition id="daZhaoDropShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="ItemDropEffect" x_offset="0" y_offset="0" />
				</animationDefinition>

			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Walk_Boss_32" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Walk_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss29" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_29" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="BeAttack_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Attack_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Dead_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="daZhaoBodyShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Show_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="daZhaoDropShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf" showClass="ItemDropEffect"
						x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</boss7>
	</boss1>

	



<bosspre1>
	<!--敌人数据 -->
	<enemyData>
		<data att="totalHp" value="4000000" />
		<data att="attack" value="10000" />
		<data att="expOfDieThisEnemy" value="110000" />
		<data att="defence" value="100" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="0.5" />
		<data att="criticalMuti" value="3" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.25" />
	</enemyData>
	<animal id="boss21" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
		runSpeed="200">
		<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
			x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
			/> -->

		
		<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
		<attackRange x="0" y="-30" z="-1" xRange="60" yRange="60"
			zRange="100" />

		<idle defId="idle_boss21" />
		<walk defId="walk_boss21" />
		<run defId="run_boss21" />
		<attack defId="attack_boss21" />
		<hurt defId="hurt_boss21" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss21" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</skill>
		
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>

		<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

		<animationDefinitions>
			<animationDefinition id="idle_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Walk_Boss_17" x_offset="0" y_offset="0" />
				<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
					x_offset="0" y_offset="0" /> -->
			</animationDefinition>
			<animationDefinition id="walk_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Walk_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<!-- <animationDefinition id="run_boss21" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
				showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
			<animationDefinition id="hurt_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="BeAttack_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Attack_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss21" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Dead_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="skillAnimation4" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level21/Boss.swf"
					showClass="Show_Boss_17" x_offset="0" y_offset="0" />
			</animationDefinition>

			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossFootShadow" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="superRotate" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>



	</animal>
</bosspre1>

<bosspre2>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="4000000" />
			<data att="attack" value="8000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="100" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="0.5" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<addBloodInfo cd="10" value="600000" />
		<animal id="boss27" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="30"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-30" z="-1" xRange="120" yRange="60"
				zRange="100" />

			<idle defId="idle_boss27" />
			<walk defId="walk_boss27" />
			<run defId="run_boss27" />
			<attack defId="attack_boss27" />
			<hurt defId="hurt_boss27" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss27" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />

			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>

			
			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>


			<animationDefinitions>
				<animationDefinition id="idle_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Walk_Boss_27" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Walk_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss26" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_26" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="BeAttack_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Attack_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss27" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Dead_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="Show_Boss_27" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="AddHp" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level27/Boss.swf"
						showClass="AddHp" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>



		</animal>
	</bosspre2>
<bosspre3>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="5000000" />
			<data att="attack" value="12000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="100" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="0.5" />
			<data att="criticalMuti" value="2" />
			<data att="deCriticalRate" value="0.8" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<addSuperInfo deCriticalRate="1.5" defence="15000" />
		<animal id="boss28" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="180" walkSpeed="25"
			runSpeed="100">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="70" y="-30" z="-1" xRange="70" yRange="60"
				zRange="100" />

			<idle defId="walk_boss28" />
			<walk defId="walk_boss28" />
			<run defId="walk_boss28" />
			<attack defId="attack_boss28" />
			<hurt defId="hurt_boss28" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss28" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />

			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>

				<animationDefinition id="walk_boss28" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Walk_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss22" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_17" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss28" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="BeAttack_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss28" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Attack_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss28" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Dead_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Show_Boss_28" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="change" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
						showClass="Change" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!--<animationDefinition id="superRotate" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="1" defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" /> </animationDefinition> -->
			</animationDefinitions>
			<shows>
				<show defId="walk_boss28" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
					showClass="Walk_Boss_InSuper_28" x_offset="0" y_offset="0" />
				<show defId="hurt_boss28" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
					showClass="BeAttack_Boss_InSuper_28" x_offset="0" y_offset="0" />
				<show defId="attack_boss28" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
					showClass="Attack_Boss_InSuper_28" x_offset="0" y_offset="0" />
				<show defId="die_boss28" eqClassName="change"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/Level28/Boss.swf"
					showClass="Dead_Boss_InSuper_28" x_offset="0" y_offset="0" />
			</shows>


		</animal>
	</bosspre3>

<bosspre4>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="8000000" />
			<data att="attack" value="10000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="5000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>10</skillCd>
		<animal id="boss29" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="30"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="140" yRange="100"
				zRange="100" />

			<idle defId="idle_boss29" />
			<walk defId="walk_boss29" />
			<run defId="run_boss29" />
			<attack defId="attack_boss29" />
			<hurt defId="hurt_boss29" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss29" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			
			<skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao"
				x="-30" y="-40" z="-1" xRange="900" yRange="400" zRange="150"
				bodyDefId="skillAnimation" hurtDuration="3500"
				bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^"
				effectAddtoTargetId="jieBing" everyEntityAddShowIsFrontOfBody="0">
				<shakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf" className="ShakeView" />
				<animationDefinition id="jieBing" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf" 
						showClass="JieBing" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>

			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<animationDefinitions>
				<animationDefinition id="idle_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Walk_Boss_29" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Walk_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss29" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_29" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="BeAttack_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Attack_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss29" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Dead_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Skill_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="Show_Boss_29" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="jieBing" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level29/Boss.swf"
						showClass="JieBing" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</bosspre4>

<bosspre5>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="8000000" />
			<data att="attack" value="12000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="5000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>6</skillCd>
		<animal id="boss30" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="30"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->


			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="90" yRange="100"
				zRange="100" />

			<idle defId="idle_boss30" />
			<walk defId="walk_boss30" />
			<run defId="run_boss30" />
			<attack defId="attack_boss30" />
			<hurt defId="hurt_boss30" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss30" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			
			<skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao"
				x="-30" y="-40" z="-1" xRange="900" yRange="400" zRange="150"
				bodyDefId="skillAnimation" hurtDuration="3500"
				bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^"
				effectAddtoTargetId="tunshi" everyEntityAddShowIsFrontOfBody="0">
				<shakeView swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf" className="ShakeView" />
				<animationDefinition id="tunshi" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf" 
						showClass="tunshi" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>


			<animationDefinitions>
				<animationDefinition id="idle_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Walk_Boss_30" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Walk_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<!-- <animationDefinition id="run_boss29" rows="1" cols="1" walkable="false" 
					overlap="false" frameInterval="3" defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf" 
					showClass="Run_Boss_29" x_offset="0" y_offset="0" /> </animationDefinition> -->
				<animationDefinition id="hurt_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="BeAttack_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Attack_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss30" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Dead_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="skillAnimation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Skill_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="Show_Boss_30" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="tunshi" rows="1" cols="1"
					walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level30/Boss.swf"
						showClass="tunshi" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</bosspre5>
	<bosspre6 direct="4" distance="230" enemyClass="YJFY.LevelMode1.OtherEnemys.Boss_ActiveSkill">
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="4800000" />
			<data att="attack" value="15000" />
			<data att="expOfDieThisEnemy" value="200000" />
			<data att="defence" value="12000" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="1.8" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="2.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skillCd>5</skillCd>
		
		<skillInvincible>true</skillInvincible><!-- 用技能的时候是否无敌 -->
		
		<animal id="boss32" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="12"
			runSpeed="200">
			<!--<bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" 
				x_offset="-20" y_offset="-60" /> <attackSound swf="YJFY/sound.swf" className="ShootSound2" 
				/> -->

			<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->
			
			<!--攻击范围，x：攻击矩形范围在实体坐标系中的x坐标， y:攻击矩形范围在实体坐标， width：攻击矩形范围的x轴范围长度， height：攻击矩形范围的y轴范围长度 -->
			<attackRange x="0" y="-50" z="-1" xRange="90" yRange="100"
				zRange="100" />

			<idle defId="idle_boss32" />
			<walk defId="walk_boss32" />
			<run defId="run_boss32" />
			<attack defId="attack_boss32" />
			<hurt defId="hurt_boss32" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>

			<die defId="die_boss32" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />


			<skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"
				superRotateId="superRotate">
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>

			<skill id="BossDaZhao" className="YJFY.BossMode.Boss1.BossDaZhao1"
				bodyDefId="daZhaoBodyShow" dropShowDefId="daZhaoDropShow">
				<dropAttackRange x="-50" y="-50" z="-1" xRange="100"
					yRange="100" zRange="100" />
				<animationDefinition id="daZhaoDropShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="ItemDropEffect" x_offset="0" y_offset="0" />
				</animationDefinition>

			</skill>

			<sound>
				<sound id="beAttackSound1" name="beAttackSound1"
					swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>

			<skill id="Skill_BossDaZhao_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEndSummon^stop^"
				 bodySkillEndFrameLabel="skillEndSummon^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>

			<animationDefinitions>
				<animationDefinition id="idle_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Walk_Boss_32" x_offset="0" y_offset="0" />
					<!--<show swfPath="NewGameFolder/MonkeyWeapon0.swf" showClass="SWKStand_Weapon_0" 
						x_offset="0" y_offset="0" /> -->
				</animationDefinition>
				<animationDefinition id="walk_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Walk_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="BeAttack_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Attack_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss32" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Dead_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="Show_Boss_32" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="daZhaoBodyShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf"
						showClass="SkillRandomDropEffect" x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="daZhaoDropShow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/Level32/Boss.swf" showClass="ItemDropEffect"
						x_offset="0" y_offset="0" />
				</animationDefinition>

				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</bosspre6>
</data>
