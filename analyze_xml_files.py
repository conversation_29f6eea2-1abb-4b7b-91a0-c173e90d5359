#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XML文件分析脚本
用于提取所有XML文件中的ID和名称信息
"""

import os
import xml.etree.ElementTree as ET
from collections import defaultdict
import re

def analyze_xml_file(file_path):
    """分析单个XML文件，提取ID和名称信息"""
    results = []
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # 递归遍历所有元素
        def traverse_element(element, parent_tag=""):
            for child in element:
                # 提取属性中的ID和名称
                attrs = child.attrib
                item_data = {}
                
                # 常见的ID属性名
                id_attrs = ['id', 'defId', 'key', 'className', 'skillId', 'entityId']
                name_attrs = ['name', 'title', 'description']
                
                for attr_name, attr_value in attrs.items():
                    if attr_name.lower() in [a.lower() for a in id_attrs]:
                        item_data['id'] = attr_value
                        item_data['id_type'] = attr_name
                    elif attr_name.lower() in [a.lower() for a in name_attrs]:
                        item_data['name'] = attr_value
                        item_data['name_type'] = attr_name
                
                # 如果找到了ID或名称，记录这个项目
                if 'id' in item_data or 'name' in item_data:
                    item_data['tag'] = child.tag
                    item_data['parent_tag'] = parent_tag
                    item_data['file_path'] = file_path
                    results.append(item_data)
                
                # 递归处理子元素
                traverse_element(child, child.tag)
        
        traverse_element(root)
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
    
    return results

def categorize_data(all_data):
    """将数据按类型分类"""
    categories = defaultdict(list)
    
    for item in all_data:
        # 根据文件路径和标签确定类别
        file_path = item.get('file_path', '')
        tag = item.get('tag', '')
        
        if 'skill' in file_path.lower():
            categories['技能 (Skills)'].append(item)
        elif 'equipment' in file_path.lower():
            categories['装备 (Equipment)'].append(item)
        elif 'player' in file_path.lower():
            categories['角色 (Players)'].append(item)
        elif 'pet' in file_path.lower():
            categories['宠物 (Pets)'].append(item)
        elif 'mount' in file_path.lower():
            categories['坐骑 (Mounts)'].append(item)
        elif 'enemy' in file_path.lower() or 'enemies' in file_path.lower():
            categories['敌人 (Enemies)'].append(item)
        elif 'task' in file_path.lower():
            categories['任务 (Tasks)'].append(item)
        elif 'shop' in file_path.lower():
            categories['商店 (Shop)'].append(item)
        elif 'activity' in file_path.lower():
            categories['活动 (Activities)'].append(item)
        elif 'ui' in file_path.lower():
            categories['界面 (UI)'].append(item)
        else:
            categories['其他 (Others)'].append(item)
    
    return categories

def main():
    """主函数"""
    print("开始分析XML文件...")
    
    # 获取所有XML文件
    xml_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.xml'):
                xml_files.append(os.path.join(root, file))
    
    print(f"找到 {len(xml_files)} 个XML文件")
    
    # 分析所有文件
    all_data = []
    for xml_file in xml_files:
        print(f"分析文件: {xml_file}")
        file_data = analyze_xml_file(xml_file)
        all_data.extend(file_data)
    
    print(f"总共提取了 {len(all_data)} 个数据项")
    
    # 分类数据
    categories = categorize_data(all_data)
    
    # 生成报告
    with open('xml_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write("XML文件分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"分析的文件总数: {len(xml_files)}\n")
        f.write(f"提取的数据项总数: {len(all_data)}\n")
        f.write(f"数据类别总数: {len(categories)}\n\n")
        
        # 按类别输出
        for category, items in categories.items():
            f.write(f"\n{category}\n")
            f.write("-" * 30 + "\n")
            f.write(f"项目数量: {len(items)}\n\n")
            
            for item in items:
                id_info = item.get('id', 'N/A')
                name_info = item.get('name', 'N/A')
                file_path = item.get('file_path', 'N/A')
                tag = item.get('tag', 'N/A')
                
                f.write(f"ID: {id_info}\n")
                f.write(f"名称: {name_info}\n")
                f.write(f"标签: {tag}\n")
                f.write(f"文件: {file_path}\n")
                f.write("-" * 20 + "\n")
    
    print("分析完成！结果已保存到 xml_analysis_report.txt")

if __name__ == "__main__":
    main()
