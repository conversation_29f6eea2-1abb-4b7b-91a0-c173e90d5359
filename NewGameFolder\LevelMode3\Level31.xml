<?xml version="1.0" encoding="utf-8" ?>
<data id="level1" swfPath="NewGameFolder/LevelMode3/newfail.swf"
	className="LevelMap" x="0" y="0" z="0" xRange="1920" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!-- <projectLayer sx="300" sy="-350" sz="60"  name="tangsengmc" /> --> <!-- 被煮唐僧的动画位置 -->
	<!-- <projectLayer sx="1500" sy="-300" sz="60"  name="lightmc" /> -->
	<!-- 第三屏兵器的移动范围 -->
	<lightinfo startx="1500" starty="350" startz="0" xrange1="1200" yrange1="150" xrange2="1800" yrange2="350" totalframe="50" stoptime="2" />
	<!-- 第三屏兵器的光照范围 -->
	<LightBossRange x="0" y="-30" z="-1" xRange="60" yRange="60"
				zRange="100" />
	<bosslasthp>0</bosslasthp><!-- 当boss血量到这个值的时候开始倒计时 -->
	<!--需要触发倒计时的全屏技能的id-->
	<screenskills>
		<screenskill>10101000</screenskill>
		<screenskill>10101100</screenskill>
		<screenskill>10101200</screenskill>
		<screenskill>10101300</screenskill>
		
		<screenskill>10102004</screenskill>
		<screenskill>10102104</screenskill>
		<screenskill>10102204</screenskill>
		<screenskill>10102304</screenskill>
		
		<screenskill>10104004</screenskill>
		<screenskill>10104104</screenskill>
		<screenskill>10104204</screenskill>
		<screenskill>10104304</screenskill>
		
		<screenskill>10105004</screenskill>
		<screenskill>10105104</screenskill>
		<screenskill>10105204</screenskill>
		<screenskill>10105304</screenskill>
		
		<screenskill>10106004</screenskill>
		<screenskill>10106104</screenskill>
		<screenskill>10106204</screenskill>
		<screenskill>10106304</screenskill>
		
		<screenskill>10107004</screenskill>
		<screenskill>10107104</screenskill>
		<screenskill>10107204</screenskill>
		<screenskill>10107304</screenskill>

		<screenskill>10109004</screenskill>
		<screenskill>10109104</screenskill>
		<screenskill>10109204</screenskill>
		<screenskill>10109304</screenskill>
	</screenskills>
	<summon1>3</summon1>
	<summon2>3</summon2>
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound7.swf" className="SoundQ" />	
	<startShow swfPath="NewGameFolder/LevelMode3/StartShow5.swf" className="StartShow" />
	<Waves totalWaveNum="1">
		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<!-- 第一屏 -->
		<Wave waveCount="1" totalEnemyNum="1" x="200" y="200" xRange="0" yRange="0" time="30" >
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem1" startTime="0" duration="0" num="1"  />
			
		
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem3" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem4" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem5" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem6" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem7" startTime="0" duration="0" num="1"  />
				
			<Enemy enemyClass="YJFY.LevelMode2.XunShan" xmlPath="boss1" localpos="xunshan" startTime="0" duration="0" num="1"  />
		
		</Wave>
		<!-- 第二屏 -->
		<Wave waveCount="2" totalEnemyNum="2" x="200" y="200" xRange="0" yRange="0" time="30" >
			
		<Enemy enemyClass="YJFY.LevelMode2.XunShan" xmlPath="boss1" localpos="xunshan1" startTime="0" duration="0" num="1"  />
		<Enemy enemyClass="YJFY.LevelMode2.XunShan" xmlPath="boss1" localpos="xunshan2" startTime="0" duration="0" num="1"  />
		
				<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem8" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem9" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem10" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem11" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem12" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode2.TiDeng" xmlPath="xiaoBing1" localpos="infoitem13" startTime="0" duration="0" num="1"  />
			
		</Wave>
		<!-- 第三屏 -->
		<Wave waveCount="3" totalEnemyNum="0" x="200" y="200" xRange="0" yRange="0" time="30" >
			<!-- <Enemy enemyClass="YJFY.LevelMode2.NewBoss" xmlPath="boss3" startTime="0" duration="0" num="1"  /> -->
		</Wave>
	</Waves>
    <!--抽奖配置-->
	<lottery>
		<!-- 只能12个-->
	
       
       
     
			 <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
        <!--   天使灵符碎片 -->
        <item id="10500123" num="1" proWeight="3" />
		   <!-- 幸运宝石-->
        <item id="10500000" num="3" proWeight="10" />
        <!-- 天使灵符模具 -->
        <item id="12100101" num="1" proWeight="2" />
		 <!-- 幸运宝石 -->
        <item id="10500000" num="2" proWeight="15" />
        <!-- 灵符洗炼石 -->
        <item id="11000030" num="1" proWeight="2" />
		 <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
        <!--   天使灵符碎片 -->
        <item id="10500123" num="2" proWeight="3" />
			 <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
        <!-- 幽冥宝珠紫-->
        <item id="10500124" num="1" proWeight="3" />
			 <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
        <!--   天使灵符碎片 -->
        <item id="10500123" num="1" proWeight="3" />
		
		
		
		
	</lottery>
    <EqDrop>
	    <xiaoBing noDropProWeight="500">
		   <!--proWeight 概率权重-->
	    
  	      <!--  红药 -->   
   	      <item dropClassName="Item_HpUp" proWeight="5" />
   	      <!--  金币 -->  
   	      <item dropClassName="Item_MoneyUp" proWeight="5" />
   	      <!--  蓝药 -->  
   	      <item dropClassName="Item_MpUp" proWeight="10" /> 
		  
		  	      <!-- 灵符碎片-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Lingfusuipian1_S" proWeight="10" />	
   	  
		  <!-- 灵符模具-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_lingfu_tianshi_S" proWeight="5" />	
   	  
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="5" />
   	   
	  	</xiaoBing>
	    <boss noDropProWeight="1">
		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="5" />
				   <numData num="2" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="4" proWeight="8" />
				   <numData num="5" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>		  
	      <!-- 灵符碎片-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Lingfusuipian1_S" proWeight="200" />	
   	  
		  <!-- 灵符模具-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_lingfu_tianshi_S" proWeight="30" />	
   
		  <!-- 灵符模具-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.precious_tianshi_S" proWeight="5" />	  
		  
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="200" />
		  </boss>
   	</EqDrop>
	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
	</sharedAnimationDefinitions>

	<xunshan>
		<startX>800</startX> <startY>400</startY>
		<arragex>100</arragex> <arragey>100</arragey>
		<arragex2>900</arragex2>
		<arragey2>400</arragey2>
		<totalFrame>30</totalFrame>
		<stoptime>5</stoptime>
		<LightRange x="-200" y="-25" z="-50" xRange="400" yRange="100" zRange="150" />
	</xunshan>

	<xunshan1>
		<startX>700</startX> <startY>400</startY>
		<arragex>1400</arragex> <arragey>100</arragey>
		<arragex2>900</arragex2>
		<arragey2>400</arragey2>
		<totalFrame>35</totalFrame>
		<stoptime>3</stoptime>
		<LightRange x="-200" y="-25" z="-50" xRange="400" yRange="100" zRange="150" />
	</xunshan1>
	
	<xunshan2>
		<startX>1400</startX> <startY>100</startY>
		<arragex>700</arragex> <arragey>400</arragey>
		<arragex2>900</arragex2>
		<arragey2>200</arragey2>
		<totalFrame>35</totalFrame>
		<stoptime>3</stoptime>
		<LightRange x="-200" y="-25" z="-50" xRange="400" yRange="100" zRange="150" />
	</xunshan2>

	<infoitem1>
		<startX>500</startX>  <startY>400</startY>
		<endX>200</endX>  <endY>400</endY>
		<backattack>2</backattack>
		<totalframe>50</totalframe>
		<stoptime>3</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem1>

	<infoitem2>
		<startX>800</startX>
		<startY>400</startY>
		<arragex>100</arragex>
		<arragey>100</arragey>
		<arragex2>900</arragex2>
		<arragey2>300</arragey2>
		<totalFrame>30</totalFrame>
		<stoptime>5</stoptime>
		<LightRange x="0" y="-25" z="-1" xRange="50" yRange="50" zRange="100" />
	</infoitem2>

	<infoitem3>
		<startX>300</startX> <startY>320</startY>
		<endX>800</endX> <endY>320</endY>
		<backattack>2</backattack>
		<totalframe>40</totalframe>
		<stoptime>2</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem3>

	<infoitem4>
		<startX>700</startX> <startY>240</startY>
		<endX>200</endX> <endY>240</endY>
		<backattack>3</backattack>
		<totalframe>45</totalframe>
		<stoptime>0</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem4>
	
	<infoitem5>
		<startX>800</startX> <startY>160</startY>
		<endX>300</endX> <endY>160</endY>
		<backattack>2</backattack>
		<totalframe>35</totalframe>
		<stoptime>0</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem5>
	
	<infoitem6>
		<startX>200</startX> <startY>80</startY>
		<endX>700</endX> <endY>80</endY>
		<backattack>3</backattack>
		<totalframe>55</totalframe>
		<stoptime>1</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem6>
	
	<infoitem7>
		<startX>600</startX> <startY>10</startY>
		<endX>300</endX> <endY>10</endY>
		<backattack>2</backattack>
		<totalframe>45</totalframe>
		<stoptime>2</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem7>
	
	
	
	<infoitem8>
		<startX>1000</startX>  <startY>400</startY>
		<endX>700</endX>  <endY>400</endY>
		<backattack>3</backattack>
		<totalframe>50</totalframe>
		<stoptime>3</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem8>



	<infoitem9>
		<startX>800</startX> <startY>320</startY>
		<endX>1300</endX> <endY>320</endY>
		<backattack>2</backattack>
		<totalframe>60</totalframe>
		<stoptime>2</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem9>

	<infoitem10>
		<startX>1200</startX> <startY>240</startY>
		<endX>700</endX> <endY>240</endY>
		<backattack>3</backattack>
		<totalframe>50</totalframe>
		<stoptime>0</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem10>
	
	<infoitem11>
		<startX>1300</startX> <startY>160</startY>
		<endX>800</endX> <endY>160</endY>
		<backattack>2</backattack>
		<totalframe>60</totalframe>
		<stoptime>0</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem11>
	
	<infoitem12>
		<startX>1400</startX> <startY>80</startY>
		<endX>900</endX> <endY>80</endY>
		<backattack>3</backattack>
		<totalframe>55</totalframe>
		<stoptime>1</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem12>
	
	<infoitem13>
		<startX>1100</startX> <startY>10</startY>
		<endX>800</endX> <endY>10</endY>
		<backattack>2</backattack>  
		<totalframe>50</totalframe>
		<stoptime>2</stoptime>
		<LightRange x="50" y="-25" z="-1" xRange="100" yRange="50" zRange="100" />
	</infoitem13>
	
	
	
	

	<xiaoBing1>
		<!--敌人数据 -->
		<enemyAttackData>
			<!--单位毫秒-->
			<data att="unableAttackMinInterval" value="5000" />
			<data att="unableAttackMaxInterval" value="10000" />
		</enemyAttackData>
		<enemyData>
			<data att="totalHp" value="7500000" />
			<data att="attack" value="250000" />
			<data att="expOfDieThisEnemy" value="40000" />
			<data att="defence" value="2000" />
			<data att="dogdeRate" value="0.05" />
			<data att="criticalRate" value="0.2" />
			<data att="criticalMuti" value="1" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.09" />
		</enemyData>
		<!--移动速度以秒为单位 -->
		<animal id="enemy1" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="2000" bodyWidth="60" bodyHeight="130" walkSpeed="40"
			runSpeed="80">
			<attackRange x="0" y="-25" z="-1" xRange="100" yRange="100"
				zRange="100" />
			<notBePushed>true</notBePushed><!--不能被推 -->
			<idle defId="idle_enemy" />
			<walk defId="walk_enemy" />
			<run defId="run_enemy" />
			<attack defId="attack_enemy" />
			<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
			</hurt>
			<hurt2 defId="hurt2_enemy" />
			<die defId="die_enemy" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow" />
			<sound>
				<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
					className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>
			<skill id="Skill_show" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill3Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<animationDefinitions>
				<animationDefinition id="skill3Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/tideng.swf"
						showClass="tideng_say" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="idle_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/tideng.swf"
						showClass="tideng_idle" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/tideng.swf"
						showClass="tideng_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
	            <animationDefinition id="run_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/tideng.swf"
						showClass="tideng_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/tideng.swf"
						showClass="tideng_beattack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt2_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/tideng.swf"
						showClass="tideng_beattack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="2"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/tideng.swf"
						showClass="tideng_attack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/tideng.swf"
						showClass="tideng_die" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="enemyFootShadow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</xiaoBing1>
	

	<boss1>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="9000000" />
			<data att="attack" value="15000" />
			<data att="expOfDieThisEnemy" value="110000" />
			<data att="defence" value="100" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="0.5" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<animal id="boss21" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
			runSpeed="200">
			<attackRange x="0" y="0" z="0" xRange="0" yRange="0"
				zRange="0" />
			<notBePushed>true</notBePushed><!--不能被推 -->
			<idle defId="walk_boss21" />
			<walk defId="walk_boss21" />
			<run defId="run_boss21" />
			<attack defId="attack_boss21" />
			<hurt defId="hurt_boss21" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>
			<die defId="die_boss21" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />
	        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			<skill id="Skill_Shoot" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="false"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation1" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillshoutEnd^stop^"
				 bodySkillEndFrameLabel="skillshoutEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<sound>
				<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
					className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>
			<animationDefinitions>
				<animationDefinition id="skillAnimation1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/xunshan.swf"
						showClass="xunshan_shout" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="idle_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/xunshan.swf"
						showClass="xunshan_idle" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/xunshan.swf"
						showClass="xunshan_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/xunshan.swf"
						showClass="xunshan_hurt" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/xunshan.swf"
						showClass="xunshan_shout" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode3/xunshan.swf"
						showClass="xunshan_die" x_offset="0" y_offset="0" />
				</animationDefinition>


				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</boss1>


<boss4 type="YJFY.BossMode.Boss1.Boss1">
	<!--敌人数据 -->
	<!--技能伤害hurtMulti=X倍数*attack-->
   <bossData hpSegment="100000"> <!--用于血条显示，一条的容量-->
		<skill skillId="Skill_BossTeleport" hurtMulti="0" costMp="20" cdTime="5000" priorityForRun="0" priorityForRunInHurt="1"  
	      isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIActiveSkillVO" /> <!--cdTime 毫秒-->
		<!-- <skill skillId="Skill_BossSkill1" hurtMulti="0" costMp="20" cdTime="10000" priorityForRun="1" priorityForRunInHurt="0"  
	   isInvincibleInRun="0" isAbleRunInHurt="0" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO" /> --> <!--cdTime 毫秒-->
		<skill skillId="Skill_BossDaZhao" hurtMulti="1" costMp="30" cdTime="3000" priorityForRun="2" priorityForRunInHurt="0"  
	  isInvincibleInRun="1" isAbleRunInHurt="1" className="YJFY.XydzjsData.AISkillVO.AIAttackSkillVO"/> <!--cdTime 毫秒-->
		
		</bossData>
		
    <!--BOSS属性-->		
	<enemyData>
		<data att="totalHp" value="4800000" />
		<data att="attack" value="16000" />
		<data att="expOfDieThisEnemy" value="1200000" />
		<data att="defence" value="20" />
		<data att="dogdeRate" value="0.04" />
		<data att="criticalRate" value="1" />
		<data att="criticalMuti" value="1" />
		<data att="deCriticalRate" value="0.8" />
		<data att="hitRate" value="0.5" />
		
		
		<data att="totalMp" value="200" />
		
     <!--回血回魔-->	
		<data att="regHpPerS" value="0" />
		<data att="regMpPerS" value="5" />
	</enemyData>
	
	<animal id="chanchuking" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="5000" bodyWidth="100" bodyHeight="190" walkSpeed="60"
		runSpeed="200">
		<attackRange x="-20" y="-10" z="-1" xRange="200" yRange="20" zRange="100" />

		<idle defId="idle_boss" />
		<walk defId="walk_boss" />
		<run defId="run_boss" />
		<attack defId="attack_boss" />
		<hurt defId="hurt_boss" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>

		<die defId="die_boss" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="bossFootShadow1" />
		
       
	    <skill id="Skill_BossTeleport" className="YJFY.BossMode.Boss1.Skill_BossTeleport"  disappearBodyId="disappearAnimation" 
		appearBodyId="appearAnimation">
			
		</skill>
		<!-- <skill id="Skill_BossSkill1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" x="0"
			y="-15" z="-1" xRange="350" yRange="30" zRange="100" bodyDefId="skill1Animation"  hurtDuration="1000"  
			skillAttackEffectDefId="bossSkill1AttackEffect" randomPlaceXRange="10" randomPlaceYRange="-60" attackInterval="400"
			skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^" >
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
		</skill> -->
<!-- 		<skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_ZengZhangDaZhao" x="-200"
			y="-100" z="-1" xRange="400" yRange="200" zRange="100" bodyDefId="skill3Animation"  hurtDuration="5000" 
			bodyAttackReachFrameLabel="skillReach" bodySkillEndFrameLabel="skillEnd^stop^"
			everyEntityAddShowIsFrontOfBody="0">
		</skill> -->
		
		<skill id="Skill_BossDaZhao" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-200" y="-100" z="-1" xRange="400" yRange="200" zRange="100" attackInterval="500"
				bodyDefId="skill3Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>


		<animationDefinitions>
			<animationDefinition id="idle_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_idle" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_run" x_offset="0" y_offset="0" />
			</animationDefinition>
			 <animationDefinition id="run_boss" rows="1" cols="1" walkable="false" 
				overlap="false" frameInterval="1" 
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition"> 
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf" 
				showClass="chanchu_run" x_offset="0" y_offset="0" /> </animationDefinition> 
			<animationDefinition id="hurt_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_hurt" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_attack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_boss" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_die" x_offset="0" y_offset="0" />
			</animationDefinition>


			
			<!-- <animationDefinition id="skill1Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="SkillNormalWaitEffect" x_offset="0" y_offset="0" />
			</animationDefinition> -->
			<animationDefinition id="skill3Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_skill_2" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="disappearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_skill_3_1" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="appearAnimation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_skill_3_2" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossFootShadow1" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/chanchu.swf"
					showClass="chanchu_yingzi" x_offset="0" y_offset="0" />
			</animationDefinition>
			
			<animationDefinition id="bossSkill1AttackEffect"
				rows="1" cols="1" walkable="false" overlap="false" frameInterval="2"
				funFrame="3"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/SharedSource.swf"
					showClass="SharedEffect" x_offset="0" y_offset="-20" />
			</animationDefinition>
		<!-- 	<animationDefinition id="yunQuan" rows="1" cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/BossMode/Boss10.swf"
					showClass="YunQuan" x_offset="0" y_offset="0" />
			</animationDefinition> -->
			
		</animationDefinitions>



	</animal>
</boss4>

<xiaoBingAdd enemyClass="YJFY.LevelMode2.XiaoBing">
	<hpShowData frameLabel="smallShrimp" />
	<hurtAnimation2 defId="hurt2_enemy" playFrameLabel="1" recoverFrameLabel="recover^stop^" />
	<enemyAttackData>
		<!--单位毫秒-->
		<data att="unableAttackMinInterval" value="500" />
		<data att="unableAttackMaxInterval" value="1000" />
	</enemyAttackData>
	<enemyData>
		<data att="totalHp" value="8500000" />
		<data att="attack" value="50000" />
		<data att="expOfDieThisEnemy" value="40000" />
		<data att="defence" value="20" />
		<data att="dogdeRate" value="0.05" />
		<data att="criticalRate" value="0.2" />
		<data att="criticalMuti" value="1" />
		<data att="deCriticalRate" value="0.3" />
		<data att="hitRate" value="0.8" />
	</enemyData>
	<!--移动速度以秒为单位 -->
	<animal id="chanchu_gongjixiaoguai" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
		delayDisappear="2000" bodyWidth="60" bodyHeight="120" walkSpeed="80"
		runSpeed="180">
		<attackRange x="0" y="-25" z="-1" xRange="100" yRange="100"
			zRange="100" />
			
			
			
		<notShowBeattack>true</notShowBeattack><!-- 不播放被攻击展示 -->
			<notBePushed>true</notBePushed><!--不能被推 -->	
			
		<idle defId="idle_enemy" />
		<walk defId="walk_enemy" />
		<run defId="run_enemy" />
		<attack defId="attack_enemy" />
		<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
		</hurt>
		<hurt2 defId="hurt2_enemy" />
		<die defId="die_enemy" soundId="enemyDeadSound1">
			<attackSourceData entityId="" skillId="" />
		</die>
		<shadow defId="enemyFootShadow" />
		<sound>
			<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
				className="BeAttackSound1" />
			<sound id="enemyDeadSound1" name="enemyDeadSound1"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
				className="EnemyDeadSound" />
		</sound>
		<skill id="Skill_show" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill3Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
		</skill>
		<animationDefinitions>
			<animationDefinition id="skill3Animation" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/gongjixiaoguai.swf"
					showClass="gongji_show" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="idle_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/gongjixiaoguai.swf"
					showClass="gongji_idle" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="walk_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/gongjixiaoguai.swf"
					showClass="gongji_walk" x_offset="0" y_offset="0" />
			</animationDefinition>
            <animationDefinition id="run_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/gongjixiaoguai.swf"
					showClass="gongji_run" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/gongjixiaoguai.swf"
					showClass="gongji_beattack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="hurt2_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/gongjixiaoguai.swf"
					showClass="gongji_down" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="attack_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="2"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/gongjixiaoguai.swf"
					showClass="gongji_attack" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="die_enemy" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="1"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode3/gongjixiaoguai.swf"
					showClass="gongji_die" x_offset="0" y_offset="0" />
			</animationDefinition>
			<animationDefinition id="enemyFootShadow" rows="1"
				cols="1" walkable="false" overlap="false" frameInterval="20"
				defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
				<show swfPath="NewGameFolder/LevelMode2/Level1/SmallShrimp.swf"
					showClass="ShadowOfSmallShrimp" x_offset="0" y_offset="0" />
			</animationDefinition>
		</animationDefinitions>
	</animal>
</xiaoBingAdd>
</data>
