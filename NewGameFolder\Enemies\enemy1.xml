<?xml version="1.0" encoding="utf-8" ?>

<Animal id="enemy1"  enemyType="YJFY.Map.Animal.Enemy.LongDistanceSoldier" delayDisappear="5000" > 
	  <bloodChangeBarData id="changerBar2" xml="YJFY/changeBar/CMSXChangeBar/CMSXChangeBarData2.xml" x_offset="-20"  y_offset="-60" />
	  <attackSound swf="YJFY/sound.swf" className="ShootSound2" />
	  
	  <data>
		 <att="hp" value="23000" />
		 <att="attack" value="1500" />
		 <att="speed" value="10" />
		 <att="exp" value="1200" />
		 <att="defence" value="20" />
		 <att="dogdeRate" value="0.03" />
		 <att="deCriticalRate" value="0.3" />
		 <att="hitRate" value="0.09" />
		 <att="attackRange" value="50" />
	  </data>
	  
	   
	  <animationDefinitions>
		  <animationDefinition  id="spider1Walking" rows="1" cols="1" walkable="false" overlap="false"  frameInterval="1"  funFrame="3" 
		    defClassName="">
             <show swf="YJFY/Spider1Big.swf" showClass="Spider1WalkingBig" x_offset="0" y_offset="0" />
	     </animationDefinition>
	  </animationDefinitions>
</Animal>


