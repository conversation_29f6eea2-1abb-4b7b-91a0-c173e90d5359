<?xml version="1.0" encoding="utf-8" ?>
<data id="level1" swfPath="NewGameFolder/LevelMode4/anhei.swf"
	className="LevelMap" x="0" y="0" z="0" xRange="1920" yRange="400"
	zRange="100">
	<mainGameProjectLayerData sx="0" sy="0" sz="100"
		name="middleMap">
	</mainGameProjectLayerData>
	<!-- <projectLayer sx="300" sy="-350" sz="60"  name="tangsengmc" /> --> <!-- 被煮唐僧的动画位置 -->
	<!-- <projectLayer sx="1500" sy="-300" sz="60"  name="lightmc" /> -->
	<!-- 第三屏兵器的移动范围 -->
	<lightinfo startx="1500" starty="350" startz="0" xrange1="1200" yrange1="150" xrange2="1800" yrange2="350" totalframe="50" stoptime="2" />
	<!-- 第三屏兵器的光照范围 -->
	<LightBossRange x="-200" y="-25" z="-50" xRange="400" yRange="100" zRange="150" />
	<deshp>50000</deshp><!-- 蛇攻击力 -->
	<hpSegment>100000</hpSegment>
	<bosslasthp>0</bosslasthp><!-- 当boss血量到这个值的时候开始倒计时 -->
	<!--需要触发倒计时的全屏技能的id-->
	<screenskills>
		<screenskill>10101000</screenskill>
		<screenskill>10101100</screenskill>
		<screenskill>10101200</screenskill>
		<screenskill>10101300</screenskill>
		
		<screenskill>10102004</screenskill>
		<screenskill>10102104</screenskill>
		<screenskill>10102204</screenskill>
		<screenskill>10102304</screenskill>
		
		<screenskill>10104004</screenskill>
		<screenskill>10104104</screenskill>
		<screenskill>10104204</screenskill>
		<screenskill>10104304</screenskill>
		
		<screenskill>10105004</screenskill>
		<screenskill>10105104</screenskill>
		<screenskill>10105204</screenskill>
		<screenskill>10105304</screenskill>
		
		<screenskill>10106004</screenskill>
		<screenskill>10106104</screenskill>
		<screenskill>10106204</screenskill>
		<screenskill>10106304</screenskill>
		
		<screenskill>10107004</screenskill>
		<screenskill>10107104</screenskill>
		<screenskill>10107204</screenskill>
		<screenskill>10107304</screenskill>

		<screenskill>10103118</screenskill>
		<screenskill>10103218</screenskill>
		<screenskill>10103318</screenskill>
		<screenskill>10103119</screenskill>

		<screenskill>10109004</screenskill>
		<screenskill>10109104</screenskill>
		<screenskill>10109204</screenskill>
		<screenskill>10109304</screenskill>
	</screenskills>
	<lighttime>5</lighttime>
	<blacktime>8</blacktime>
	<snake>2</snake>
	<backgroundMusic id="LevelMusic" name="LevelMusic"
				swfPath="NewGameFolder/GuardingTangSengLevelMode/LevelSounds/Sound7.swf" className="SoundQ" />	
	<startShow swfPath="NewGameFolder/LevelMode4/StartShow5.swf" className="StartShow" />
	<Waves totalWaveNum="1">
		<!--waveCount enemyNum 用于显示用, time 产生的世界时间 -->
		<!-- 第一屏 -->
		<Wave waveCount="1" totalEnemyNum="7" x="200" y="200" xRange="0" yRange="0" time="30" >
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem1" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem3" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem4" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem5" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem6" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem7" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatBig" xmlPath="boss1" localpos="xunshan" startTime="0" duration="0" num="1"  />
		</Wave>
		<!-- 第二屏 -->
		<Wave waveCount="2" totalEnemyNum="6" x="200" y="200" xRange="0" yRange="0" time="30" >
			
<!-- 		<Enemy enemyClass="YJFY.LevelMode2.XunShan" xmlPath="boss1" localpos="xunshan1" startTime="0" duration="0" num="1"  />
		<Enemy enemyClass="YJFY.LevelMode2.XunShan" xmlPath="boss1" localpos="xunshan2" startTime="0" duration="0" num="1"  /> -->
		
				<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem8" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem9" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem10" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem11" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem12" startTime="0" duration="0" num="1"  />
			<Enemy enemyClass="YJFY.LevelMode4.BatSmall" xmlPath="xiaoBing1" localpos="infoitem13" startTime="0" duration="0" num="1"  />
			
		</Wave>
		<!-- 第三屏 -->
		<Wave waveCount="3" totalEnemyNum="1" x="200" y="200" xRange="0" yRange="0" time="30" >
			<Enemy enemyClass="YJFY.LevelMode4.BatBoss" xmlPath="batbossxml" startTime="0" duration="0" num="1"  />
		</Wave>
	</Waves>
    <!--抽奖配置-->
	<lottery>
		<!-- 只能12个-->
	
       
       
     
			 <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
        <!--   恶魔灵符碎片 -->
        <item id="10500156" num="1" proWeight="3" />
		   <!-- 幸运宝石-->
        <item id="10500000" num="3" proWeight="10" />
        <!-- 恶魔灵符模具 -->
        <item id="12100132" num="1" proWeight="2" />
		 <!-- 幸运宝石 -->
        <item id="10500000" num="2" proWeight="15" />
        <!-- 灵符洗炼石 -->
        <item id="11000030" num="1" proWeight="2" />
		 <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
        <!--   恶魔灵符碎片 -->
        <item id="10500156" num="2" proWeight="3" />
			 <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
        <!-- 幽冥宝珠紫-->
        <item id="10500124" num="1" proWeight="3" />
			 <!-- 幸运宝石-->
        <item id="10500000" num="1" proWeight="10" />
        <!--   恶魔灵符碎片 -->
        <item id="10500156" num="1" proWeight="3" />
		
		
		
		
	</lottery>
    <EqDrop>
	    <xiaoBing noDropProWeight="500">
		   <!--proWeight 概率权重-->
	    
  	      <!--  红药 -->   
   	      <item dropClassName="Item_HpUp" proWeight="5" />
   	      <!--  金币 -->  
   	      <item dropClassName="Item_MoneyUp" proWeight="5" />
   	      <!--  蓝药 -->  
   	      <item dropClassName="Item_MpUp" proWeight="10" /> 
		  
		  	      <!-- 灵符碎片-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Lingfusuipian2_S" proWeight="10" />	
   	  
		  <!-- 灵符模具-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_lingfu_emo_S" proWeight="5" />	
   	  
   	      <!-- 幸运宝石 -->
   	      <item dropClassName="UI.Equipments.SceneEquipments.LuckStone_S" proWeight="5" />
   	   
	  	</xiaoBing>
	    <boss noDropProWeight="1">
		   <!--proWeight 概率权重-->
		   <dropNumData>
			   <smallDropNumData proWeight="10" >
				   <numData num="1" proWeight="5" />
				   <numData num="2" proweight="5" />
			   </smallDropNumData>
			   <bigDropNumData proWeight="1">
				   <numData num="4" proWeight="8" />
				   <numData num="5" proWeight="2" />
			   </bigDropNumData>
		   </dropNumData>		  
	      <!-- 灵符碎片-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Material_Lingfusuipian2_S" proWeight="200" />	
		  
		 
   	  
		  <!-- 灵符模具-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.Scroll_lingfu_emo_S" proWeight="30" />	
   
		  <!-- 灵符模具-->
   	      <item dropClassName="UI.Equipments.SceneEquipments.precious_emo_S" proWeight="5" />	  
		  
   	   
		  </boss>
   	</EqDrop>
	<!--暂时放在这，以后可能要移动其他地方 -->
	<sharedAnimationDefinitions>
	</sharedAnimationDefinitions>

	<xunshan>
		<startX>800</startX> <startY>400</startY>
		<arragex>100</arragex> <arragey>100</arragey>
		<arragex2>900</arragex2>
		<arragey2>400</arragey2>
		<totalFrame>30</totalFrame>
		<stoptime>5</stoptime>
		<LightRange x="-200" y="-100" z="-50" xRange="400" yRange="200" zRange="150" />
	</xunshan>

	<xunshan1>
		<startX>700</startX> <startY>400</startY>
		<arragex>1400</arragex> <arragey>100</arragey>
		<arragex2>900</arragex2>
		<arragey2>400</arragey2>
		<totalFrame>35</totalFrame>
		<stoptime>3</stoptime>
		<LightRange x="-200" y="-100" z="-50" xRange="400" yRange="200" zRange="150" />
	</xunshan1>
	
	<xunshan2>
		<startX>1400</startX> <startY>100</startY>
		<arragex>700</arragex> <arragey>400</arragey>
		<arragex2>900</arragex2>
		<arragey2>200</arragey2>
		<totalFrame>35</totalFrame>
		<stoptime>3</stoptime>
		<LightRange x="-200" y="-100" z="-50" xRange="400" yRange="200" zRange="150" />
	</xunshan2>

	<infoitem1>
		<startX>500</startX>  <startY>400</startY>
		<endX>250</endX>  <endY>400</endY>
		<backattack>2</backattack>
		<totalframe>50</totalframe>
		<stoptime>3</stoptime>
		<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem1>

	<infoitem2>
		<startX>800</startX>
		<startY>400</startY>
		<arragex>200</arragex>
		<arragey>150</arragey>
		<arragex2>900</arragex2>
		<arragey2>300</arragey2>
		<totalFrame>30</totalFrame>
		<stoptime>5</stoptime>
	<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem2>

	<infoitem3>
		<startX>300</startX> <startY>320</startY>
		<endX>800</endX> <endY>320</endY>
		<backattack>2</backattack>
		<totalframe>40</totalframe>
		<stoptime>2</stoptime>
		<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem3>

	<infoitem4>
		<startX>700</startX> <startY>240</startY>
		<endX>250</endX> <endY>240</endY>
		<backattack>3</backattack>
		<totalframe>45</totalframe>
		<stoptime>0</stoptime>
	<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem4>
	
	<infoitem5>
		<startX>800</startX> <startY>160</startY>
		<endX>300</endX> <endY>160</endY>
		<backattack>2</backattack>
		<totalframe>35</totalframe>
		<stoptime>0</stoptime>
		<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem5>
	
	<infoitem6>
		<startX>200</startX> <startY>80</startY>
		<endX>700</endX> <endY>80</endY>
		<backattack>3</backattack>
		<totalframe>55</totalframe>
		<stoptime>1</stoptime>
	<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem6>
	
	<infoitem7>
		<startX>600</startX> <startY>10</startY>
		<endX>300</endX> <endY>10</endY>
		<backattack>2</backattack>
		<totalframe>45</totalframe>
		<stoptime>2</stoptime>
		<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem7>
	
	
	
	<infoitem8>
		<startX>1000</startX>  <startY>400</startY>
		<endX>700</endX>  <endY>400</endY>
		<backattack>3</backattack>
		<totalframe>50</totalframe>
		<stoptime>3</stoptime>
		<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem8>



	<infoitem9>
		<startX>800</startX> <startY>320</startY>
		<endX>1300</endX> <endY>320</endY>
		<backattack>2</backattack>
		<totalframe>60</totalframe>
		<stoptime>2</stoptime>
		<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem9>

	<infoitem10>
		<startX>1200</startX> <startY>240</startY>
		<endX>700</endX> <endY>240</endY>
		<backattack>3</backattack>
		<totalframe>50</totalframe>
		<stoptime>0</stoptime>
	<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem10>
	
	<infoitem11>
		<startX>1300</startX> <startY>160</startY>
		<endX>800</endX> <endY>160</endY>
		<backattack>2</backattack>
		<totalframe>60</totalframe>
		<stoptime>0</stoptime>
		<LightRange x="-100" y="-50" z="-50" xRange="200" yRange="100" zRange="150" />
	</infoitem11>
	
	<infoitem12>
		<startX>1400</startX> <startY>80</startY>
		<endX>900</endX> <endY>80</endY>
		<backattack>3</backattack>
		<totalframe>55</totalframe>
		<stoptime>1</stoptime>
	<LightRange x="-60" y="-50" z="-50" xRange="120" yRange="100" zRange="150" />
	</infoitem12>
	
	<infoitem13>
		<startX>1100</startX> <startY>10</startY>
		<endX>800</endX> <endY>10</endY>
		<backattack>2</backattack>  
		<totalframe>50</totalframe>
		<stoptime>2</stoptime>
		<LightRange x="-100" y="-50" z="-50" xRange="200" yRange="100" zRange="150" />
	</infoitem13>
	
	
	
	

	<xiaoBing1>
		<!--敌人数据 -->
		<enemyAttackData>
			<!--单位毫秒-->
			<data att="unableAttackMinInterval" value="5000" />
			<data att="unableAttackMaxInterval" value="10000" />
		</enemyAttackData>
		<enemyData>
			<data att="totalHp" value="7500000" />
			<data att="attack" value="10000" />
			<data att="expOfDieThisEnemy" value="40000" />
			<data att="defence" value="2000" />
			<data att="dogdeRate" value="0.05" />
			<data att="criticalRate" value="0.2" />
			<data att="criticalMuti" value="1" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.09" />
		</enemyData>
		<skilltime value="5"></skilltime>
		<!--移动速度以秒为单位 -->
		<animal id="enemy1" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="2000" bodyWidth="60" bodyHeight="130" walkSpeed="40"
			runSpeed="80">
			<attackRange x="0" y="-25" z="-1" xRange="100" yRange="100"
				zRange="100" />
			<notBePushed>true</notBePushed><!--不能被推 -->
			<idle defId="idle_enemy" />
			<walk defId="walk_enemy" />
			<run defId="run_enemy" />
			<attack defId="attack_enemy" />
			<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
			</hurt>
			<hurt2 defId="hurt2_enemy" />
			<die defId="die_enemy" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow" />
			<sound>
				<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
					className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>
			<skill id="Skill_show" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skill3Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="Skill_Shoot" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="false"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation4" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillshoutEnd^stop^"
				 bodySkillEndFrameLabel="skillshoutEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<animationDefinitions>
				<animationDefinition id="skill3Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_skill1" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation4" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_skill2" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="idle_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
	            <animationDefinition id="run_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_beattack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt2_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_beattack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="2"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_attack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batbig_die" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="enemyFootShadow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</xiaoBing1>
	

	<boss1>
		<!--敌人数据 -->
		<enemyData>
			<data att="totalHp" value="9000000" />
			<data att="attack" value="15000" />
			<data att="expOfDieThisEnemy" value="110000" />
			<data att="defence" value="100" />
			<data att="dogdeRate" value="0.04" />
			<data att="criticalRate" value="0.5" />
			<data att="criticalMuti" value="3" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.25" />
		</enemyData>
		<skilltime value="5"></skilltime>
		<animal id="boss21" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="5000" bodyWidth="120" bodyHeight="160" walkSpeed="22"
			runSpeed="200">
			<attackRange x="0" y="0" z="0" xRange="0" yRange="0"
				zRange="0" />
			<notBePushed>true</notBePushed><!--不能被推 -->
			<idle defId="walk_boss21" />
			<walk defId="walk_boss21" />
			<run defId="run_boss21" />
			<attack defId="attack_boss21" />
			<hurt defId="hurt_boss21" soundId="beAttackSound1">
				<attackSourceData entityId="" skillId="" />
			</hurt>
			<die defId="die_boss21" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="bossFootShadow1" />
	        <skill id="skill_BossInvincibleSkill" className="YJFY.Skill.BossSkills.Skill_BossInvincibleSkill"  superRotateId="superRotate" >
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</skill>
			<skill id="Skill_Shoot" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="false"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation1" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillshoutEnd^stop^"
				 bodySkillEndFrameLabel="skillshoutEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="Skill_Shengbo" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="false"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" attackInterval="500"
				bodyDefId="skillAnimation2" hurtDuration="2500" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skillEnd^stop^"
				 bodySkillEndFrameLabel="skillEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<sound>
				<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf"
					className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1"
					swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>
			<animationDefinitions>
				<animationDefinition id="skillAnimation1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batsmall_skill2" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skillAnimation2" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batsmall_skill1" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="idle_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batsmall_idle" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batsmall_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batsmall_beattack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="3"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batsmall_attack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_boss21" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batsmall.swf"
						showClass="batsmall_die" x_offset="0" y_offset="0" />
				</animationDefinition>


				<animationDefinition id="bossFootShadow1" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="superRotate" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.MovieClipEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="BossEffect_SuperRotate" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</boss1>
	<batbossxml>
		<!--敌人数据 -->
		<enemyAttackData>
			<!--单位毫秒-->
			<data att="unableAttackMinInterval" value="5000" />
			<data att="unableAttackMaxInterval" value="10000" />
		</enemyAttackData>
		<startX>1600</startX> 
		<startY>80</startY>
		<hpline>500000</hpline>
		<skill3CD>15</skill3CD><!-- 突袭 -->
		<skill2CD>8</skill2CD><!-- 连击 -->
		<skill1CD>10</skill1CD><!-- 毒雾 -->
		
		<enemyData>
			<data att="totalHp" value="7500000" />
			<data att="attack" value="15000" />
			<data att="expOfDieThisEnemy" value="40000" />
			<data att="defence" value="200" />
			<data att="dogdeRate" value="0.05" />
			<data att="criticalRate" value="0.2" />
			<data att="criticalMuti" value="1" />
			<data att="deCriticalRate" value="0.3" />
			<data att="hitRate" value="0.2" />
		</enemyData>
		<skillInvincible>true</skillInvincible>
		<notBePushed>true</notBePushed><!--不能被推 -->
		
		
		<!--移动速度以秒为单位 -->
		<animal id="batboss" animalType="YJFY.Entity.EnemyEntity.EnemyEntity"
			delayDisappear="2000" bodyWidth="60" bodyHeight="130" walkSpeed="40"
			runSpeed="80">
			<attackRange x="0" y="-25" z="-1" xRange="100" yRange="100"
				zRange="100" />
			<notBePushed>true</notBePushed><!--不能被推 -->
			
			<idle defId="idle_enemy" />
			<walk defId="walk_enemy" />
			<run defId="run_enemy" />
			<attack defId="attack_enemy" />
			<hurt defId="hurt_enemy" soundId="beAttackSound1">
			<attackSourceData entityId="" skillId="" />
			</hurt>
			<hurt2 defId="hurt2_enemy" />
			<die defId="die_enemy" soundId="enemyDeadSound1">
				<attackSourceData entityId="" skillId="" />
			</die>
			<shadow defId="enemyFootShadow" />
			<sound>
				<sound id="beAttackSound1" name="beAttackSound1" swfPath="NewGameFolder/SharedSound.swf" className="BeAttackSound1" />
				<sound id="enemyDeadSound1" name="enemyDeadSound1" swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
					className="EnemyDeadSound" />
			</sound>
			<skill id="skill_1" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-200" y="-100" z="-1" xRange="400" yRange="200" zRange="100" 
				bodyDefId="skill1Animation" hurtDuration="1000" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skilduwulEnd^stop^"
				 bodySkillEndFrameLabel="skilduwulEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="skill_2" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-200" y="-100" z="-1" xRange="400" yRange="200" zRange="100" 
				bodyDefId="skill2Animation" hurtDuration="1000" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skilllianjiEnd^stop^"
				 bodySkillEndFrameLabel="skilllianjiEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="skill_3" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="-200" y="-100" z="-1" xRange="400" yRange="200" zRange="100" 
				bodyDefId="skill3Animation" hurtDuration="1000" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skilltuxiEnd^stop^"
				 bodySkillEndFrameLabel="skilltuxiEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="skill_4" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" 
				bodyDefId="skill4Animation" hurtDuration="0" skillAttackReachFrameLabel="skillReach" skillEndFrameLabel="skill4xiaoshiEnd^stop^"
				 bodySkillEndFrameLabel="skill4xiaoshiEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
			<skill id="skill_5" className="YJFY.Skill.BossSkills.Skill_NanTaSkill" dontAttacktangsheng="true"
				x="0" y="0" z="0" xRange="0" yRange="0" zRange="0" 
				bodyDefId="skill5Animation" hurtDuration="0" skillAttackReachFrameLabel="skillhangReach" skillEndFrameLabel="skillhangEnd^stop^"
				 bodySkillEndFrameLabel="skillhangEnd^stop^" randomPlaceXRange="10" randomPlaceYRange="10">
			</skill>
		
			<animationDefinitions>
				<animationDefinition id="skill1Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_skill1" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skill2Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_skill2" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skill3Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_skill3" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skill4Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_skill4" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="skill5Animation" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_skill5" x_offset="0" y_offset="0" />
				</animationDefinition>
		
				<animationDefinition id="idle_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="walk_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
	            <animationDefinition id="run_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_walk" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="hurt_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_beattack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="attack_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="2"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_attack" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="die_enemy" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="1"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/LevelMode4/batboss.swf"
						showClass="batboss_die" x_offset="0" y_offset="0" />
				</animationDefinition>
				<animationDefinition id="enemyFootShadow" rows="1"
					cols="1" walkable="false" overlap="false" frameInterval="20"
					defClassName="YJFY.EntityAnimation.BitmapDatasFromMCEntityAnimationDefinition">
					<show swfPath="NewGameFolder/GuardingTangSengLevelMode/SharedSource.swf"
						showClass="EnemyFootShadow" x_offset="0" y_offset="0" />
				</animationDefinition>
			</animationDefinitions>
		</animal>
	</batbossxml>
</data>
